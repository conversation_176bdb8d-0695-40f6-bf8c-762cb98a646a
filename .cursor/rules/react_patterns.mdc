# React前端开发模式

基于A8Tools项目的React 19 + Material-UI v7前端架构模式和最佳实践。

## 📁 前端目录组织

### 标准目录结构
```
frontend/src/
├── components/          # 可复用组件 (50+模块化组件)
│   ├── table/          # 表格组件系列
│   ├── custom-dialog/  # 对话框组件
│   ├── hook-form/      # 表单组件
│   ├── iconify/        # 图标组件
│   └── */              # 其他功能组件
├── pages/              # 页面组件
│   ├── account/        # 账户页面
│   ├── wallet/         # 钱包页面
│   └── */              # 其他页面
├── sections/           # 功能区块组件
│   ├── account/        # 账户功能区块
│   ├── wallet/         # 钱包功能区块
│   └── */              # 其他功能区块
├── layouts/            # 布局组件
│   ├── dashboard/      # 仪表板布局
│   ├── auth-split/     # 认证分屏布局
│   └── */              # 其他布局
├── hooks/              # 自定义Hook
├── theme/              # Material-UI主题
├── locales/            # 国际化资源
└── lib/                # 工具库
    ├── wallet/         # 钱包工具
    └── tools/          # 通用工具
```

## 🧩 组件设计模式

### 函数组件模式
```jsx
// ✅ DO: 使用函数组件和React 19特性
import { useState, useEffect, useCallback, useMemo } from 'react';
import { Box, Card, Typography } from '@mui/material';

import { useTranslate } from 'src/locales';

export function WalletCard({ wallet, onEdit, onDelete, ...other }) {
  const { t } = useTranslate('wallet');
  const [loading, setLoading] = useState(false);

  // ✅ DO: 使用useCallback优化事件处理函数
  const handleEdit = useCallback(() => {
    if (onEdit) {
      onEdit(wallet.id);
    }
  }, [onEdit, wallet.id]);

  // ✅ DO: 使用useMemo优化计算值
  const displayName = useMemo(() => {
    return wallet.name || `${wallet.chain} Wallet`;
  }, [wallet.name, wallet.chain]);

  return (
    <Card sx={{ p: 2 }} {...other}>
      <Typography variant="h6">{displayName}</Typography>
      <Typography variant="body2" color="text.secondary">
        {wallet.chain}
      </Typography>
      {/* 其他内容 */}
    </Card>
  );
}

// ❌ DON'T: 不要使用类组件（除非必需）
class BadWalletCard extends Component {
  render() {
    // 避免使用类组件
  }
}
```

### Props模式
```jsx
// ✅ DO: 使用清晰的Props类型定义
export function TableView({
  columns,
  getData,
  onRowClick,
  enableSelection = true,
  onSelectionChange,
  onDelete,
  selectedActions = [],
  loading: externalLoading,
  noDataText,
  tableProps = {},
  enableStickyLastColumn = true,
  ...other
}) {
  // 组件实现
}

// ✅ DO: 使用默认参数
export function CustomButton({ 
  variant = 'contained',
  color = 'primary',
  size = 'medium',
  children,
  ...other 
}) {
  return (
    <Button variant={variant} color={color} size={size} {...other}>
      {children}
    </Button>
  );
}

// ✅ DO: 使用解构和剩余参数
export function IconButton({ icon, children, sx, ...other }) {
  return (
    <MuiIconButton sx={{ ...sx }} {...other}>
      {icon && <Iconify icon={icon} />}
      {children}
    </MuiIconButton>
  );
}
```

## 🎣 Hooks模式

### 自定义Hook设计
```jsx
// ✅ DO: 创建可复用的自定义Hook
export function useAppSettings() {
  const {
    state: settings,
    setState,
    setField,
  } = useLocalStorage(APP_SETTINGS_STORAGE_KEY, defaultAppSettings);

  const [hasPassword, setHasPassword] = useState(false);

  // ✅ DO: 使用useCallback优化函数
  const refreshPasswordStatus = useCallback(async () => {
    try {
      const result = await AppPasswordService.HasPassword(APP_PASSWORD_KEY);
      setHasPassword(result);
      return result;
    } catch (error) {
      console.error('检查密码状态失败:', error);
      setHasPassword(false);
      return false;
    }
  }, []);

  // ✅ DO: 使用useEffect处理副作用
  useEffect(() => {
    refreshPasswordStatus();
  }, [refreshPasswordStatus]);

  const setPassword = useCallback(async (password) => {
    if (!password || typeof password !== 'string' || password.trim() === '') {
      console.error('密码参数无效');
      return false;
    }

    try {
      await AppPasswordService.SetPassword(APP_PASSWORD_KEY, password);
      setHasPassword(true);
      return true;
    } catch (error) {
      console.error('设置密码失败:', error);
      await refreshPasswordStatus();
      return false;
    }
  }, [refreshPasswordStatus]);

  return {
    settings,
    hasPassword,
    setPassword,
    refreshPasswordStatus,
    getSetting: (key, defaultValue) => settings[key] ?? defaultValue,
    updateSetting: (key, value) => setField(key, value),
  };
}
```

### Context模式
```jsx
// ✅ DO: 使用Context提供全局状态
import { createContext, useContext, useReducer } from 'react';

const AppContext = createContext();

export function useAppContext() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within AppProvider');
  }
  return context;
}

export function AppProvider({ children }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  const value = useMemo(() => ({
    ...state,
    dispatch,
  }), [state]);

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
}

// ✅ DO: 为特定功能创建专门的Context
export function SettingsProvider({ defaultSettings, children }) {
  const [settings, setSettings] = useState(defaultSettings);

  const updateSettings = useCallback((newSettings) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  }, []);

  const value = useMemo(() => ({
    settings,
    updateSettings,
  }), [settings, updateSettings]);

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
}
```

## 🎨 样式和主题模式

### Material-UI styled组件
```jsx
// ✅ DO: 使用Material-UI的sx prop
import { Box, Card, styled } from '@mui/material';

export function StyledCard({ children, ...other }) {
  return (
    <Card
      sx={{
        p: 3,
        borderRadius: 2,
        boxShadow: (theme) => theme.shadows[3],
        '&:hover': {
          boxShadow: (theme) => theme.shadows[6],
        },
      }}
      {...other}
    >
      {children}
    </Card>
  );
}

// ✅ DO: 对于复杂样式使用styled组件
const StyledTableContainer = styled(Box)(({ theme }) => ({
  '& .MuiTableRow-root': {
    height: 55,
  },
  '& .MuiTableCell-root': {
    padding: '12px 16px',
  },
  '& .MuiTableHead-root': {
    backgroundColor: theme.palette.grey[50],
  },
}));

// ✅ DO: 响应式设计
export function ResponsiveBox({ children }) {
  return (
    <Box
      sx={{
        width: {
          xs: '100%',
          sm: '80%',
          md: '60%',
          lg: '50%',
        },
        p: {
          xs: 2,
          sm: 3,
          md: 4,
        },
      }}
    >
      {children}
    </Box>
  );
}
```

### 主题定制
```jsx
// ✅ DO: 扩展Material-UI主题
import { createTheme } from '@mui/material/styles';

export const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
      light: '#42a5f5',
      dark: '#1565c0',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 600,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
        },
      },
    },
  },
});
```

## 🧱 组件组合模式

### 渲染函数模式
```jsx
// ✅ DO: 使用渲染函数提供灵活性
export function DataTable({
  data,
  columns,
  renderRow,
  renderEmpty,
  renderLoading,
  ...other
}) {
  if (loading) {
    return renderLoading ? renderLoading() : <DefaultLoading />;
  }

  if (!data || data.length === 0) {
    return renderEmpty ? renderEmpty() : <DefaultEmpty />;
  }

  return (
    <Table {...other}>
      <TableBody>
        {data.map((row, index) => 
          renderRow ? renderRow(row, index) : <DefaultRow key={index} data={row} />
        )}
      </TableBody>
    </Table>
  );
}

// ✅ DO: 使用Children API
export function CardContainer({ children, title, action }) {
  return (
    <Card>
      {title && (
        <CardHeader 
          title={title}
          action={action}
        />
      )}
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );
}
```

### 高阶组件模式
```jsx
// ✅ DO: 使用高阶组件封装通用逻辑
export function withLoading(WrappedComponent) {
  return function WithLoadingComponent({ loading, ...props }) {
    if (loading) {
      return <LoadingSpinner />;
    }
    return <WrappedComponent {...props} />;
  };
}

// ✅ DO: 使用forwardRef传递ref
export const CustomInput = forwardRef(function CustomInput(
  { label, error, helperText, ...other },
  ref
) {
  return (
    <TextField
      ref={ref}
      label={label}
      error={!!error}
      helperText={error?.message || helperText}
      {...other}
    />
  );
});
```

## 🛣️ 路由和导航模式

### React Router v7模式
```jsx
// ✅ DO: 使用React Router v7的数据路由模式
import { createBrowserRouter, RouterProvider } from 'react-router';

const router = createBrowserRouter([
  {
    path: '/',
    element: <DashboardLayout />,
    children: [
      {
        index: true,
        element: <DashboardPage />,
      },
      {
        path: 'wallet',
        element: <WalletPage />,
        loader: walletLoader,
      },
      {
        path: 'account',
        children: [
          {
            path: 'email',
            element: <EmailAccountPage />,
          },
          {
            path: 'discord',
            element: <DiscordAccountPage />,
          },
        ],
      },
    ],
  },
]);

export function App() {
  return <RouterProvider router={router} />;
}

// ✅ DO: 使用路由守卫
export function ProtectedRoute({ children }) {
  const { isAuthenticated } = useAuth();
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return children;
}
```

### 导航Hook
```jsx
// ✅ DO: 创建导航相关的Hook
export function useNavigation() {
  const navigate = useNavigate();
  const location = useLocation();

  const goToWallet = useCallback((walletId) => {
    navigate(`/wallet/${walletId}`);
  }, [navigate]);

  const goBack = useCallback(() => {
    navigate(-1);
  }, [navigate]);

  const isCurrentPath = useCallback((path) => {
    return location.pathname === path;
  }, [location.pathname]);

  return {
    goToWallet,
    goBack,
    isCurrentPath,
    currentPath: location.pathname,
  };
}
```

## 📊 状态管理模式

### 本地状态管理
```jsx
// ✅ DO: 使用useState管理简单状态
export function WalletForm() {
  const [formData, setFormData] = useState({
    name: '',
    chain: '',
    mnemonic: '',
  });

  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  // ✅ DO: 使用useReducer管理复杂状态
  const [state, dispatch] = useReducer(formReducer, initialState);

  const handleSubmit = useCallback(async (event) => {
    event.preventDefault();
    setLoading(true);
    
    try {
      await createWallet(formData);
      // 成功处理
    } catch (error) {
      setErrors({ submit: error.message });
    } finally {
      setLoading(false);
    }
  }, [formData]);

  return (
    <form onSubmit={handleSubmit}>
      {/* 表单内容 */}
    </form>
  );
}
```

### 全局状态管理
```jsx
// ✅ DO: 使用SWR进行数据获取和缓存
import useSWR from 'swr';

export function useWallets() {
  const { data, error, mutate } = useSWR('/api/wallets', fetcher);

  const createWallet = useCallback(async (walletData) => {
    const newWallet = await api.createWallet(walletData);
    mutate([...data, newWallet], false);
    return newWallet;
  }, [data, mutate]);

  return {
    wallets: data,
    loading: !error && !data,
    error,
    createWallet,
    refresh: mutate,
  };
}

// ✅ DO: 使用Context进行设置管理
export function useSettings() {
  const context = useContext(SettingsContext);
  
  if (!context) {
    throw new Error('useSettings must be used within SettingsProvider');
  }
  
  return context;
}
```

## 🎭 性能优化模式

### Memoization模式
```jsx
// ✅ DO: 使用React.memo优化组件
export const WalletItem = memo(function WalletItem({ wallet, onEdit, onDelete }) {
  return (
    <Card>
      <Typography>{wallet.name}</Typography>
      <Button onClick={() => onEdit(wallet.id)}>Edit</Button>
      <Button onClick={() => onDelete(wallet.id)}>Delete</Button>
    </Card>
  );
});

// ✅ DO: 使用useMemo优化计算
export function WalletList({ wallets, searchTerm }) {
  const filteredWallets = useMemo(() => {
    if (!searchTerm) return wallets;
    return wallets.filter(wallet => 
      wallet.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [wallets, searchTerm]);

  return (
    <Box>
      {filteredWallets.map(wallet => (
        <WalletItem key={wallet.id} wallet={wallet} />
      ))}
    </Box>
  );
}

// ✅ DO: 使用useCallback优化事件处理
export function WalletManager() {
  const [wallets, setWallets] = useState([]);

  const handleDeleteWallet = useCallback((walletId) => {
    setWallets(prev => prev.filter(w => w.id !== walletId));
  }, []);

  const handleEditWallet = useCallback((walletId) => {
    // 编辑逻辑
  }, []);

  return (
    <Box>
      {wallets.map(wallet => (
        <WalletItem
          key={wallet.id}
          wallet={wallet}
          onEdit={handleEditWallet}
          onDelete={handleDeleteWallet}
        />
      ))}
    </Box>
  );
}
```

### 懒加载模式
```jsx
// ✅ DO: 使用React.lazy进行代码分割
import { lazy, Suspense } from 'react';

const WalletPage = lazy(() => import('src/pages/wallet'));
const AccountPage = lazy(() => import('src/pages/account'));

export function App() {
  return (
    <Suspense fallback={<LoadingScreen />}>
      <Routes>
        <Route path="/wallet" element={<WalletPage />} />
        <Route path="/account" element={<AccountPage />} />
      </Routes>
    </Suspense>
  );
}

// ✅ DO: 使用动态导入
export function useAsyncComponent() {
  const [Component, setComponent] = useState(null);

  useEffect(() => {
    import('./HeavyComponent').then(module => {
      setComponent(() => module.default);
    });
  }, []);

  return Component;
}
```

## 🌐 国际化模式

### i18next集成
```jsx
// ✅ DO: 使用useTranslate Hook
import { useTranslate } from 'src/locales';

export function WalletCard({ wallet }) {
  const { t } = useTranslate('wallet');

  return (
    <Card>
      <Typography variant="h6">
        {t('title')}
      </Typography>
      <Typography variant="body2">
        {t('chain')}: {wallet.chain}
      </Typography>
      <Button>
        {t('actions.edit')}
      </Button>
    </Card>
  );
}

// ✅ DO: 支持命名空间
export function AccountPage() {
  const { t } = useTranslate('account');
  const { t: tCommon } = useTranslate('common');

  return (
    <Box>
      <Typography variant="h4">
        {t('title')}
      </Typography>
      <Button>
        {tCommon('actions.save')}
      </Button>
    </Box>
  );
}

// ✅ DO: 使用翻译参数
export function WalletStats({ count }) {
  const { t } = useTranslate('wallet');

  return (
    <Typography>
      {t('stats.total', { count })}
    </Typography>
  );
}
```

## 🔗 Wails集成模式

### 服务调用
```jsx
// ✅ DO: 创建服务调用Hook
import { WalletService } from '@wailsio/runtime';

export function useWalletService() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const createWallet = useCallback(async (walletData) => {
    setLoading(true);
    setError(null);

    try {
      const result = await WalletService.CreateWallet(
        walletData.name,
        walletData.chain,
        walletData.mnemonic
      );
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const getWallets = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const wallets = await WalletService.GetWallets();
      return wallets;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    createWallet,
    getWallets,
    loading,
    error,
  };
}
```

### 错误处理
```jsx
// ✅ DO: 统一错误处理
export function useErrorHandler() {
  const { enqueueSnackbar } = useSnackbar();

  const handleError = useCallback((error, context = '') => {
    console.error(`错误${context ? ` (${context})` : ''}:`, error);
    
    const message = error?.message || '操作失败';
    enqueueSnackbar(message, { variant: 'error' });
  }, [enqueueSnackbar]);

  return { handleError };
}

// ✅ DO: 在组件中使用错误处理
export function WalletManager() {
  const { createWallet } = useWalletService();
  const { handleError } = useErrorHandler();

  const handleCreateWallet = useCallback(async (data) => {
    try {
      await createWallet(data);
      enqueueSnackbar('钱包创建成功', { variant: 'success' });
    } catch (error) {
      handleError(error, '创建钱包');
    }
  }, [createWallet, handleError]);

  return (
    // 组件内容
  );
}
```

## 📝 最佳实践总结

### ✅ DO
- 使用函数组件和React 19特性
- 使用useCallback和useMemo优化性能
- 创建可复用的自定义Hook
- 使用Material-UI的sx prop进行样式设计
- 实现响应式设计
- 使用React Router v7的数据路由模式
- 使用SWR进行数据获取和缓存
- 使用React.memo、useMemo、useCallback优化性能
- 使用React.lazy进行代码分割
- 正确使用国际化
- 统一错误处理

### ❌ DON'T
- 不要使用类组件（除非必需）
- 不要在render中创建新的对象或函数
- 不要过度使用Context（避免性能问题）
- 不要忽略key属性
- 不要在useEffect中忘记清理副作用
- 不要直接修改state
- 不要在组件外部定义组件
- 不要忽略错误处理
- 不要硬编码文本（使用国际化）
- 不要忽略无障碍性（a11y）
