# Go后端开发模式

基于A8Tools项目的Go后端架构模式和最佳实践。

## 📁 项目结构规范

### 标准目录组织
```
backend/
├── models/          # 数据模型层
│   ├── account/    # 账户相关模型
│   ├── base.go     # 基础模型定义
│   └── *.go        # 其他业务模型
├── services/        # 业务服务层
│   ├── account/    # 账户服务
│   ├── wallet/     # 钱包服务
│   └── */          # 其他业务服务
├── pkg/            # 核心功能包
│   ├── chrome/     # 浏览器管理
│   ├── proxy/      # 代理服务
│   └── */          # 其他核心包
├── db/             # 数据库层
├── utils/          # 工具函数
└── init.go         # 初始化逻辑
```

## 🏗️ 服务层模式

### BaseService泛型模式
```go
// ✅ DO: 使用泛型BaseService提供通用CRUD
type WalletService struct {
    *db.BaseService[models.Wallet]
}

func NewWalletService() *WalletService {
    return &WalletService{
        BaseService: db.NewBaseService[models.Wallet](),
    }
}

// ✅ DO: 继承BaseService的所有方法
// - Create(entity *T) error
// - GetByID(id uint) (*T, error)  
// - Update(entity *T) error
// - DeleteByID(id uint) error
// - List() ([]T, error)
// - Count(conditions map[string]interface{}) (int64, error)
// - BatchCreate(entities []T) error
// - BatchDelete(ids []uint) error
```

### 业务服务扩展
```go
// ✅ DO: 在BaseService基础上添加业务方法
type WalletService struct {
    *db.BaseService[models.Wallet]
}

// ✅ DO: 添加特定业务逻辑
func (s *WalletService) GetByChain(chain string) ([]models.Wallet, error) {
    conditions := map[string]interface{}{"chain": chain}
    return s.FindByConditions(conditions)
}

// ✅ DO: 复杂业务逻辑使用专门方法
func (s *WalletService) GenerateWalletWithMnemonic(chain string) (*models.Wallet, error) {
    // 复杂的钱包生成逻辑
    mnemonic, err := s.generateMnemonic()
    if err != nil {
        return nil, fmt.Errorf("生成助记词失败: %w", err)
    }
    
    wallet := &models.Wallet{
        Chain:    chain,
        Mnemonic: mnemonic,
        // ... 其他字段
    }
    
    if err := s.Create(wallet); err != nil {
        return nil, fmt.Errorf("保存钱包失败: %w", err)
    }
    
    return wallet, nil
}
```

### 非BaseService服务模式
```go
// ✅ DO: 对于特殊业务逻辑，使用独立服务
type SettingService struct {
    database *gorm.DB
}

func NewSettingService() *SettingService {
    return &SettingService{
        database: db.DB,
    }
}

// ✅ DO: 实现特定的业务方法
func (s *SettingService) Get(key string) (string, error) {
    var setting models.Setting
    if err := s.database.Where("key = ?", key).First(&setting).Error; err != nil {
        return "", err
    }
    return setting.Value, nil
}

func (s *SettingService) Set(key string, value string) error {
    var setting models.Setting
    
    if err := s.database.Where("key = ?", key).First(&setting).Error; err != nil {
        if err == gorm.ErrRecordNotFound {
            setting = models.Setting{Key: key, Value: value}
        } else {
            return err
        }
    } else {
        setting.Value = value
    }
    
    return s.database.Save(&setting).Error
}
```

## 🔗 接口设计模式

### 完整接口定义
```go
// ✅ DO: 定义清晰的接口契约
type ProxyManager interface {
    // 基础操作
    Start(config *ProxyConfig) error
    Stop(id string) error
    Restart(id string) error
    
    // 状态查询
    GetStatus(id string) (*ProxyStatus, error)
    ListAll() ([]*ProxyStatus, error)
    
    // 配置管理
    UpdateConfig(id string, config *ProxyConfig) error
    GetConfig(id string) (*ProxyConfig, error)
    
    // 健康检查
    HealthCheck(id string) (*HealthStatus, error)
    
    // 清理资源
    Cleanup() error
}

// ✅ DO: 实现完整接口
type DefaultProxyManager struct {
    processes map[string]*ProxyProcess
    mutex     sync.RWMutex
    logger    *slog.Logger
}

func (m *DefaultProxyManager) Start(config *ProxyConfig) error {
    m.mutex.Lock()
    defer m.mutex.Unlock()
    
    // 实现启动逻辑
    process, err := m.createProcess(config)
    if err != nil {
        return fmt.Errorf("创建代理进程失败: %w", err)
    }
    
    m.processes[config.ID] = process
    return nil
}
```

## ❌ 错误处理模式

### 自定义错误类型
```go
// ✅ DO: 定义领域特定错误
type ProxyError struct {
    Type    string
    Message string
    Cause   error
}

func (e *ProxyError) Error() string {
    if e.Cause != nil {
        return fmt.Sprintf("%s: %s (原因: %v)", e.Type, e.Message, e.Cause)
    }
    return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

func (e *ProxyError) Unwrap() error {
    return e.Cause
}

// ✅ DO: 创建错误构造函数
func NewProxyError(errType, message string, cause error) *ProxyError {
    return &ProxyError{
        Type:    errType,
        Message: message,
        Cause:   cause,
    }
}

// ✅ DO: 预定义常见错误
var (
    ErrProxyNotFound    = NewProxyError("NOT_FOUND", "代理未找到", nil)
    ErrProxyNotRunning  = NewProxyError("NOT_RUNNING", "代理未运行", nil)
    ErrInvalidConfig    = NewProxyError("INVALID_CONFIG", "配置无效", nil)
)
```

### 错误包装和传播
```go
// ✅ DO: 使用fmt.Errorf包装错误
func (s *ProxyService) StartProxy(config *ProxyConfig) error {
    if err := s.validateConfig(config); err != nil {
        return fmt.Errorf("配置验证失败: %w", err)
    }
    
    if err := s.manager.Start(config); err != nil {
        return fmt.Errorf("启动代理失败: %w", err)
    }
    
    return nil
}

// ✅ DO: 在服务层提供有意义的错误信息
func (s *WalletService) CreateWallet(req *CreateWalletRequest) (*models.Wallet, error) {
    if req.Chain == "" {
        return nil, fmt.Errorf("区块链类型不能为空")
    }
    
    wallet := &models.Wallet{
        Name:  req.Name,
        Chain: req.Chain,
    }
    
    if err := s.Create(wallet); err != nil {
        return nil, fmt.Errorf("创建钱包失败: %w", err)
    }
    
    return wallet, nil
}

// ❌ DON'T: 吞没错误或返回无意义错误
func (s *BadService) DoSomething() error {
    if err := s.operation(); err != nil {
        return errors.New("操作失败") // 丢失了原始错误信息
    }
    return nil
}
```

## 📦 包组织模式

### 核心包结构
```go
// ✅ DO: 清晰的包导出
package proxy

import (
    "a8.tools/backend/pkg/proxy/core"
    "a8.tools/backend/pkg/proxy/api"
    "a8.tools/backend/pkg/proxy/types"
)

// ✅ DO: 导出主要接口和类型
type (
    Manager = core.ProxyManager
    Config  = types.ProxyConfig
    Status  = types.ProxyStatus
)

// ✅ DO: 提供便捷的构造函数
func NewManager(opts ...Option) Manager {
    return core.NewDefaultManager(opts...)
}

func NewAPI() *api.ProxyApi {
    return api.NewProxyApi()
}
```

### 内部包组织
```go
// ✅ DO: 使用internal限制包访问
package internal

// ✅ DO: 共享常量和类型
package common

const (
    ProxyTypeShadowsocks = "shadowsocks"
    ProxyTypeVMess       = "vmess"
    DefaultTimeout       = 300 * time.Second
)

// ✅ DO: 类型定义集中管理
package types

type ProxyConfig struct {
    ID       string            `json:"id"`
    Type     string            `json:"type"`
    Server   string            `json:"server"`
    Port     int               `json:"port"`
    Settings map[string]string `json:"settings"`
}
```

## 🔄 并发模式

### Context使用
```go
// ✅ DO: 正确传递和使用Context
func (s *ProxyService) StartWithContext(ctx context.Context, config *ProxyConfig) error {
    // 检查上下文是否已取消
    select {
    case <-ctx.Done():
        return ctx.Err()
    default:
    }
    
    // 将context传递给下层
    return s.manager.StartWithContext(ctx, config)
}

// ✅ DO: 在长时间运行的操作中检查context
func (m *ProxyManager) monitorProcess(ctx context.Context, processID string) {
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-ctx.Done():
            m.logger.Info("监控被取消", "processID", processID)
            return
        case <-ticker.C:
            if err := m.healthCheck(processID); err != nil {
                m.logger.Error("健康检查失败", "processID", processID, "error", err)
            }
        }
    }
}
```

### Goroutine管理
```go
// ✅ DO: 使用sync.WaitGroup管理goroutine
func (s *ProxyService) StartMultiple(configs []*ProxyConfig) error {
    var wg sync.WaitGroup
    errChan := make(chan error, len(configs))
    
    for _, config := range configs {
        wg.Add(1)
        go func(cfg *ProxyConfig) {
            defer wg.Done()
            if err := s.Start(cfg); err != nil {
                errChan <- fmt.Errorf("启动代理 %s 失败: %w", cfg.ID, err)
            }
        }(config)
    }
    
    wg.Wait()
    close(errChan)
    
    // 收集所有错误
    var errors []error
    for err := range errChan {
        errors = append(errors, err)
    }
    
    if len(errors) > 0 {
        return fmt.Errorf("部分代理启动失败: %v", errors)
    }
    
    return nil
}
```

## 🧪 测试模式

### 使用testify suite
```go
// ✅ DO: 使用testify suite组织测试
type WalletServiceSuite struct {
    suite.Suite
    mock         sqlmock.Sqlmock
    service      *WalletService
    originalDB   *gorm.DB
    mockDB       *gorm.DB
    app          *fxtest.App
}

func (s *WalletServiceSuite) SetupTest() {
    var (
        sqlDB *sql.DB
        err   error
    )

    sqlDB, s.mock, err = sqlmock.New()
    s.Require().NoError(err)

    s.mockDB, err = gorm.Open(mysql.New(mysql.Config{
        Conn:                      sqlDB,
        SkipInitializeWithVersion: true,
    }), &gorm.Config{})
    s.Require().NoError(err)

    // 保存原始DB并替换为mock
    s.originalDB = originaldb.DB
    originaldb.DB = s.mockDB

    // 使用fx创建测试应用
    s.app = fxtest.New(s.T(),
        fx.Provide(NewWalletService),
        fx.Populate(&s.service),
    )

    s.app.RequireStart()
}

func (s *WalletServiceSuite) TearDownTest() {
    originaldb.DB = s.originalDB
    if s.app != nil {
        s.app.RequireStop()
    }
}

func TestWalletServiceSuite(t *testing.T) {
    suite.Run(t, new(WalletServiceSuite))
}
```

### 测试用例编写
```go
// ✅ DO: 详细的测试用例
func (s *WalletServiceSuite) TestCreateWallet() {
    s.T().Run("should create a new wallet", func(t *testing.T) {
        wallet := &models.Wallet{
            Name:     "Test Wallet",
            Mnemonic: "test twelve words mnemonic phrase",
            Remark:   "Test wallet remark",
        }

        s.mock.ExpectBegin()
        s.mock.ExpectExec(regexp.QuoteMeta(
            "INSERT INTO `wallet` (`created_at`,`updated_at`,`deleted_at`,`name`,`mnemonic`,`remark`) VALUES (?,?,?,?,?,?)")).
            WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), nil, wallet.Name, wallet.Mnemonic, wallet.Remark).
            WillReturnResult(sqlmock.NewResult(1, 1))
        s.mock.ExpectCommit()

        err := s.service.Create(wallet)

        assert.NoError(t, err)
        assert.NotZero(t, wallet.ID)
        assert.NoError(t, s.mock.ExpectationsWereMet())
    })
}
```

## 🏷️ Wails绑定标签

### 服务绑定
```go
// ✅ DO: 使用Wails绑定标签暴露服务
type WalletService struct {
    *db.BaseService[models.Wallet]
}

//go:wails:bind
func NewWalletService() *WalletService {
    return &WalletService{
        BaseService: db.NewBaseService[models.Wallet](),
    }
}

// ✅ DO: 暴露给前端的方法使用清晰命名
func (s *WalletService) CreateWallet(name, chain, mnemonic string) error {
    wallet := &models.Wallet{
        Name:     name,
        Chain:    chain,
        Mnemonic: mnemonic,
    }
    return s.Create(wallet)
}

func (s *WalletService) GetWallets() ([]models.Wallet, error) {
    return s.List()
}
```

## 🔒 安全模式

### 输入验证
```go
// ✅ DO: 严格的输入验证
func (s *WalletService) CreateWallet(req *CreateWalletRequest) error {
    if req == nil {
        return fmt.Errorf("请求不能为空")
    }
    
    if strings.TrimSpace(req.Name) == "" {
        return fmt.Errorf("钱包名称不能为空")
    }
    
    if len(req.Name) > 100 {
        return fmt.Errorf("钱包名称不能超过100个字符")
    }
    
    if !isValidChain(req.Chain) {
        return fmt.Errorf("不支持的区块链类型: %s", req.Chain)
    }
    
    // 继续创建逻辑...
}

// ✅ DO: 验证函数
func isValidChain(chain string) bool {
    validChains := []string{"BTC", "ETH", "SOL", "MATIC", "BSC"}
    for _, valid := range validChains {
        if chain == valid {
            return true
        }
    }
    return false
}
```

### 敏感数据处理
```go
// ✅ DO: 使用keyring安全存储
func (s *KeyringService) SetPassword(key, password string) error {
    if key == "" || password == "" {
        return fmt.Errorf("密钥和密码不能为空")
    }
    
    hashedPassword, err := utils.HashPassword(password)
    if err != nil {
        return fmt.Errorf("密码哈希失败: %w", err)
    }
    
    return keyring.Set(ServiceName, key, hashedPassword)
}

// ✅ DO: 验证时使用安全比较
func (s *KeyringService) VerifyPassword(key, password string) (bool, error) {
    storedHash, err := keyring.Get(ServiceName, key)
    if err != nil {
        return false, fmt.Errorf("获取存储密码失败: %w", err)
    }
    
    return utils.VerifyPassword(password, storedHash), nil
}
```

## 📝 最佳实践总结

### ✅ DO
- 使用泛型BaseService提供统一CRUD操作
- 在BaseService基础上扩展业务特定方法
- 定义清晰的接口契约
- 使用自定义错误类型和错误包装
- 正确传递和使用Context
- 编写完整的测试用例
- 严格验证所有输入
- 使用keyring存储敏感数据

### ❌ DON'T
- 不要绕过BaseService直接操作数据库
- 不要吞没错误或返回无意义错误
- 不要在没有Context的情况下执行长时间操作
- 不要忽略并发安全
- 不要在代码中硬编码敏感信息
- 不要跳过输入验证
- 不要使用panic处理可恢复错误
