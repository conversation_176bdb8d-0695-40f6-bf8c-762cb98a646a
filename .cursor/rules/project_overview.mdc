# A8Tools 项目概览

A8Tools是基于Wails v3 + React 19 + Go构建的跨平台桌面应用，专为加密货币空投猎人设计。

## 🚀 技术栈

### 后端技术栈
- **框架**: Go 1.23+ + Wails v3 (桌面应用框架)
- **数据库**: SQLite + GORM (ORM框架)
- **浏览器自动化**: go-rod + stealth (防检测)
- **安全存储**: go-keyring (系统级密码管理)
- **依赖注入**: Uber Fx (依赖管理)
- **代理管理**: 自研代理包 (支持多协议)

### 前端技术栈
- **框架**: React 19.1.0 + Vite 6.2.3
- **UI组件**: Material-UI v7 (主题系统)
- **路由**: React Router v7 (数据路由模式)
- **Web3**: OKX Web3 SDK (15+区块链支持)
- **国际化**: i18next (中英双语)
- **状态管理**: React Context + SWR

### 构建工具
- **主构建**: Task Runner (Taskfile.yml)
- **开发命令**: `wails3 dev` (开发), `wails3 build` (生产)
- **代码质量**: ESLint + Prettier + Go标准工具

## 📁 项目结构

```
desktop/
├── backend/                 # Go后端
│   ├── models/             # 数据模型 (account/, base.go, wallet等)
│   ├── services/           # 业务服务层 (account/, wallet/, proxy/等)
│   ├── pkg/               # 核心包
│   │   ├── chrome/        # 浏览器管理 (指纹、自动化)
│   │   ├── proxy/         # 代理服务 (多协议支持)
│   │   ├── rod/           # 浏览器自动化
│   │   └── cryptor/       # 加密工具
│   ├── db/                # 数据库层 (BaseService泛型)
│   └── utils/             # 工具函数
├── frontend/               # React前端
│   └── src/
│       ├── components/    # 可复用组件 (50+模块化组件)
│       ├── pages/         # 页面组件
│       ├── layouts/       # 布局组件 (dashboard, auth-split等)
│       ├── sections/      # 功能区块组件
│       ├── hooks/         # 自定义Hook
│       ├── theme/         # Material-UI主题
│       ├── locales/       # 国际化资源
│       └── lib/           # 工具库 (wallet, tools等)
└── main.go                # 应用入口
```

## 🔧 核心功能模块

### 1. 多账户管理
- **邮箱账户**: Gmail、Outlook等邮箱管理
- **社交账户**: Discord、Telegram、X(Twitter)
- **代理账户**: 多协议代理配置管理
- **统一存储**: 基于BaseService的泛型CRUD

### 2. 加密钱包管理
- **多链支持**: 15+主流区块链 (Bitcoin, Ethereum, Solana等)
- **OKX集成**: 使用OKX Web3 SDK进行钱包操作
- **安全存储**: 助记词和私钥的安全管理
- **批量操作**: 钱包的批量创建和管理

### 3. 代理服务系统
- **多协议支持**: Shadowsocks, V2Ray, SSR, Trojan等
- **订阅管理**: 自动解析和更新代理订阅
- **进程管理**: 完整的代理进程生命周期管理
- **健康检查**: 实时监控代理状态和性能

### 4. 浏览器自动化
- **指纹浏览器**: 完整的浏览器指纹伪装
- **反检测技术**: WebDriver检测规避、Canvas指纹干扰
- **自动化脚本**: 基于go-rod的Web自动化
- **会话管理**: 多个浏览器会话并行管理

### 5. 安全与存储
- **密码管理**: 基于系统keyring的安全存储
- **自动锁屏**: 可配置的应用自动锁定
- **数据加密**: 敏感数据的加密存储
- **权限控制**: 基于密码的应用访问控制

## 🛠️ 开发工具和命令

### 开发环境
```bash
# 启动开发服务器
wails3 dev

# 构建应用
wails3 build

# 前端开发 (独立)
cd frontend && npm run dev
```

### 代码质量检查
```bash
# 前端检查
npm run lint && npm run fm:check && npm run test

# 后端检查  
go fmt ./... && go vet ./... && go test ./...

# 一键修复
npm run fix:all
```

### 项目管理
```bash
# 清理重建
npm run re:build

# 依赖管理
go mod tidy && npm install
```

## 🎯 关键设计模式

### 后端模式
- **服务层模式**: 每个业务领域独立Service
- **泛型BaseService**: `BaseService[T]`提供通用CRUD
- **接口驱动**: 如proxy包的完整接口设计
- **依赖注入**: 使用Uber Fx管理服务依赖

### 前端模式
- **组件化架构**: 高度模块化的React组件
- **Hook模式**: 自定义Hook封装业务逻辑
- **Context模式**: 全局状态和设置管理
- **表格组件**: 通用TableView支持搜索、分页、批量操作

## 🔐 安全考虑

- **敏感数据**: 使用go-keyring系统级存储
- **输入验证**: 所有用户输入的严格验证
- **防检测**: 浏览器指纹伪装和反爬虫技术
- **权限管理**: 基于密码的应用访问控制

## 🚀 部署目标

- **跨平台**: Windows、macOS、Linux
- **单一可执行文件**: Wails打包为独立应用
- **便携式**: 支持绿色版部署
- **自动更新**: 内置更新检查机制

## 📋 开发规范

- **代码风格**: Go标准格式 + ESLint/Prettier
- **测试覆盖**: 关键业务逻辑必须有测试
- **文档要求**: 公开接口必须有注释
- **版本控制**: 语义化版本管理
- **国际化**: 所有用户界面文本支持多语言
