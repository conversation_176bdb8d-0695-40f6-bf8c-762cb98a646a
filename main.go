package main

import (
	"crypto/rand"
	"embed"
	_ "embed"
	"log"
	"log/slog"

	"a8.tools/backend"
	"a8.tools/backend/config"
	"a8.tools/backend/services/account"
	"a8.tools/backend/services/keyring"
	"a8.tools/backend/services/setting"
	"a8.tools/backend/services/wallet"
	"github.com/wailsapp/wails/v3/pkg/application"
)

// Wails uses Go's `embed` package to embed the frontend files into the binary.
// Any files in the frontend/dist folder will be embedded into the binary and
// made available to the frontend.
// See https://pkg.go.dev/embed for more information.

//go:embed all:frontend/dist
var assets embed.FS

// generateRandomEncryptionKey 生成一个32字节的随机加密密钥
func generateRandomEncryptionKey() [32]byte {
	var key [32]byte
	_, err := rand.Read(key[:])
	if err != nil {
		log.Fatal("Failed to generate random encryption key:", err)
	}
	return key
}

// 生成随机加密密钥
var encryptionKey = generateRandomEncryptionKey()

var mainWindow *application.WebviewWindow

// main function serves as the application's entry point. It initializes the application, creates a window,
// and starts a goroutine that emits a time-based event every second. It subsequently runs the application and
// logs any error that might occur.
func startApp() {

	// Create a new Wails application by providing the necessary options.
	// Variables 'Name' and 'Description' are for application metadata.
	// 'Assets' configures the asset server with the 'FS' variable pointing to the frontend files.
	// 'Bind' is a list of Go struct instances. The frontend has access to the methods of these instances.
	// 'Mac' options tailor the application when running an macOS.

	// 根据构建标签确定安全的日志级别
	var logLevel slog.Level
	if config.IsDebugBuild {
		logLevel = slog.LevelInfo // 调试模式：启用详细日志
		log.Println("Debug build - Wails logging enabled (Debug level)")
	} else {
		logLevel = slog.LevelError // 生产模式：只记录错误，禁用调试信息
		log.Println("Release build - Wails logging restricted (Error level only)")
	}

	app := application.New(application.Options{
		Name:        "A8 Tools",
		Description: "A8 Tools",

		Assets: application.AssetOptions{
			Handler: application.AssetFileServerFS(assets),
		},
		Services: []application.Service{
			application.NewService(account.NewMailService()),
			application.NewService(account.NewDiscordService()),
			application.NewService(account.NewTelegramService()),
			application.NewService(account.NewProxyService()),
			application.NewService(account.NewXService()),
			application.NewService(keyring.NewAppPasswordService()),
			application.NewService(wallet.NewWalletService()),
			application.NewService(setting.NewSettingService()),
		},

		Mac: application.MacOptions{
			ApplicationShouldTerminateAfterLastWindowClosed: true,
		},
		Windows: application.WindowsOptions{},
		Linux:   application.LinuxOptions{},
		OnShutdown: func() {

		},
		PanicHandler: func(err interface{}) {
			log.Printf("Panic: %v", err)
		},
		SingleInstance: &application.SingleInstanceOptions{
			UniqueID: "tools.a8.app",
			OnSecondInstanceLaunch: func(data application.SecondInstanceData) {
				if mainWindow != nil {
					mainWindow.Restore()
					mainWindow.Focus()
				}
			},
			EncryptionKey: encryptionKey,
		},
		LogLevel: logLevel, // 使用安全的日志级别配置
	})

	// Create a new window with the necessary options.
	// 'Title' is the title of the window.
	// 'Mac' options tailor the window when running on macOS.
	// 'BackgroundColour' is the background colour of the window.
	// 'URL' is the URL that will be loaded into the webview.
	mainWindow = app.NewWebviewWindowWithOptions(application.WebviewWindowOptions{
		Title:  "A8 Tools",
		Width:  1600,
		Height: 1000,
		Mac: application.MacWindow{
			InvisibleTitleBarHeight: 50,
			Backdrop:                application.MacBackdropTranslucent,
			TitleBar:                application.MacTitleBarHiddenInset,
		},
		BackgroundColour: application.NewRGB(27, 38, 54),
		URL:              "/",
		DevToolsEnabled:  config.IsDebugBuild, // 使用统一的构建配置
	})

	// Run the application. This blocks until the application has been exited.
	err := app.Run()

	// If an error occurred while running the application, log it and exit.
	if err != nil {
		log.Fatal(err)
	}
}

func main() {

	// 初始化
	backend.Init()

	// 启动应用
	startApp()
}
