package account

import "a8.tools/backend/models"

type Email struct {
	models.BaseModel
	Account  string `gorm:"column:account;type:text;not null" json:"account" encrypt:"true"`
	Password string `gorm:"column:password;type:text;not null" json:"password" encrypt:"true"`
	Remark   string `gorm:"column:remark;type:text;default:''" json:"remark"`
	Group    string `gorm:"column:group;type:text;default:''" json:"group"`
	Labels   string `gorm:"column:labels;type:text;default:''"`
}

func (e *Email) TableName() string {
	return "email"
}

func (e *Email) GetID() uint {
	return e.ID
}

func (e *Email) SetID(id uint) {
	e.ID = id
}
