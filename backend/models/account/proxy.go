package account

import "a8.tools/backend/models"

type Proxy struct {
	models.BaseModel
	Type       string `gorm:"column:type;type:text;default:''" json:"type"`
	Server     string `gorm:"column:server;type:text;not null" json:"server"`
	Port       int    `gorm:"column:port;type:int;not null" json:"port"`
	Username   string `gorm:"column:username;type:text;default:''" json:"username"`
	Password   string `gorm:"column:password;type:text;default:''" json:"password"`
	Group      string `gorm:"column:group;type:text;default:''" json:"group"`
	Status     string `gorm:"column:status;type:text;default:''" json:"status"`
	IPLocation string `gorm:"column:ip_location;type:text;default:''" json:"ip_location"`
	Remark     string `gorm:"column:remark;type:text;default:''" json:"remark"`
}

func (p *Proxy) TableName() string {
	return "proxy"
}

func (p *Proxy) GetID() uint {
	return p.ID
}

func (p *Proxy) SetID(id uint) {
	p.ID = id
}
