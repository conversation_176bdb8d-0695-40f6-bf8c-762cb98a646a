package account

import "a8.tools/backend/models"

type Discord struct {
	models.BaseModel
	Account  string `gorm:"column:account;type:text;not null" json:"account" encrypt:"true"`
	Password string `gorm:"column:password;type:text;not null" json:"password" encrypt:"true"`
	Remark   string `gorm:"column:remark;type:text;default:''" json:"remark"`
	Group    string `gorm:"column:group;type:text;default:''" json:"group"`
	Labels   string `gorm:"column:labels;type:text;default:''" json:"labels"`
}

func (d *Discord) TableName() string {
	return "discord"
}

func (d *Discord) GetID() uint {
	return d.ID
}

func (d *Discord) SetID(id uint) {
	d.ID = id
}
