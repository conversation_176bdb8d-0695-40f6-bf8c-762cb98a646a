package models

import (
	"time"

	"a8.tools/backend/pkg/proxy/types"
)

// ProxySubscription 订阅源模型
type ProxySubscription struct {
	BaseModel
	Name        string                 `gorm:"column:name;type:varchar(255);not null" json:"name"`            // 订阅源名称
	URL         string                 `gorm:"column:url;type:text;not null" json:"url"`                      // 订阅链接
	Description string                 `gorm:"column:description;type:text" json:"description"`               // 订阅源描述
	Type        types.SubscriptionType `gorm:"column:type;type:varchar(50)" json:"type"`                      // 订阅类型
	LastUpdate  *time.Time             `gorm:"column:last_update;type:datetime" json:"last_update"`           // 最后更新时间
	NodeCount   int                    `gorm:"column:node_count;type:int;default:0" json:"node_count"`        // 节点数量
	Status      string                 `gorm:"column:status;type:varchar(50);default:'active'" json:"status"` // 状态：active, inactive, error
	ErrorMsg    string                 `gorm:"column:error_msg;type:text" json:"error_msg"`                   // 错误信息
	UpdatedBy   string                 `gorm:"column:updated_by;type:varchar(100)" json:"updated_by"`         // 更新者
}

// ProxyNode 代理节点模型
type ProxyNode struct {
	BaseModel
	SubscriptionID uint       `gorm:"column:subscription_id;not null;index" json:"subscription_id"`     // 所属订阅源ID
	NodeData       string     `gorm:"column:node_data;type:text;not null" json:"node_data"`             // 节点数据JSON
	Name           string     `gorm:"column:name;type:varchar(255)" json:"name"`                        // 节点名称
	Server         string     `gorm:"column:server;type:varchar(255)" json:"server"`                    // 服务器地址
	Port           int        `gorm:"column:port;type:int" json:"port"`                                 // 端口
	Type           string     `gorm:"column:type;type:varchar(50)" json:"type"`                         // 节点类型
	Status         string     `gorm:"column:status;type:varchar(50);default:'available'" json:"status"` // 状态：available, running, error
	LastUsed       *time.Time `gorm:"column:last_used;type:datetime" json:"last_used"`                  // 最后使用时间

	// 关联关系
	Subscription ProxySubscription `gorm:"foreignKey:SubscriptionID" json:"subscription,omitempty"`
}

// ProxyInstance 代理实例模型（正在运行的代理）
type ProxyInstance struct {
	BaseModel
	NodeID     uint       `gorm:"column:node_id;not null;index" json:"node_id"`                    // 关联的节点ID
	LocalPort  int        `gorm:"column:local_port;type:int;not null" json:"local_port"`           // 本地端口
	Status     string     `gorm:"column:status;type:varchar(50);default:'starting'" json:"status"` // 状态：starting, running, stopped, error
	PID        int        `gorm:"column:pid;type:int" json:"pid"`                                  // 进程ID
	ConfigPath string     `gorm:"column:config_path;type:varchar(500)" json:"config_path"`         // 配置文件路径
	StartTime  *time.Time `gorm:"column:start_time;type:datetime" json:"start_time"`               // 启动时间
	StopTime   *time.Time `gorm:"column:stop_time;type:datetime" json:"stop_time"`                 // 停止时间
	ErrorMsg   string     `gorm:"column:error_msg;type:text" json:"error_msg"`                     // 错误信息

	// 关联关系
	Node ProxyNode `gorm:"foreignKey:NodeID" json:"node,omitempty"`
}

// TableName 指定表名
func (ProxySubscription) TableName() string {
	return "proxy_subscriptions"
}

func (ProxyNode) TableName() string {
	return "proxy_nodes"
}

func (ProxyInstance) TableName() string {
	return "proxy_instances"
}

// ProxySubscriptionStatus 订阅源状态常量
const (
	SubscriptionStatusActive   = "active"
	SubscriptionStatusInactive = "inactive"
	SubscriptionStatusError    = "error"
)

// ProxyNodeStatus 代理节点状态常量
const (
	NodeStatusAvailable = "available"
	NodeStatusRunning   = "running"
	NodeStatusError     = "error"
)

// ProxyInstanceStatus 代理实例状态常量
const (
	InstanceStatusStarting = "starting"
	InstanceStatusRunning  = "running"
	InstanceStatusStopped  = "stopped"
	InstanceStatusError    = "error"
)
