package models

type Wallet struct {
	BaseModel
	Name     string `gorm:"column:name;type:varchar(255);" json:"name"`
	Mnemonic string `gorm:"column:mnemonic;type:varchar(255);" json:"mnemonic"`
	Group    string `gorm:"column:group;type:varchar(255);" json:"group"`
	Label    string `gorm:"column:label;type:varchar(255);" json:"label"`
	Remark   string `gorm:"column:remark;type:varchar(255);" json:"remark"`
}

func (w *Wallet) TableName() string {
	return "wallet"
}

func (w *Wallet) GetID() uint {
	return w.ID
}

func (w *Wallet) SetID(id uint) {
	w.ID = id
}
