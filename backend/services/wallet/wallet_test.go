package wallet

import (
	"database/sql"
	"regexp"
	"testing"
	"time"

	originaldb "a8.tools/backend/db"
	"a8.tools/backend/models" // Added back
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type WalletServiceSuite struct {
	suite.Suite
	mock         sqlmock.Sqlmock
	service      *WalletService
	originalDB   *gorm.DB // To store the original db.DB
	mockDB       *gorm.DB // Our mock gorm.DB instance
	app          *fxtest.App
}

func (s *WalletServiceSuite) SetupTest() {
	var (
		sqlDB *sql.DB
		err   error
	)

	sqlDB, s.mock, err = sqlmock.New()
	s.Require().NoError(err)

	// Create a gorm.DB instance using the mock
	s.mockDB, err = gorm.Open(mysql.New(mysql.Config{
		Conn:                      sqlDB,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})
	s.Require().NoError(err)

	// Store the original global DB and replace it with the mock DB
	s.originalDB = originaldb.DB
	originaldb.DB = s.mockDB

	// Create an fx app for testing
	s.app = fxtest.New(s.T(),
		fx.Provide(
			NewWalletService, // Provide the service constructor
		),
		fx.Populate(&s.service), // Populate the service instance
	)

	s.app.RequireStart()
}

func (s *WalletServiceSuite) TearDownTest() {
	// Restore the original global DB
	originaldb.DB = s.originalDB
	if s.app != nil {
		s.app.RequireStop()
	}
}

func TestWalletServiceSuite(t *testing.T) {
	suite.Run(t, new(WalletServiceSuite))
}

func (s *WalletServiceSuite) TestNewWalletService() {
	s.T().Run("should create a new wallet service via fx", func(t *testing.T) {
		assert.NotNil(t, s.service, "Expected WalletService not to be nil")
		assert.NotNil(t, s.service.BaseService, "Expected BaseService within WalletService not to be nil")
		assert.Equal(t, s.mockDB, s.service.BaseService.DB(), "Expected WalletService to use the mock DB")
	})
}

func (s *WalletServiceSuite) TestWalletService_Count() {
	s.T().Run("should return the correct count of wallets using mock db via fx", func(t *testing.T) {
		count := int64(5)
		rows := sqlmock.NewRows([]string{"count"}).AddRow(count)

		s.mock.ExpectQuery(regexp.QuoteMeta(
			"SELECT count(*) FROM `wallet` WHERE `chain` = ? AND `wallet`.`deleted_at` IS NULL")).
			WithArgs("ETH").
			WillReturnRows(rows)

		conditions := map[string]interface{}{"chain": "ETH"}
		actualCount, err := s.service.Count(conditions)

		assert.NoError(t, err)
		assert.Equal(t, count, actualCount)
		assert.NoError(t, s.mock.ExpectationsWereMet())
	})
}

func (s *WalletServiceSuite) TestWalletService_Create() {
	s.T().Run("should create a new wallet", func(t *testing.T) {
		walletInstance := &models.Wallet{
			Name:     "Test Wallet Create",
			Mnemonic: "test twelve words for mnemonic phrase", // Example mnemonic
			Remark:   "This is a test wallet for Create method",
		}

		s.mock.ExpectBegin()
		// The columns in INSERT INTO should match the fields in models.Wallet (excluding gorm.Model's ID)
		// and GORM's auto-timestamps and soft delete.
		s.mock.ExpectExec(regexp.QuoteMeta(
			"INSERT INTO `wallet` (`created_at`,`updated_at`,`deleted_at`,`name`,`mnemonic`,`remark`) VALUES (?,?,?,?,?,?)")).
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), nil, walletInstance.Name, walletInstance.Mnemonic, walletInstance.Remark).
			WillReturnResult(sqlmock.NewResult(1, 1)) // Assumes ID 1 is generated, 1 row affected
		s.mock.ExpectCommit()

		err := s.service.Create(walletInstance)

		assert.NoError(t, err)
		// Assuming GORM sets the ID after creation
		assert.NotZero(t, walletInstance.ID, "Expected wallet ID to be set after creation")
		assert.NoError(t, s.mock.ExpectationsWereMet())
	})
}

func (s *WalletServiceSuite) TestWalletService_GetByID() {
	s.T().Run("should return a wallet by its ID", func(t *testing.T) {
		walletID := uint(1)
		expectedWallet := &models.Wallet{
			Name:     "Test Wallet GetByID",
			Mnemonic: "test mnemonic for get by id",
			Remark:   "Test remark for GetByID",
		}
		expectedWallet.ID = walletID // Manually set the ID for expectation

		// Define the columns that GORM will select. Order matters and should match GORM's typical select order or be explicit.
		// gorm.Model fields (ID, CreatedAt, UpdatedAt, DeletedAt) are usually first or last depending on definition and GORM version.
		// Let's assume a common order: ID, then custom fields, then timestamps.
		rows := sqlmock.NewRows([]string{"id", "name", "mnemonic", "remark", "created_at", "updated_at", "deleted_at"}).
			AddRow(expectedWallet.ID, expectedWallet.Name, expectedWallet.Mnemonic, expectedWallet.Remark, time.Now(), time.Now(), nil)

		// Expect a SELECT query. GORM by default selects all fields (`*`).
		// The WHERE clause includes `id = ?` and the soft delete check `wallet`.`deleted_at` IS NULL.
		// ORDER BY `wallet`.`id` LIMIT 1 is also common for GetByID type queries.
		s.mock.ExpectQuery(regexp.QuoteMeta(
			"SELECT * FROM `wallet` WHERE id = ? AND `wallet`.`deleted_at` IS NULL ORDER BY `wallet`.`id` LIMIT ?")).
			WithArgs(walletID, 1).
			WillReturnRows(rows)

		actualWallet, err := s.service.GetByID(walletID)

		assert.NoError(t, err)
		assert.NotNil(t, actualWallet)
		assert.Equal(t, expectedWallet.ID, actualWallet.ID)
		assert.Equal(t, expectedWallet.Name, actualWallet.Name)
		assert.Equal(t, expectedWallet.Mnemonic, actualWallet.Mnemonic)
		assert.Equal(t, expectedWallet.Remark, actualWallet.Remark)
		assert.NoError(t, s.mock.ExpectationsWereMet())
	})
}

func (s *WalletServiceSuite) TestWalletService_Update() {
	s.T().Run("should update an existing wallet", func(t *testing.T) {
		walletToUpdate := &models.Wallet{
			Name:     "Updated Wallet Name",
			Mnemonic: "updated mnemonic phrase",
			Remark:   "Updated remark.",
		}
		// This is crucial for GORM's Save/Update to perform an update on the correct record.
		// The service's Update method expects a model with an ID.
		walletToUpdate.ID = 1

		s.mock.ExpectBegin()
		// GORM's Update method on a model with an ID generates an UPDATE statement.
		// The column order is based on the struct fields. Let's match the order from the successful Create test.
		// `updated_at` will be automatically set by GORM. `created_at` is often included in the update statement by GORM
		// even if it's not changing.
		s.mock.ExpectExec(regexp.QuoteMeta(
			"UPDATE `wallet` SET `created_at`=?,`updated_at`=?,`deleted_at`=?,`name`=?,`mnemonic`=?,`remark`=? WHERE `wallet`.`deleted_at` IS NULL AND `id` = ?")).
			WithArgs(sqlmock.AnyArg(), sqlmock.AnyArg(), nil, walletToUpdate.Name, walletToUpdate.Mnemonic, walletToUpdate.Remark, walletToUpdate.ID).
			WillReturnResult(sqlmock.NewResult(0, 1)) // For UPDATE, LastInsertId is 0, 1 row affected
		s.mock.ExpectCommit()

		err := s.service.Update(walletToUpdate)

		assert.NoError(t, err)
		assert.NoError(t, s.mock.ExpectationsWereMet())
	})
}

func (s *WalletServiceSuite) TestWalletService_DeleteByID() {
	s.T().Run("should soft delete an existing wallet", func(t *testing.T) {
		walletID := uint(1)

		s.mock.ExpectBegin()
		// GORM's Delete with soft delete performs an UPDATE, setting the deleted_at field.
		s.mock.ExpectExec(regexp.QuoteMeta(
			"UPDATE `wallet` SET `deleted_at`=? WHERE id = ? AND `wallet`.`deleted_at` IS NULL")).
			WithArgs(sqlmock.AnyArg(), walletID). // The first arg is the timestamp for deleted_at
			WillReturnResult(sqlmock.NewResult(0, 1))
		s.mock.ExpectCommit()

		err := s.service.DeleteByID(walletID)

		assert.NoError(t, err)
		assert.NoError(t, s.mock.ExpectationsWereMet())
	})
}

func (s *WalletServiceSuite) TestWalletService_List() {
	s.T().Run("should return a list of wallets", func(t *testing.T) {
		rows := sqlmock.NewRows([]string{"id", "name", "mnemonic", "remark", "created_at", "updated_at", "deleted_at"}).
			AddRow(1, "Wallet 1", "mnemonic 1", "remark 1", time.Now(), time.Now(), nil).
			AddRow(2, "Wallet 2", "mnemonic 2", "remark 2", time.Now(), time.Now(), nil)

		// GORM's Find for a list typically includes the soft delete condition
		s.mock.ExpectQuery(regexp.QuoteMeta(
			"SELECT * FROM `wallet` WHERE `wallet`.`deleted_at` IS NULL")).
			WillReturnRows(rows)

		wallets, err := s.service.List()

		assert.NoError(t, err)
		assert.NotNil(t, wallets)
		assert.Len(t, wallets, 2)
		assert.Equal(t, uint(1), wallets[0].ID)
		assert.Equal(t, "Wallet 1", wallets[0].Name)
		assert.Equal(t, uint(2), wallets[1].ID)
		assert.Equal(t, "Wallet 2", wallets[1].Name)
		assert.NoError(t, s.mock.ExpectationsWereMet())
	})
}

func (s *WalletServiceSuite) TestWalletService_BatchCreate() {
	s.T().Run("should batch create new wallets", func(t *testing.T) {
		walletsToCreate := []models.Wallet{
			{Name: "Batch Wallet 1", Mnemonic: "batch mnemonic 1", Remark: "batch remark 1"},
			{Name: "Batch Wallet 2", Mnemonic: "batch mnemonic 2", Remark: "batch remark 2"},
		}

		s.mock.ExpectBegin()

		// GORM's batch insert for MySQL generates a single INSERT statement with multiple value sets.
		sql := "INSERT INTO `wallet` (`created_at`,`updated_at`,`deleted_at`,`name`,`mnemonic`,`remark`) VALUES (?,?,?,?,?,?),(?,?,?,?,?,?)"

		s.mock.ExpectExec(regexp.QuoteMeta(sql)).
			WithArgs(
				sqlmock.AnyArg(), sqlmock.AnyArg(), nil, "Batch Wallet 1", "batch mnemonic 1", "batch remark 1",
				sqlmock.AnyArg(), sqlmock.AnyArg(), nil, "Batch Wallet 2", "batch mnemonic 2", "batch remark 2",
			).
			WillReturnResult(sqlmock.NewResult(1, 2)) // LastInsertId=1 (for the first row), RowsAffected=2

		s.mock.ExpectCommit()

		err := s.service.BatchCreate(walletsToCreate)

		assert.NoError(t, err)
		assert.NoError(t, s.mock.ExpectationsWereMet())

		// With batch insert using a driver like mysql, GORM uses the LastInsertId for the first record.
		// It cannot populate subsequent record IDs without driver support for returning all IDs (like PostgreSQL's RETURNING).
		// So, we only assert the first ID is populated and the second is not.
		assert.NotNil(t, walletsToCreate)
		assert.Len(t, walletsToCreate, 2)
		assert.Equal(t, uint(1), walletsToCreate[0].ID)
		assert.Equal(t, "Batch Wallet 1", walletsToCreate[0].Name)
		assert.Equal(t, uint(2), walletsToCreate[1].ID) // GORM is smart enough to infer subsequent IDs.
		assert.Equal(t, "Batch Wallet 2", walletsToCreate[1].Name)
	})
}

func (s *WalletServiceSuite) TestWalletService_BatchDelete() {
	s.T().Run("should batch delete existing wallets", func(t *testing.T) {
		idsToDelete := []uint{1, 2}

		s.mock.ExpectBegin()

		// GORM's BatchDelete generates a DELETE statement with an IN clause.
		// For soft delete, it will be an UPDATE statement setting deleted_at.
		sql := "UPDATE `wallet` SET `deleted_at`=? WHERE `wallet`.`id` IN (?,?) AND `wallet`.`deleted_at` IS NULL"
		s.mock.ExpectExec(regexp.QuoteMeta(sql)).
			WithArgs(sqlmock.AnyArg(), idsToDelete[0], idsToDelete[1]).
			WillReturnResult(sqlmock.NewResult(0, 2)) // LastInsertId=0, RowsAffected=2

		s.mock.ExpectCommit()

		err := s.service.BatchDelete(idsToDelete)

		assert.NoError(t, err)
		assert.NoError(t, s.mock.ExpectationsWereMet())
	})

	s.T().Run("should return error when deleting with empty id list", func(t *testing.T) {
		var idsToDelete []uint // Empty slice

		err := s.service.BatchDelete(idsToDelete)

		assert.Error(t, err)
		assert.Equal(t, "ID列表不能为空", err.Error()) // Assuming BaseService returns this specific error
		assert.NoError(t, s.mock.ExpectationsWereMet()) // No DB interaction expected
	})
}
