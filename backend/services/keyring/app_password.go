package keyring

import (
	"errors"
	"fmt"

	"a8.tools/backend/utils"
	"github.com/zalando/go-keyring"
)

const (
	ServiceName = "a8.tools"
)

type AppPasswordService struct {
	failedAttempts int
}

func NewAppPasswordService() *AppPasswordService {
	return &AppPasswordService{}
}

func (a *AppPasswordService) SetPassword(key, password string) error {
	hashedPassword, err := utils.HashPassword(password)
	if err != nil {
		return err
	}
	// 使用系统凭据管理器存储
	return keyring.Set(ServiceName, key, hashedPassword)
}

func (a *AppPasswordService) VerifyPassword(key, password string) (bool, error) {
	hashedPassword, err := keyring.Get(ServiceName, key)
	if err != nil {
		return false, err
	}
	isValid := utils.VerifyPassword(password, hashedPassword)
	if isValid {
		a.failedAttempts = 0
		return true, nil
	} else {
		// 增加失败尝试计数
		a.failedAttempts++
		if a.failedAttempts >= 10 {
			return false, errors.New("too many failed attempts")
		}
	}
	return false, fmt.Errorf("密码错误，剩余尝试次数: %d", 5-a.failedAttempts)
}

// HasPassword 检查是否设置了密码
func (a *AppPasswordService) HasPassword(key string) bool {
	_, err := keyring.Get(ServiceName, key)
	return err == nil
}

// ClearPassword 清除存储的密码
func (a *AppPasswordService) ClearPassword(key string) error {
	return keyring.Delete(ServiceName, key)
}

func (a *AppPasswordService) ClearAppPassword() error {
	return keyring.DeleteAll(ServiceName)
}
