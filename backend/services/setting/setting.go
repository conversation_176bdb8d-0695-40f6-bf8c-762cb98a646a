package setting

import (
	"a8.tools/backend/db"
	"a8.tools/backend/models"
	"gorm.io/gorm"
)

type SettingService struct {
	database *gorm.DB
}

func NewSettingService() *SettingService {
	return &SettingService{
		database: db.DB,
	}
}

// Get 获取设置
func (s *SettingService) Get(key string) (string, error) {
	var setting models.Setting
	if err := s.database.Where("key = ?", key).First(&setting).Error; err != nil {
		return "", err
	}
	return setting.Value, nil
}

// Set 设置设置
func (s *SettingService) Set(key string, value string) error {
	var setting models.Setting
	// 尝试查找已存在的记录
	if err := s.database.Where("key = ?", key).First(&setting).Error; err != nil {
		// 如果记录不存在，创建新记录
		if err == gorm.ErrRecordNotFound {
			setting = models.Setting{Key: key, Value: value}
		} else {
			// 其他错误直接返回
			return err
		}
	} else {
		// 记录存在，更新值
		setting.Value = value
	}
	// Save 方法会根据是否有主键自动选择 Create 或 Update
	return s.database.Save(&setting).Error
}
