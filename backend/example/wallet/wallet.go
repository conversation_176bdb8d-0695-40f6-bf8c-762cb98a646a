package main

import (
	"log"

	"a8.tools/backend"
	"a8.tools/backend/models"
	"a8.tools/backend/services/wallet"
)

func main() {
	// 初始化后端，包括数据库连接等
	backend.Init()
	log.Println("Backend initialized.")

	walletService := wallet.NewWalletService()
	log.Println("WalletService initialized.")

	// // 1. 创建单个钱包
	log.Println("--- Testing Create ---")
	createdWallet := &models.Wallet{
		Name:     "My Test Wallet",
		Mnemonic: "example mnemonic phrase for testing",
		Remark:   "This is a wallet created for integration testing.",
	}
	err := walletService.Create(createdWallet)
	if err != nil {
		log.Fatalf("Error creating wallet: %v", err)
	}
	log.Printf("Wallet created successfully. ID: %d, Name: %s", createdWallet.ID, createdWallet.Name)

}
