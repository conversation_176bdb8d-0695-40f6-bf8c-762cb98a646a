package shared

import (
	"fmt"
	"time"
)

// ErrorCode represents different types of errors
type ErrorCode string

const (
	// Client errors
	ErrorCodeClientNotFound    ErrorCode = "CLIENT_NOT_FOUND"
	ErrorCodeClientInvalid     ErrorCode = "CLIENT_INVALID" 
	ErrorCodeVersionMismatch   ErrorCode = "VERSION_MISMATCH"
	
	// Subscription errors
	ErrorCodeSubscriptionNotFound    ErrorCode = "SUBSCRIPTION_NOT_FOUND"
	ErrorCodeSubscriptionInvalid     ErrorCode = "SUBSCRIPTION_INVALID"
	ErrorCodeSubscriptionExists      ErrorCode = "SUBSCRIPTION_EXISTS"
	ErrorCodeSubscriptionParseError  ErrorCode = "SUBSCRIPTION_PARSE_ERROR"
	
	// Process errors
	ErrorCodeProcessNotFound      ErrorCode = "PROCESS_NOT_FOUND"
	ErrorCodeProcessStartFailed   ErrorCode = "PROCESS_START_FAILED"
	ErrorCodeProcessStopFailed    ErrorCode = "PROCESS_STOP_FAILED"
	ErrorCodeProcessAlreadyRunning ErrorCode = "PROCESS_ALREADY_RUNNING"
	
	// Configuration errors
	ErrorCodeConfigInvalid        ErrorCode = "CONFIG_INVALID"
	ErrorCodeConfigNotFound       ErrorCode = "CONFIG_NOT_FOUND"
	ErrorCodeConfigGenerationFailed ErrorCode = "CONFIG_GENERATION_FAILED"
	
	// Port allocation errors
	ErrorCodePortNotAvailable     ErrorCode = "PORT_NOT_AVAILABLE"
	ErrorCodePortAlreadyAllocated ErrorCode = "PORT_ALREADY_ALLOCATED"
	ErrorCodePortReleaseError     ErrorCode = "PORT_RELEASE_ERROR"
	
	// Storage errors
	ErrorCodeStorageError         ErrorCode = "STORAGE_ERROR"
	ErrorCodeStorageNotFound      ErrorCode = "STORAGE_NOT_FOUND"
	ErrorCodeStoragePermission    ErrorCode = "STORAGE_PERMISSION"
	
	// Network errors
	ErrorCodeNetworkError         ErrorCode = "NETWORK_ERROR"
	ErrorCodeNetworkTimeout       ErrorCode = "NETWORK_TIMEOUT"
	ErrorCodeNetworkUnreachable   ErrorCode = "NETWORK_UNREACHABLE"
	
	// Validation errors
	ErrorCodeValidationFailed     ErrorCode = "VALIDATION_FAILED"
	ErrorCodeInvalidInput         ErrorCode = "INVALID_INPUT"
	ErrorCodeMissingRequired      ErrorCode = "MISSING_REQUIRED"
	
	// System errors
	ErrorCodeInternal             ErrorCode = "INTERNAL_ERROR"
	ErrorCodeNotImplemented       ErrorCode = "NOT_IMPLEMENTED"
	ErrorCodeServiceUnavailable   ErrorCode = "SERVICE_UNAVAILABLE"
)

// ProxyError represents a proxy-specific error
type ProxyError struct {
	Code      ErrorCode              `json:"code"`
	Message   string                 `json:"message"`
	Details   map[string]interface{} `json:"details,omitempty"`
	Cause     error                  `json:"-"`
	Timestamp time.Time              `json:"timestamp"`
	Component string                 `json:"component,omitempty"`
	Operation string                 `json:"operation,omitempty"`
}

// Error implements the error interface
func (e *ProxyError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("[%s] %s: %v", e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// Unwrap returns the underlying cause
func (e *ProxyError) Unwrap() error {
	return e.Cause
}

// WithDetails adds details to the error
func (e *ProxyError) WithDetails(key string, value interface{}) *ProxyError {
	if e.Details == nil {
		e.Details = make(map[string]interface{})
	}
	e.Details[key] = value
	return e
}

// WithComponent sets the component that generated the error
func (e *ProxyError) WithComponent(component string) *ProxyError {
	e.Component = component
	return e
}

// WithOperation sets the operation that failed
func (e *ProxyError) WithOperation(operation string) *ProxyError {
	e.Operation = operation
	return e
}

// NewProxyError creates a new proxy error
func NewProxyError(code ErrorCode, message string) *ProxyError {
	return &ProxyError{
		Code:      code,
		Message:   message,
		Timestamp: time.Now(),
	}
}

// NewProxyErrorWithCause creates a new proxy error with an underlying cause
func NewProxyErrorWithCause(code ErrorCode, message string, cause error) *ProxyError {
	return &ProxyError{
		Code:      code,
		Message:   message,
		Cause:     cause,
		Timestamp: time.Now(),
	}
}

// IsProxyError checks if an error is a ProxyError
func IsProxyError(err error) bool {
	_, ok := err.(*ProxyError)
	return ok
}

// GetProxyError extracts ProxyError from error, returns nil if not a ProxyError
func GetProxyError(err error) *ProxyError {
	if proxyErr, ok := err.(*ProxyError); ok {
		return proxyErr
	}
	return nil
}

// ErrorClassifier helps classify errors into categories
type ErrorClassifier struct{}

// IsRetryable determines if an error should be retried
func (ec *ErrorClassifier) IsRetryable(err error) bool {
	if proxyErr := GetProxyError(err); proxyErr != nil {
		switch proxyErr.Code {
		case ErrorCodeNetworkTimeout, ErrorCodeNetworkError, ErrorCodeServiceUnavailable:
			return true
		case ErrorCodePortNotAvailable, ErrorCodeProcessStartFailed:
			return true
		default:
			return false
		}
	}
	return false
}

// IsTemporary determines if an error is temporary
func (ec *ErrorClassifier) IsTemporary(err error) bool {
	if proxyErr := GetProxyError(err); proxyErr != nil {
		switch proxyErr.Code {
		case ErrorCodeNetworkTimeout, ErrorCodeNetworkError:
			return true
		case ErrorCodeServiceUnavailable:
			return true
		default:
			return false
		}
	}
	return false
}

// GetSeverity returns the severity level of an error
func (ec *ErrorClassifier) GetSeverity(err error) string {
	if proxyErr := GetProxyError(err); proxyErr != nil {
		switch proxyErr.Code {
		case ErrorCodeInternal, ErrorCodeStoragePermission:
			return "critical"
		case ErrorCodeProcessStartFailed, ErrorCodeProcessStopFailed:
			return "high"
		case ErrorCodeSubscriptionParseError, ErrorCodeConfigGenerationFailed:
			return "medium"
		case ErrorCodeValidationFailed, ErrorCodeInvalidInput:
			return "low"
		default:
			return "medium"
		}
	}
	return "unknown"
}

// Common error constructors for frequently used errors

// ErrClientNotFound creates a client not found error
func ErrClientNotFound(clientType string) *ProxyError {
	return NewProxyError(ErrorCodeClientNotFound, fmt.Sprintf("client not found: %s", clientType)).
		WithComponent("ClientDetector").
		WithDetails("client_type", clientType)
}

// ErrSubscriptionNotFound creates a subscription not found error
func ErrSubscriptionNotFound(id string) *ProxyError {
	return NewProxyError(ErrorCodeSubscriptionNotFound, fmt.Sprintf("subscription not found: %s", id)).
		WithComponent("SubscriptionManager").
		WithDetails("subscription_id", id)
}

// ErrSubscriptionExists creates a subscription already exists error
func ErrSubscriptionExists(url string) *ProxyError {
	return NewProxyError(ErrorCodeSubscriptionExists, fmt.Sprintf("subscription already exists: %s", url)).
		WithComponent("SubscriptionManager").
		WithDetails("url", url)
}

// ErrProcessNotFound creates a process not found error
func ErrProcessNotFound(id string) *ProxyError {
	return NewProxyError(ErrorCodeProcessNotFound, fmt.Sprintf("process not found: %s", id)).
		WithComponent("ProcessManager").
		WithDetails("process_id", id)
}

// ErrPortNotAvailable creates a port not available error
func ErrPortNotAvailable(port int) *ProxyError {
	return NewProxyError(ErrorCodePortNotAvailable, fmt.Sprintf("port not available: %d", port)).
		WithComponent("PortAllocator").
		WithDetails("port", port)
}

// ErrConfigInvalid creates a configuration invalid error
func ErrConfigInvalid(reason string) *ProxyError {
	return NewProxyError(ErrorCodeConfigInvalid, fmt.Sprintf("configuration invalid: %s", reason)).
		WithComponent("ConfigManager").
		WithDetails("reason", reason)
}

// ErrValidationFailed creates a validation failed error
func ErrValidationFailed(field string, reason string) *ProxyError {
	return NewProxyError(ErrorCodeValidationFailed, fmt.Sprintf("validation failed for field %s: %s", field, reason)).
		WithComponent("Validator").
		WithDetails("field", field).
		WithDetails("reason", reason)
}

// Global error classifier instance
var Classifier = &ErrorClassifier{}