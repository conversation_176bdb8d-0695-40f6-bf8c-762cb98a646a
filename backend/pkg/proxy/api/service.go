package api

import (
	"crypto/md5"
	"fmt"
	"time"

	"a8.tools/backend/pkg/proxy/core"
	"a8.tools/backend/pkg/proxy/infrastructure/parser"
	"a8.tools/backend/pkg/proxy/infrastructure/storage"
	"a8.tools/backend/utils/http"
)

type ProxyApi struct {
	subscriptionManager *core.SubscriptionManager
}

func NewProxyApi() *ProxyApi {
	// 创建存储适配器
	storageAdapter, err := storage.NewSubscriptionStorageAdapter("./data")
	if err != nil {
		// 这里暂时使用 panic，实际项目中应该返回错误
		panic(err)
	}

	// 创建订阅管理器
	subscriptionManager := core.NewSubscriptionManager(storageAdapter)

	return &ProxyApi{
		subscriptionManager: subscriptionManager,
	}
}

func (p *ProxyApi) ImportSubscription(url string) (*core.SubscriptionData, error) {
	// 从url获取订阅内容
	content, err := fetchSubscriptionContent(url)
	if err != nil {
		return nil, err
	}

	// 解析订阅内容
	subscriptionType, nodes, err := parser.ParseSubscription(content)
	if err != nil {
		return nil, err
	}
	// 转换节点格式
	nodeData := make([]core.NodeData, len(nodes))
	for i, node := range nodes {
		nodeData[i] = core.NodeData{
			ID:       generateNodeID(node.Server, node.Port), // 生成节点ID
			Name:     node.Name,
			Server:   node.Server,
			Port:     node.Port,
			Type:     node.Type,
			Method:   node.Method,
			Password: node.Password,
			Protocol: node.Protocol,
		}
	}

	// 创建订阅数据
	subscriptionData := &core.SubscriptionData{
		URL:       url,
		Type:      string(subscriptionType),
		Status:    core.StatusActive,
		Nodes:     nodeData,
		NodeCount: len(nodeData),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	return subscriptionData, nil
}

// fetchSubscriptionContent 从URL获取订阅内容
func fetchSubscriptionContent(url string) ([]byte, error) {

	client := http.NewClient()

	content, err := client.
		WithTimeout(30*time.Second).
		WithHeader("User-Agent", "clash-verge/v2.3.1").
		GetBytes(url)
	if err != nil {
		return nil, err
	}

	return content, nil
}

// generateNodeID 生成节点唯一ID
func generateNodeID(server string, port int) string {
	data := fmt.Sprintf("%s:%d", server, port)
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("%x", hash)[:8]
}
