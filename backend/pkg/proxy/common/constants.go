package common

import "time"

// 代理类型常量
const (
	ProxyTypeShadowsocks = "shadowsocks"
	ProxyTypeSS          = "ss"
	ProxyTypeVMess       = "vmess"
	ProxyTypeSSR         = "ssr"
	ProxyTypeTrojan      = "trojan"
	ProxyTypeHysteria    = "hysteria"
	ProxyTypeClash       = "clash"
)

// 状态常量
const (
	StatusActive   = "active"
	StatusInactive = "inactive"
	StatusError    = "error"
)

// 网络类型常量
const (
	NetworkTCP = "tcp"
	NetworkUDP = "udp"
	NetworkWS  = "ws"
	NetworkH2  = "h2"
)

// 默认配置值
const (
	DefaultLocalAddress = "127.0.0.1"
	DefaultTimeout      = 300 * time.Second
	DefaultRestartDelay = 5 * time.Second
	DefaultMaxRestarts  = 5
)

// 文件路径常量
const (
	ConfigDirName    = "proxy-configs"
	BackupDirName    = "backups"
	LogDirName       = "logs"
	TempConfigPrefix = "proxy-config-"
)

// 健康检查常量
const (
	DefaultHealthCheckInterval = 30 * time.Second
	DefaultHealthCheckTimeout  = 10 * time.Second
	DefaultFailureThreshold    = 3
	DefaultSuccessThreshold    = 1
	DefaultHTTPExpectedStatus  = 200
	DefaultTCPTimeout          = 5 * time.Second
)

// 健康状态常量
type HealthStatus string

const (
	HealthUnknown   HealthStatus = "unknown"
	HealthHealthy   HealthStatus = "healthy"
	HealthUnhealthy HealthStatus = "unhealthy"
	HealthChecking  HealthStatus = "checking"
)

// 性能监控常量
const (
	DefaultMetricsInterval = 15 * time.Second
	HighCPUThreshold       = 80.0
	HighMemoryThreshold    = 100 * 1024 * 1024 // 100MB
)
