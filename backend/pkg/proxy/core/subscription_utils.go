package core

import (
	"crypto/sha256"
	"fmt"
	"net/url"
	"strings"

	"a8.tools/backend/pkg/proxy/types"
)

// generateSubscriptionID 生成订阅源唯一ID
func (sm *SubscriptionManager) generateSubscriptionID(urlStr string) string {
	// 使用URL的SHA256哈希作为基础，确保相同URL生成相同ID
	hash := sha256.Sum256([]byte(urlStr))
	hashStr := fmt.Sprintf("%x", hash)

	// 取前8位作为短ID
	return hashStr[:8]
}

// detectSubscriptionType 检测订阅类型
func (sm *SubscriptionManager) detectSubscriptionType(urlStr string) types.SubscriptionType {
	// 解析URL以获取更多信息
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return types.TypeUnknown
	}

	// 基于URL路径和查询参数检测类型
	path := strings.ToLower(parsedURL.Path)
	query := parsedURL.RawQuery

	// V2Ray/VMess订阅通常包含这些特征
	if strings.Contains(path, "vmess") || strings.Contains(query, "vmess") {
		return types.TypeVMess
	}

	// Clash订阅通常有这些特征
	if strings.Contains(path, "clash") || strings.Contains(query, "clash") {
		return types.TypeClash
	}

	// Shadowsocks订阅
	if strings.Contains(path, "ss") || strings.Contains(query, "ss") || strings.Contains(path, "shadowsocks") {
		return types.TypeShadowsocks
	}

	// Trojan订阅
	if strings.Contains(path, "trojan") || strings.Contains(query, "trojan") {
		return types.TypeUnknown // TypeTrojan not defined in types package
	}

	// 默认返回未知类型
	return types.TypeUnknown
}