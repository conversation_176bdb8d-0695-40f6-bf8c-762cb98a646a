package core

import (
	"context"
	"sync"
	"time"
)

// ProcessManager defines the interface for managing proxy processes
type ProcessManager interface {
	// Process lifecycle management
	StartProcess(config *ProxyProcessConfig) error
	StopProcess(processID string) error
	RestartProcess(processID string) error
	
	// Process information
	GetProcess(processID string) (*ProxyProcessInfo, error)
	GetAllProcesses() []*ProxyProcessInfo
	GetProcessesByFilter(filter *ProcessFilter) []*ProxyProcessInfo
	GetProcessesByStatus(status ProcessStatus) []*ProxyProcessInfo
	
	// Batch operations
	StartBatch(processIDs []string) (*BatchOperation, error)
	StopBatch(processIDs []string) (*BatchOperation, error)
	RestartBatch(processIDs []string) (*BatchOperation, error)
	StartAllStopped() (*BatchOperation, error)
	StopAllRunning() (*BatchOperation, error)
	RestartAllRunning() (*BatchOperation, error)
	
	// Health monitoring
	CheckHealth(processID string) (*ProcessHealthInfo, error)
	CheckAllHealth() ([]*ProcessHealthInfo, error)
	GetProcessHealth(processID string) (*ProcessHealthInfo, error)
	GetAllProcessHealth() ([]*ProcessHealthInfo, error)
	
	// Performance monitoring
	GetPerformanceMetrics(processID string) (*PerformanceMetrics, error)
	GetAllPerformanceMetrics() ([]*PerformanceMetrics, error)
	
	// Event management
	Subscribe(subscriber EventSubscriber) string
	Unsubscribe(subscriptionID string) error
	
	// Utility methods
	IsProcessRunning(processID string) bool
	GetProcessCount() int
	GetRunningProcessCount() int
	
	// Cleanup and shutdown
	Shutdown(ctx context.Context) error
	GracefulShutdown(timeout time.Duration) error
}

// ProcessManagerConfig holds configuration for the process manager
type ProcessManagerConfig struct {
	// Working directory for processes
	WorkingDir string `json:"working_dir"`
	
	// Default configuration paths
	ConfigDir string `json:"config_dir"`
	LogDir    string `json:"log_dir"`
	
	// Monitoring configuration
	HealthCheckInterval    time.Duration `json:"health_check_interval"`
	PerformanceInterval    time.Duration `json:"performance_interval"`
	CleanupInterval        time.Duration `json:"cleanup_interval"`
	
	// Process limits
	MaxProcesses        int           `json:"max_processes"`
	ProcessStartTimeout time.Duration `json:"process_start_timeout"`
	ProcessStopTimeout  time.Duration `json:"process_stop_timeout"`
	
	// Restart configuration
	DefaultRestartDelay time.Duration `json:"default_restart_delay"`
	MaxRestartAttempts  int           `json:"max_restart_attempts"`
	
	// Resource limits
	MaxCPUUsage    float64 `json:"max_cpu_usage"`
	MaxMemoryUsage int64   `json:"max_memory_usage"`
	
	// Event configuration
	MaxEventHistory int           `json:"max_event_history"`
	EventBuffer     int           `json:"event_buffer"`
	EventTimeout    time.Duration `json:"event_timeout"`
}

// DefaultProcessManager provides the default implementation of ProcessManager
type DefaultProcessManager struct {
	config      *ProcessManagerConfig
	processes   map[string]*ProxyProcessInfo
	subscribers map[string]EventSubscriber
	
	// Monitoring
	healthChecker     interface{} // TODO: define proper health checker interface
	performanceMonitor *PerformanceMonitor
	
	// Event handling
	eventChan       chan *ProcessEvent
	eventBuffer     []*ProcessEvent
	shutdownChan    chan struct{}
	shutdownContext context.Context
	shutdownCancel  context.CancelFunc
	
	// Synchronization
	processMutex     sync.RWMutex
	subscriberMutex  sync.RWMutex
	eventMutex       sync.RWMutex
}

// NewDefaultProcessManager creates a new default process manager
func NewDefaultProcessManager(config *ProcessManagerConfig) (*DefaultProcessManager, error) {
	if config == nil {
		config = &ProcessManagerConfig{
			WorkingDir:             "/tmp/proxy",
			ConfigDir:              "/tmp/proxy/config",
			LogDir:                 "/tmp/proxy/logs",
			HealthCheckInterval:    30 * time.Second,
			PerformanceInterval:    10 * time.Second,
			CleanupInterval:        5 * time.Minute,
			MaxProcesses:           100,
			ProcessStartTimeout:    30 * time.Second,
			ProcessStopTimeout:     10 * time.Second,
			DefaultRestartDelay:    5 * time.Second,
			MaxRestartAttempts:     3,
			MaxCPUUsage:           80.0,
			MaxMemoryUsage:        1024 * 1024 * 1024, // 1GB
			MaxEventHistory:       1000,
			EventBuffer:           100,
			EventTimeout:          1 * time.Second,
		}
	}
	
	ctx, cancel := context.WithCancel(context.Background())
	
	manager := &DefaultProcessManager{
		config:          config,
		processes:       make(map[string]*ProxyProcessInfo),
		subscribers:     make(map[string]EventSubscriber),
		eventChan:       make(chan *ProcessEvent, config.EventBuffer),
		eventBuffer:     make([]*ProcessEvent, 0, config.MaxEventHistory),
		shutdownChan:    make(chan struct{}),
		shutdownContext: ctx,
		shutdownCancel:  cancel,
	}
	
	// Initialize health checker (simplified)
	manager.healthChecker = nil // TODO: implement health checker
	
	// Initialize performance monitor
	manager.performanceMonitor = &PerformanceMonitor{interval: config.PerformanceInterval}
	
	// TODO: Start background goroutines when methods are implemented
	// go manager.eventLoop()
	// go manager.monitoringLoop()
	// go manager.cleanupLoop()
	
	return manager, nil
}

// PerformanceMonitor provides performance monitoring capabilities
type PerformanceMonitor struct {
	interval time.Duration
}