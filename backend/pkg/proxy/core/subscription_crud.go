package core

import (
	"fmt"
	"strings"
	"time"
)

// AddSubscription 添加新的订阅源
func (sm *SubscriptionManager) AddSubscription(req AddSubscriptionRequest) (*AddSubscriptionResponse, error) {
	// 验证请求
	if err := sm.validateAddRequest(req); err != nil {
		return nil, err
	}

	// 检查URL是否已存在
	if err := sm.checkURLExists(req.URL); err != nil {
		return nil, err
	}

	// 检测订阅类型
	subscriptionType := sm.detectSubscriptionType(req.URL)

	// 生成订阅ID
	subscriptionID := sm.generateSubscriptionID(req.URL)

	// 创建订阅数据
	subscription := SubscriptionData{
		ID:          subscriptionID,
		Name:        req.Name,
		URL:         req.URL,
		Type:        string(subscriptionType),
		Status:      StatusActive,
		Description: req.Description,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		UpdatedBy:   req.UpdatedBy,
		Nodes:       []NodeData{},
		NodeCount:   0,
	}

	// 保存订阅
	if err := sm.storage.Add(subscription); err != nil {
		return nil, fmt.Errorf("failed to create subscription: %w", err)
	}

	return &AddSubscriptionResponse{
		ID:        subscription.ID,
		Name:      subscription.Name,
		URL:       subscription.URL,
		Type:      subscription.Type,
		CreatedAt: subscription.CreatedAt,
		Status:    string(subscription.Status),
	}, nil
}

// GetSubscription 获取指定ID的订阅源
func (sm *SubscriptionManager) GetSubscription(id string) (*SubscriptionData, error) {
	subscription, exists := sm.storage.GetByID(id)
	if !exists {
		return nil, fmt.Errorf("subscription not found: %s", id)
	}
	return subscription, nil
}

// GetAllSubscriptions 获取所有订阅源
func (sm *SubscriptionManager) GetAllSubscriptions() []SubscriptionData {
	return sm.storage.GetAll()
}

// UpdateSubscription 更新订阅源
func (sm *SubscriptionManager) UpdateSubscription(req UpdateSubscriptionRequest) (*UpdateSubscriptionResponse, error) {
	// 验证请求
	if err := sm.validateUpdateRequest(req); err != nil {
		return nil, err
	}

	subscription, exists := sm.storage.GetByID(req.ID)
	if !exists {
		return nil, fmt.Errorf("subscription not found: %s", req.ID)
	}

	// 更新字段
	if req.Name != "" {
		subscription.Name = req.Name
	}

	if req.Description != "" {
		subscription.Description = req.Description
	}

	subscription.UpdatedBy = req.UpdatedBy
	subscription.UpdatedAt = time.Now()

	// 保存更新
	if err := sm.storage.Update(*subscription); err != nil {
		return nil, fmt.Errorf("failed to update subscription: %w", err)
	}

	return &UpdateSubscriptionResponse{
		ID:        subscription.ID,
		Name:      subscription.Name,
		UpdatedAt: subscription.UpdatedAt,
		Success:   true,
		Message:   "Subscription updated successfully",
	}, nil
}

// UpdateSubscriptionMetadata 更新订阅源元数据（向后兼容）
func (sm *SubscriptionManager) UpdateSubscriptionMetadata(id string, name, description, updatedBy string) error {
	req := UpdateSubscriptionRequest{
		ID:          id,
		Name:        name,
		Description: description,
		UpdatedBy:   updatedBy,
	}

	_, err := sm.UpdateSubscription(req)
	return err
}

// DeleteSubscription 删除订阅源
func (sm *SubscriptionManager) DeleteSubscription(req DeleteSubscriptionRequest) (*DeleteSubscriptionResponse, error) {
	// 验证请求
	if req.ID == "" {
		return nil, fmt.Errorf("subscription ID is required")
	}

	// 检查订阅是否存在
	_, exists := sm.storage.GetByID(req.ID)
	if !exists {
		return &DeleteSubscriptionResponse{
			ID:      req.ID,
			Success: false,
			Message: "Subscription not found",
		}, nil
	}

	// 删除订阅
	if err := sm.storage.Delete(req.ID); err != nil {
		return nil, fmt.Errorf("failed to delete subscription: %w", err)
	}

	return &DeleteSubscriptionResponse{
		ID:      req.ID,
		Success: true,
		Message: "Subscription deleted successfully",
	}, nil
}

// ListSubscriptions 列出订阅源（带分页和过滤）
func (sm *SubscriptionManager) ListSubscriptions(req ListSubscriptionsRequest) (*ListSubscriptionsResponse, error) {
	// 获取所有订阅
	allSubscriptions := sm.storage.GetAll()

	// 应用过滤
	var filteredSubscriptions []SubscriptionData
	for _, sub := range allSubscriptions {
		if sm.matchesFilter(sub, req) {
			filteredSubscriptions = append(filteredSubscriptions, sub)
		}
	}

	// 分页
	total := len(filteredSubscriptions)
	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	start := (page - 1) * pageSize
	end := start + pageSize
	if start > total {
		start = total
	}
	if end > total {
		end = total
	}

	var pagedItems []SubscriptionData
	if start < total {
		pagedItems = filteredSubscriptions[start:end]
	}

	// 转换为响应格式
	items := make([]*SubscriptionData, len(pagedItems))
	for i, item := range pagedItems {
		items[i] = &item
	}

	return &ListSubscriptionsResponse{
		Items:      convertToListItems(pagedItems),
		TotalCount: total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: (total + pageSize - 1) / pageSize,
	}, nil
}

// matchesFilter 检查订阅是否匹配过滤条件
func (sm *SubscriptionManager) matchesFilter(sub SubscriptionData, req ListSubscriptionsRequest) bool {
	// 搜索过滤
	if req.Search != "" {
		searchTerm := strings.ToLower(req.Search)
		if !strings.Contains(strings.ToLower(sub.Name), searchTerm) &&
			!strings.Contains(strings.ToLower(sub.Description), searchTerm) {
			return false
		}
	}

	// 状态过滤
	if len(req.Status) > 0 {
		found := false
		for _, status := range req.Status {
			if string(sub.Status) == status {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// 类型过滤
	if len(req.Type) > 0 {
		found := false
		for _, subType := range req.Type {
			if sub.Type == subType {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	return true
}

// convertToListItems 将 SubscriptionData 转换为 SubscriptionListItem
func convertToListItems(subscriptions []SubscriptionData) []SubscriptionListItem {
	items := make([]SubscriptionListItem, len(subscriptions))
	for i, sub := range subscriptions {
		items[i] = SubscriptionListItem{
			ID:          sub.ID,
			Name:        sub.Name,
			URL:         sub.URL,
			Type:        sub.Type,
			Status:      string(sub.Status),
			NodeCount:   sub.NodeCount,
			CreatedAt:   sub.CreatedAt,
			UpdatedAt:   sub.UpdatedAt,
			Description: sub.Description,
			UpdatedBy:   sub.UpdatedBy,
		}
	}
	return items
}