package core

import (
	"time"
)

// SubscriptionStatus 订阅状态类型
type SubscriptionStatus string

const (
	StatusActive   SubscriptionStatus = "active"
	StatusInactive SubscriptionStatus = "inactive"
	StatusError    SubscriptionStatus = "error"
)

// SubscriptionData 订阅数据结构
type SubscriptionData struct {
	ID          string             `json:"id"`
	Name        string             `json:"name"`
	URL         string             `json:"url"`
	Type        string             `json:"type"`
	Status      SubscriptionStatus `json:"status"`
	Description string             `json:"description"`
	CreatedAt   time.Time          `json:"created_at"`
	UpdatedAt   time.Time          `json:"updated_at"`
	UpdatedBy   string             `json:"updated_by"`
	Nodes       []NodeData         `json:"nodes"`
	NodeCount   int                `json:"node_count"`
}

// NodeData 节点数据结构
type NodeData struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	Server   string `json:"server"`
	Port     int    `json:"port"`
	Type     string `json:"type"`
	Method   string `json:"method,omitempty"`
	Password string `json:"password,omitempty"`
	Protocol string `json:"protocol,omitempty"`
}

// SubscriptionStorage 定义订阅存储接口，避免直接依赖 infrastructure
type SubscriptionStorage interface {
	Add(subscription SubscriptionData) error
	GetByID(id string) (*SubscriptionData, bool)
	GetAll() []SubscriptionData
	Update(subscription SubscriptionData) error
	Delete(id string) error
}

// SubscriptionManager 订阅源管理器
type SubscriptionManager struct {
	storage SubscriptionStorage
}

// NewSubscriptionManager 创建新的订阅源管理器
func NewSubscriptionManager(storage SubscriptionStorage) *SubscriptionManager {
	return &SubscriptionManager{
		storage: storage,
	}
}