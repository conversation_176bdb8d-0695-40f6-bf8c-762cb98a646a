package core

import (
	"fmt"
	"net/url"
	"strings"
)

// validateAddRequest 验证添加订阅请求
func (sm *SubscriptionManager) validateAddRequest(req AddSubscriptionRequest) error {
	// 验证名称
	if strings.TrimSpace(req.Name) == "" {
		return ValidationError{Field: "name", Message: "subscription name is required"}
	}

	if len(req.Name) > 255 {
		return ValidationError{Field: "name", Message: "subscription name must be less than 255 characters"}
	}

	// 验证URL
	if strings.TrimSpace(req.URL) == "" {
		return ValidationError{Field: "url", Message: "subscription URL is required"}
	}

	if _, err := url.Parse(req.URL); err != nil {
		return ValidationError{Field: "url", Message: "invalid URL format"}
	}

	// 验证描述长度（如果提供）
	if req.Description != "" && len(req.Description) > 1000 {
		return ValidationError{Field: "description", Message: "description must be less than 1000 characters"}
	}

	return nil
}

// validateListRequest 验证列表查询请求
func (sm *SubscriptionManager) validateListRequest(req ListSubscriptionsRequest) error {
	// 验证分页参数
	if req.Page < 0 {
		return ValidationError{Field: "page", Message: "page must be non-negative"}
	}

	if req.PageSize < 0 {
		return ValidationError{Field: "page_size", Message: "page_size must be non-negative"}
	}

	if req.PageSize > 1000 {
		return ValidationError{Field: "page_size", Message: "page_size must not exceed 1000"}
	}

	// 验证排序字段
	if req.SortBy != "" {
		validSortFields := []string{"name", "created_at", "updated_at", "last_update", "node_count", "status"}
		if !contains(validSortFields, req.SortBy) {
			return ValidationError{Field: "sort_by", Message: "invalid sort field"}
		}
	}

	// 验证排序方向
	if req.SortOrder != "" && req.SortOrder != "asc" && req.SortOrder != "desc" {
		return ValidationError{Field: "sort_order", Message: "sort_order must be 'asc' or 'desc'"}
	}

	// 验证状态过滤
	validStatuses := []string{"active", "inactive", "error"}
	for _, status := range req.Status {
		if !contains(validStatuses, status) {
			return ValidationError{Field: "status", Message: fmt.Sprintf("invalid status: %s", status)}
		}
	}

	// 验证类型过滤
	validTypes := []string{"vmess", "shadowsocks", "clash", "trojan", "unknown"}
	for _, typ := range req.Type {
		if !contains(validTypes, typ) {
			return ValidationError{Field: "type", Message: fmt.Sprintf("invalid type: %s", typ)}
		}
	}

	return nil
}

// validateDeleteRequest 验证删除请求
func (sm *SubscriptionManager) validateDeleteRequest(req DeleteSubscriptionRequest) error {
	if strings.TrimSpace(req.ID) == "" {
		return ValidationError{Field: "id", Message: "subscription id is required"}
	}

	return nil
}

// validateUpdateRequest 验证更新请求
func (sm *SubscriptionManager) validateUpdateRequest(req UpdateSubscriptionRequest) error {
	if strings.TrimSpace(req.ID) == "" {
		return ValidationError{Field: "id", Message: "subscription id is required"}
	}

	// 如果提供了名称，验证名称
	if req.Name != "" {
		if strings.TrimSpace(req.Name) == "" {
			return ValidationError{Field: "name", Message: "subscription name cannot be empty"}
		}

		if len(req.Name) > 255 {
			return ValidationError{Field: "name", Message: "subscription name must be less than 255 characters"}
		}
	}

	// 验证描述长度（如果提供）
	if req.Description != "" && len(req.Description) > 1000 {
		return ValidationError{Field: "description", Message: "description must be less than 1000 characters"}
	}

	return nil
}

// checkURLExists 检查URL是否已存在
func (sm *SubscriptionManager) checkURLExists(urlStr string) error {
	subscriptions := sm.storage.GetAll()
	for _, sub := range subscriptions {
		if sub.URL == urlStr {
			return fmt.Errorf("subscription with URL already exists: %s", urlStr)
		}
	}
	return nil
}

// contains 工具函数：检查切片中是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}