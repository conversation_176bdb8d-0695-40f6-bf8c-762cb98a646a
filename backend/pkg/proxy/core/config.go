package core

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"a8.tools/backend/pkg/proxy/common"
	"a8.tools/backend/pkg/proxy/types"
)

// ConfigFormat represents the format of configuration files
type ConfigFormat string

const (
	ConfigFormatJSON   ConfigFormat = "json"
	ConfigFormatTOML   ConfigFormat = "toml"
	ConfigFormatYAML   ConfigFormat = "yaml"
	ConfigFormatCustom ConfigFormat = "custom"
)

// SSLocalConfig represents the configuration for shadowsocks-libev sslocal
type SSLocalConfig struct {
	Server     string `json:"server"`
	ServerPort int    `json:"server_port"`
	LocalPort  int    `json:"local_port"`
	LocalAddr  string `json:"local_address"`
	Password   string `json:"password"`
	Method     string `json:"method"`
	Timeout    int    `json:"timeout,omitempty"`
	FastOpen   bool   `json:"fast_open,omitempty"`
	Mode       string `json:"mode,omitempty"`
	Plugin     string `json:"plugin,omitempty"`
	PluginOpts string `json:"plugin_opts,omitempty"`
}

// V2RayConfig represents the configuration for V2Ray
type V2RayConfig struct {
	Inbounds  []V2RayInbound  `json:"inbounds"`
	Outbounds []V2RayOutbound `json:"outbounds"`
	Routing   *V2RayRouting   `json:"routing,omitempty"`
	DNS       *V2RayDNS       `json:"dns,omitempty"`
}

// V2RayInbound represents an inbound configuration
type V2RayInbound struct {
	Port     int                    `json:"port"`
	Protocol string                 `json:"protocol"`
	Settings map[string]interface{} `json:"settings,omitempty"`
	Tag      string                 `json:"tag,omitempty"`
}

// V2RayOutbound represents an outbound configuration
type V2RayOutbound struct {
	Protocol string                 `json:"protocol"`
	Settings map[string]interface{} `json:"settings,omitempty"`
	Tag      string                 `json:"tag,omitempty"`
}

// V2RayRouting represents routing configuration
type V2RayRouting struct {
	Rules []map[string]interface{} `json:"rules,omitempty"`
}

// V2RayDNS represents DNS configuration
type V2RayDNS struct {
	Servers []string `json:"servers,omitempty"`
}

// ConfigTemplate represents a configuration template
type ConfigTemplate struct {
	Name        string       `json:"name"`
	Description string       `json:"description"`
	Format      ConfigFormat `json:"format"`
	Template    string       `json:"template"`
	Variables   []string     `json:"variables,omitempty"`
}

// ConfigFileMetadata represents metadata for a configuration file
type ConfigFileMetadata struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	FilePath    string                 `json:"file_path"`
	Format      ConfigFormat           `json:"format"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	Size        int64                  `json:"size"`
	Checksum    string                 `json:"checksum"`
	IsActive    bool                   `json:"is_active"`
	Description string                 `json:"description,omitempty"`
	Tags        []string               `json:"tags,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// ConfigCreateOptions represents options for creating configuration files
type ConfigCreateOptions struct {
	Description string                 `json:"description,omitempty"`
	Tags        []string               `json:"tags,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	SetActive   bool                   `json:"set_active"`
	Validate    bool                   `json:"validate"`
}

// ConfigValidationResult represents the result of configuration validation
type ConfigValidationResult struct {
	Valid    bool     `json:"valid"`
	Errors   []string `json:"errors,omitempty"`
	Warnings []string `json:"warnings,omitempty"`
}

// ConfigManager manages configuration files and generation
type ConfigManager struct {
	baseDir   string
	metadata  map[string]*ConfigFileMetadata
	templates map[string]*ConfigTemplate
	mutex     sync.RWMutex
	metaMutex sync.RWMutex
}

// 确保ConfigManager实现了shared.ConfigManager接口
var _ interface {
	GenerateConfigFile(node *types.Node, localAddress string, localPort int, processID string) (string, error)
	ValidateConfig(configPath string, configType string) (*ConfigValidationResult, error)
	DeleteConfigFile(processID string) error
} = (*ConfigManager)(nil)

// NewConfigManager creates a new configuration manager
func NewConfigManager(baseDir string) (*ConfigManager, error) {
	if baseDir == "" {
		baseDir = filepath.Join(os.TempDir(), common.ConfigDirName)
	}

	if err := os.MkdirAll(baseDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create base directory: %w", err)
	}

	cm := &ConfigManager{
		baseDir:   baseDir,
		metadata:  make(map[string]*ConfigFileMetadata),
		templates: make(map[string]*ConfigTemplate),
	}

	// Load existing metadata
	if err := cm.loadMetadata(); err != nil {
		return nil, fmt.Errorf("failed to load metadata: %w", err)
	}

	// Initialize default templates
	cm.initializeDefaultTemplates()

	return cm, nil
}

// GenerateConfigFile generates configuration file for a proxy node
func (cm *ConfigManager) GenerateConfigFile(node *types.Node, localAddress string, localPort int, processID string) (string, error) {
	// Ensure directory exists
	if err := common.FileUtil.EnsureDir(cm.baseDir); err != nil {
		return "", fmt.Errorf("failed to create config directory: %w", err)
	}

	// Generate config file path
	filename := fmt.Sprintf("%s%s.json", common.TempConfigPrefix, processID)
	configFile := filepath.Join(cm.baseDir, filename)

	// Generate configuration content based on node type
	var configContent string
	var err error

	switch node.Type {
	case common.ProxyTypeShadowsocks, common.ProxyTypeSS:
		configContent, err = cm.generateShadowsocksConfig(node, localAddress, localPort)
	case common.ProxyTypeVMess:
		configContent, err = cm.generateVMessConfig(node, localAddress, localPort)
	case common.ProxyTypeSSR:
		configContent, err = cm.generateSSRConfig(node, localAddress, localPort)
	case common.ProxyTypeTrojan:
		configContent, err = cm.generateTrojanConfig(node, localAddress, localPort)
	default:
		return "", fmt.Errorf("unsupported node type: %s", node.Type)
	}

	if err != nil {
		return "", fmt.Errorf("failed to generate config content: %w", err)
	}

	// Write configuration file
	if err := os.WriteFile(configFile, []byte(configContent), 0644); err != nil {
		return "", fmt.Errorf("failed to write config file: %w", err)
	}

	// Create metadata
	metadata := &ConfigFileMetadata{
		ID:        processID,
		Name:      filename,
		FilePath:  configFile,
		Format:    ConfigFormatJSON,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		IsActive:  true,
	}

	// Calculate file info
	if stat, err := os.Stat(configFile); err == nil {
		metadata.Size = stat.Size()
	}

	// Calculate checksum
	if checksum, err := cm.calculateFileChecksum(configFile); err == nil {
		metadata.Checksum = checksum
	}

	// Store metadata
	cm.metaMutex.Lock()
	cm.metadata[processID] = metadata
	cm.metaMutex.Unlock()

	return configFile, nil
}

// generateShadowsocksConfig generates Shadowsocks configuration
func (cm *ConfigManager) generateShadowsocksConfig(node *types.Node, localAddress string, localPort int) (string, error) {
	config := &SSLocalConfig{
		Server:     node.Server,
		ServerPort: node.Port,
		LocalPort:  localPort,
		LocalAddr:  localAddress,
		Password:   node.Password,
		Method:     node.Method,
		Timeout:    int(common.DefaultTimeout.Seconds()),
		FastOpen:   false,
		Mode:       "tcp_and_udp",
	}

	// Handle plugin configuration
	if plugin, exists := node.Extra["plugin"]; exists {
		config.Plugin = plugin

		// Build plugin options
		var pluginOpts []string
		for key, value := range node.Extra {
			if key != "plugin" && key != "" {
				pluginOpts = append(pluginOpts, fmt.Sprintf("%s=%s", key, value))
			}
		}
		if len(pluginOpts) > 0 {
			config.PluginOpts = strings.Join(pluginOpts, ";")
		}
	}

	return cm.marshalConfig(config)
}

// generateVMessConfig generates VMess configuration
func (cm *ConfigManager) generateVMessConfig(node *types.Node, localAddress string, localPort int) (string, error) {
	security := node.Security
	if security == "" {
		security = "auto"
	}

	network := node.Network
	if network == "" {
		network = common.NetworkTCP
	}

	config := &V2RayConfig{
		Inbounds: []V2RayInbound{
			{
				Port:     localPort,
				Protocol: "socks",
				Settings: map[string]interface{}{
					"auth": "noauth",
					"udp":  true,
				},
				Tag: "socks-in",
			},
		},
		Outbounds: []V2RayOutbound{
			{
				Protocol: "vmess",
				Settings: map[string]interface{}{
					"vnext": []map[string]interface{}{
						{
							"address": node.Server,
							"port":    node.Port,
							"users": []map[string]interface{}{
								{
									"id":       node.UUID,
									"alterId":  node.AlterID,
									"security": security,
								},
							},
						},
					},
				},
				Tag: "proxy",
			},
		},
	}

	return cm.marshalConfig(config)
}

// generateSSRConfig generates ShadowsocksR configuration
func (cm *ConfigManager) generateSSRConfig(node *types.Node, localAddress string, localPort int) (string, error) {
	// SSR uses similar format to SS but with additional fields
	config := map[string]interface{}{
		"server":       node.Server,
		"server_port":  node.Port,
		"local_port":   localPort,
		"local_address": localAddress,
		"password":     node.Password,
		"method":       node.Method,
		"protocol":     node.Protocol,
		"obfs":         node.Obfs,
		"protocol_param": node.Extra["protocol_param"],
		"obfs_param":   node.Extra["obfs_param"],
		"timeout":      int(common.DefaultTimeout.Seconds()),
	}

	return cm.marshalConfig(config)
}

// generateTrojanConfig generates Trojan configuration
func (cm *ConfigManager) generateTrojanConfig(node *types.Node, localAddress string, localPort int) (string, error) {
	config := map[string]interface{}{
		"local_addr": localAddress,
		"local_port": localPort,
		"remote_addr": node.Server,
		"remote_port": node.Port,
		"password":   []string{node.Password},
		"ssl": map[string]interface{}{
			"verify": false,
			"sni":    node.Extra["sni"],
		},
	}

	return cm.marshalConfig(config)
}

// marshalConfig marshals configuration to JSON string
func (cm *ConfigManager) marshalConfig(config interface{}) (string, error) {
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal config: %w", err)
	}
	return string(data), nil
}

// calculateFileChecksum calculates MD5 checksum of a file
func (cm *ConfigManager) calculateFileChecksum(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	return fmt.Sprintf("%x", hash.Sum(nil)), nil
}

// loadMetadata loads existing metadata from storage
func (cm *ConfigManager) loadMetadata() error {
	// Implementation for loading metadata from persistent storage
	// This could be a JSON file, database, etc.
	return nil
}

// initializeDefaultTemplates initializes default configuration templates
func (cm *ConfigManager) initializeDefaultTemplates() {
	// Add default templates for common proxy types
	cm.templates["shadowsocks"] = &ConfigTemplate{
		Name:        "Shadowsocks",
		Description: "Standard Shadowsocks configuration template",
		Format:      ConfigFormatJSON,
		Template:    `{"server":"{{.Server}}","server_port":{{.Port}},"local_port":{{.LocalPort}},"password":"{{.Password}}","method":"{{.Method}}"}`,
		Variables:   []string{"Server", "Port", "LocalPort", "Password", "Method"},
	}

	cm.templates["vmess"] = &ConfigTemplate{
		Name:        "VMess",
		Description: "V2Ray VMess configuration template",
		Format:      ConfigFormatJSON,
		Template:    `{"inbounds":[{"port":{{.LocalPort}},"protocol":"socks"}],"outbounds":[{"protocol":"vmess","settings":{"vnext":[{"address":"{{.Server}}","port":{{.Port}},"users":[{"id":"{{.UUID}}"}]}]}}]}`,
		Variables:   []string{"Server", "Port", "LocalPort", "UUID"},
	}
}

// ValidateConfig validates a configuration file
func (cm *ConfigManager) ValidateConfig(configPath string, configType string) (*ConfigValidationResult, error) {
	result := &ConfigValidationResult{
		Valid:    true,
		Errors:   []string{},
		Warnings: []string{},
	}

	// Check if file exists
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		result.Valid = false
		result.Errors = append(result.Errors, "configuration file does not exist")
		return result, nil
	}

	// Read and parse the configuration
	data, err := os.ReadFile(configPath)
	if err != nil {
		result.Valid = false
		result.Errors = append(result.Errors, fmt.Sprintf("failed to read configuration file: %v", err))
		return result, nil
	}

	// Validate JSON format
	var config interface{}
	if err := json.Unmarshal(data, &config); err != nil {
		result.Valid = false
		result.Errors = append(result.Errors, fmt.Sprintf("invalid JSON format: %v", err))
		return result, nil
	}

	return result, nil
}

// DeleteConfigFile deletes a configuration file and its metadata
func (cm *ConfigManager) DeleteConfigFile(processID string) error {
	cm.metaMutex.Lock()
	defer cm.metaMutex.Unlock()

	metadata, exists := cm.metadata[processID]
	if !exists {
		return fmt.Errorf("configuration file not found for process: %s", processID)
	}

	// Delete the actual file
	if err := os.Remove(metadata.FilePath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to delete configuration file: %w", err)
	}

	// Remove from metadata
	delete(cm.metadata, processID)

	return nil
}