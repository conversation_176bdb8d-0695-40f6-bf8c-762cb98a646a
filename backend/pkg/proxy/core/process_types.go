package core

import (
	"os"
	"sync"
	"time"

	"a8.tools/backend/pkg/proxy/common"
	"a8.tools/backend/pkg/proxy/infrastructure/health"
	"a8.tools/backend/pkg/proxy/types"
)

// ProcessStatus represents the status of a proxy process
type ProcessStatus string

const (
	ProcessStatusStopped  ProcessStatus = "stopped"
	ProcessStatusStarting ProcessStatus = "starting"
	ProcessStatusRunning  ProcessStatus = "running"
	ProcessStatusStopping ProcessStatus = "stopping"
	ProcessStatusError    ProcessStatus = "error"
	ProcessStatusRestart  ProcessStatus = "restart"
)

// ProxyProcessConfig holds configuration for a proxy process
type ProxyProcessConfig struct {
	// Basic configuration
	ID           string      `json:"id"`
	Name         string      `json:"name"`
	Node         *types.Node `json:"node"`
	LocalAddress string      `json:"local_address"`
	LocalPort    int         `json:"local_port"`
	ConfigFile   string      `json:"config_file"`

	// Process configuration
	ExecutablePath string            `json:"executable_path"`
	Arguments      []string          `json:"arguments"`
	Environment    map[string]string `json:"environment"`
	WorkingDir     string            `json:"working_dir"`

	// Advanced options
	AutoRestart  bool                      `json:"auto_restart"`
	RestartDelay time.Duration             `json:"restart_delay"`
	MaxRestarts  int                       `json:"max_restarts"`
	HealthCheck  *health.HealthCheckConfig `json:"health_check"`
	Timeout      time.Duration             `json:"timeout"`
}

// ProxyProcessInfo represents runtime information about a proxy process
type ProxyProcessInfo struct {
	// Basic information
	Config    *ProxyProcessConfig `json:"config"`
	Status    ProcessStatus       `json:"status"`
	PID       int                 `json:"pid"`
	StartTime time.Time           `json:"start_time"`

	// Runtime statistics
	Runtime         time.Duration `json:"runtime"`
	RestartCount    int           `json:"restart_count"`
	LastRestartTime time.Time     `json:"last_restart_time"`

	// Performance metrics
	CPUUsage    float64 `json:"cpu_usage"`
	MemoryUsage int64   `json:"memory_usage"`

	// Connection statistics
	ConnectionCount  int64 `json:"connection_count"`
	BytesTransferred int64 `json:"bytes_transferred"`
	BytesReceived    int64 `json:"bytes_received"`

	// Health status
	HealthStatus    common.HealthStatus `json:"health_status"`
	LastHealthCheck time.Time           `json:"last_health_check"`
	HealthFailures  int                 `json:"health_failures"`

	// Error information
	LastError     error     `json:"last_error,omitempty"`
	ErrorCount    int       `json:"error_count"`
	LastErrorTime time.Time `json:"last_error_time"`

	// Process information
	Process *os.Process `json:"-"` // Not serialized

	// Internal synchronization
	mutex sync.RWMutex `json:"-"`
}

// ProcessEventType represents the type of process event
type ProcessEventType string

const (
	ProcessEventStarted   ProcessEventType = "started"
	ProcessEventStopped   ProcessEventType = "stopped"
	ProcessEventRestarted ProcessEventType = "restarted"
	ProcessEventError     ProcessEventType = "error"
	ProcessEventHealthy   ProcessEventType = "healthy"
	ProcessEventUnhealthy ProcessEventType = "unhealthy"
)

// ProcessEvent represents events that occur during process lifecycle
type ProcessEvent struct {
	ProcessID string           `json:"process_id"`
	Type      ProcessEventType `json:"type"`
	Timestamp time.Time        `json:"timestamp"`
	Message   string           `json:"message"`
	Data      interface{}      `json:"data,omitempty"`
}

// BatchOperationType represents the type of batch operation
type BatchOperationType string

const (
	BatchOperationStart   BatchOperationType = "start"
	BatchOperationStop    BatchOperationType = "stop"
	BatchOperationRestart BatchOperationType = "restart"
)

// BatchOperation represents a batch operation on multiple processes
type BatchOperation struct {
	ID          string              `json:"id"`
	Type        BatchOperationType  `json:"type"`
	ProcessIDs  []string            `json:"process_ids"`
	StartTime   time.Time           `json:"start_time"`
	EndTime     time.Time           `json:"end_time"`
	Status      string              `json:"status"`
	Results     map[string]error    `json:"results"`
	SuccessCount int                `json:"success_count"`
	FailureCount int                `json:"failure_count"`
}

// PerformanceMetrics represents performance metrics for a process
type PerformanceMetrics struct {
	ProcessID        string    `json:"process_id"`
	Timestamp        time.Time `json:"timestamp"`
	CPUUsage         float64   `json:"cpu_usage"`
	MemoryUsage      int64     `json:"memory_usage"`
	MemoryPercent    float64   `json:"memory_percent"`
	ConnectionCount  int64     `json:"connection_count"`
	BytesTransferred int64     `json:"bytes_transferred"`
	BytesReceived    int64     `json:"bytes_received"`
	NetworkLatency   time.Duration `json:"network_latency"`
	DiskUsage        int64     `json:"disk_usage"`
	FileDescriptors  int       `json:"file_descriptors"`
}

// ProcessFilter represents filter criteria for querying processes
type ProcessFilter struct {
	Status    []ProcessStatus `json:"status,omitempty"`
	NameQuery string          `json:"name_query,omitempty"`
	NodeType  string          `json:"node_type,omitempty"`
	PortRange *PortRange      `json:"port_range,omitempty"`
}

// PortRange represents a range of ports
type PortRange struct {
	Start int `json:"start"`
	End   int `json:"end"`
}

// ProcessSortOptions represents sorting options for process lists
type ProcessSortOptions struct {
	Field     string `json:"field"`     // "name", "status", "start_time", "cpu_usage", "memory_usage"
	Direction string `json:"direction"` // "asc", "desc"
}

// ProcessHealthInfo represents health information for a process
type ProcessHealthInfo struct {
	ProcessID       string              `json:"process_id"`
	HealthStatus    common.HealthStatus `json:"health_status"`
	LastCheck       time.Time           `json:"last_check"`
	CheckDuration   time.Duration       `json:"check_duration"`
	FailureCount    int                 `json:"failure_count"`
	ConsecutiveFails int                `json:"consecutive_fails"`
	ErrorMessage    string              `json:"error_message,omitempty"`
}

// EventSubscriber represents a process event subscriber
type EventSubscriber interface {
	OnProcessEvent(event *ProcessEvent)
}

// EventSubscriberFunc is a function type that implements EventSubscriber
type EventSubscriberFunc func(event *ProcessEvent)

func (f EventSubscriberFunc) OnProcessEvent(event *ProcessEvent) {
	f(event)
}