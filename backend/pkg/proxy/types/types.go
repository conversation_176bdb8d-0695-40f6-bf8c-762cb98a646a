package types

// SubscriptionType represents the type of subscription
type SubscriptionType string

const (
	TypeUnknown     SubscriptionType = "unknown"
	TypeShadowsocks SubscriptionType = "shadowsocks"
	TypeSSR         SubscriptionType = "ssr"
	TypeVMess       SubscriptionType = "vmess"
	TypeClash       SubscriptionType = "clash"
	TypeMixed       SubscriptionType = "mixed"
)

// Node represents a proxy node from a subscription
type Node struct {
	Type        string            `json:"type"`
	Name        string            `json:"name"`
	Server      string            `json:"server"`
	Port        int               `json:"port"`
	Password    string            `json:"password"`
	Method      string            `json:"method"`
	Protocol    string            `json:"protocol,omitempty"`     // For SSR
	ProtocolArg string            `json:"protocol_arg,omitempty"` // For SSR
	Obfs        string            `json:"obfs,omitempty"`         // For SSR
	ObfsArg     string            `json:"obfs_arg,omitempty"`     // For SSR
	UUID        string            `json:"uuid,omitempty"`         // For VMess
	AlterID     int               `json:"alter_id,omitempty"`     // For VMess
	Network     string            `json:"network,omitempty"`      // For VMess
	Security    string            `json:"security,omitempty"`     // For VMess
	Path        string            `json:"path,omitempty"`         // For VMess
	Host        string            `json:"host,omitempty"`         // For VMess
	TLS         bool              `json:"tls,omitempty"`          // For VMess
	Extra       map[string]string `json:"extra,omitempty"`        // For additional parameters
}
