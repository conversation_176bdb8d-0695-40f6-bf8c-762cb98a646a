package types

import (
	"encoding/json"
	"strings"
	"testing"
)

// TestSubscriptionType 测试订阅类型常量
func TestSubscriptionType(t *testing.T) {
	tests := []struct {
		name     string
		subType  SubscriptionType
		expected string
	}{
		{
			name:     "Unknown type",
			subType:  TypeUnknown,
			expected: "unknown",
		},
		{
			name:     "Shadowsocks type",
			subType:  TypeShadowsocks,
			expected: "shadowsocks",
		},
		{
			name:     "SSR type",
			subType:  TypeSSR,
			expected: "ssr",
		},
		{
			name:     "VMess type",
			subType:  TypeVMess,
			expected: "vmess",
		},
		{
			name:     "Clash type",
			subType:  TypeClash,
			expected: "clash",
		},
		{
			name:     "Mixed type",
			subType:  TypeMixed,
			expected: "mixed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if string(tt.subType) != tt.expected {
				t.Errorf("SubscriptionType value mismatch, expected %s, got %s", tt.expected, string(tt.subType))
			}
		})
	}
}

// TestNode 测试Node结构体
func TestNode(t *testing.T) {
	t.Run("Create basic Shadowsocks node", func(t *testing.T) {
		node := Node{
			Type:     "shadowsocks",
			Name:     "Test SS Server",
			Server:   "example.com",
			Port:     8388,
			Password: "password123",
			Method:   "aes-256-gcm",
		}

		if node.Type != "shadowsocks" {
			t.Errorf("Expected type 'shadowsocks', got '%s'", node.Type)
		}
		if node.Name != "Test SS Server" {
			t.Errorf("Expected name 'Test SS Server', got '%s'", node.Name)
		}
		if node.Server != "example.com" {
			t.Errorf("Expected server 'example.com', got '%s'", node.Server)
		}
		if node.Port != 8388 {
			t.Errorf("Expected port 8388, got %d", node.Port)
		}
	})

	t.Run("Create SSR node with protocol", func(t *testing.T) {
		node := Node{
			Type:        "ssr",
			Name:        "Test SSR Server",
			Server:      "ssr.example.com",
			Port:        443,
			Password:    "password456",
			Method:      "aes-256-cfb",
			Protocol:    "auth_aes128_md5",
			ProtocolArg: "12345",
			Obfs:        "tls1.2_ticket_auth",
			ObfsArg:     "example.com",
		}

		if node.Protocol != "auth_aes128_md5" {
			t.Errorf("Expected protocol 'auth_aes128_md5', got '%s'", node.Protocol)
		}
		if node.Obfs != "tls1.2_ticket_auth" {
			t.Errorf("Expected obfs 'tls1.2_ticket_auth', got '%s'", node.Obfs)
		}
	})

	t.Run("Create VMess node with UUID", func(t *testing.T) {
		node := Node{
			Type:     "vmess",
			Name:     "Test VMess Server",
			Server:   "vmess.example.com",
			Port:     443,
			UUID:     "550e8400-e29b-41d4-a716-************",
			AlterID:  64,
			Network:  "ws",
			Security: "auto",
			Path:     "/path",
			Host:     "example.com",
			TLS:      true,
		}

		if node.UUID != "550e8400-e29b-41d4-a716-************" {
			t.Errorf("Expected UUID '550e8400-e29b-41d4-a716-************', got '%s'", node.UUID)
		}
		if node.AlterID != 64 {
			t.Errorf("Expected AlterID 64, got %d", node.AlterID)
		}
		if !node.TLS {
			t.Error("Expected TLS to be true")
		}
	})

	t.Run("Node with extra parameters", func(t *testing.T) {
		node := Node{
			Type:   "custom",
			Name:   "Custom Node",
			Server: "custom.example.com",
			Port:   8080,
			Extra: map[string]string{
				"custom_param1": "value1",
				"custom_param2": "value2",
			},
		}

		if len(node.Extra) != 2 {
			t.Errorf("Expected 2 extra parameters, got %d", len(node.Extra))
		}
		if node.Extra["custom_param1"] != "value1" {
			t.Errorf("Expected custom_param1 'value1', got '%s'", node.Extra["custom_param1"])
		}
	})
}

// TestNodeJSON 测试Node的JSON序列化和反序列化
func TestNodeJSON(t *testing.T) {
	t.Run("JSON marshaling and unmarshaling", func(t *testing.T) {
		original := Node{
			Type:     "shadowsocks",
			Name:     "Test Server",
			Server:   "example.com",
			Port:     8388,
			Password: "password123",
			Method:   "aes-256-gcm",
			Extra: map[string]string{
				"plugin": "v2ray-plugin",
			},
		}

		// Marshal to JSON
		jsonData, err := json.Marshal(original)
		if err != nil {
			t.Fatalf("Failed to marshal node to JSON: %v", err)
		}

		// Unmarshal from JSON
		var restored Node
		err = json.Unmarshal(jsonData, &restored)
		if err != nil {
			t.Fatalf("Failed to unmarshal node from JSON: %v", err)
		}

		// Compare fields
		if restored.Type != original.Type {
			t.Errorf("Type mismatch: expected %s, got %s", original.Type, restored.Type)
		}
		if restored.Name != original.Name {
			t.Errorf("Name mismatch: expected %s, got %s", original.Name, restored.Name)
		}
		if restored.Server != original.Server {
			t.Errorf("Server mismatch: expected %s, got %s", original.Server, restored.Server)
		}
		if restored.Port != original.Port {
			t.Errorf("Port mismatch: expected %d, got %d", original.Port, restored.Port)
		}
		if restored.Extra["plugin"] != original.Extra["plugin"] {
			t.Errorf("Extra parameter mismatch: expected %s, got %s",
				original.Extra["plugin"], restored.Extra["plugin"])
		}
	})

	t.Run("JSON with omitempty fields", func(t *testing.T) {
		node := Node{
			Type:   "shadowsocks",
			Name:   "Minimal Node",
			Server: "example.com",
			Port:   8388,
		}

		jsonData, err := json.Marshal(node)
		if err != nil {
			t.Fatalf("Failed to marshal minimal node: %v", err)
		}

		jsonStr := string(jsonData)

		// Check that empty fields are omitted
		if strings.Contains(jsonStr, "protocol") {
			t.Error("Expected protocol field to be omitted in JSON")
		}
		if strings.Contains(jsonStr, "uuid") {
			t.Error("Expected uuid field to be omitted in JSON")
		}
		if strings.Contains(jsonStr, "tls") {
			t.Error("Expected tls field to be omitted in JSON")
		}
	})
}

// TestNodeValidation 测试Node字段验证
func TestNodeValidation(t *testing.T) {
	t.Run("Valid Shadowsocks node", func(t *testing.T) {
		node := Node{
			Type:     "shadowsocks",
			Name:     "Valid SS",
			Server:   "example.com",
			Port:     8388,
			Password: "password123",
			Method:   "aes-256-gcm",
		}

		if !isValidNode(node) {
			t.Error("Expected valid Shadowsocks node to be valid")
		}
	})

	t.Run("Invalid node - missing server", func(t *testing.T) {
		node := Node{
			Type: "shadowsocks",
			Name: "Invalid SS",
			Port: 8388,
		}

		if isValidNode(node) {
			t.Error("Expected node without server to be invalid")
		}
	})

	t.Run("Invalid node - invalid port", func(t *testing.T) {
		node := Node{
			Type:   "shadowsocks",
			Name:   "Invalid Port",
			Server: "example.com",
			Port:   0,
		}

		if isValidNode(node) {
			t.Error("Expected node with port 0 to be invalid")
		}
	})
}

// isValidNode 简单的节点验证函数（用于测试）
func isValidNode(node Node) bool {
	if node.Server == "" {
		return false
	}
	if node.Port <= 0 || node.Port > 65535 {
		return false
	}
	return true
}

// BenchmarkNodeCreation 基准测试节点创建
func BenchmarkNodeCreation(b *testing.B) {
	for i := 0; i < b.N; i++ {
		node := Node{
			Type:     "shadowsocks",
			Name:     "Benchmark Node",
			Server:   "example.com",
			Port:     8388,
			Password: "password123",
			Method:   "aes-256-gcm",
			Extra:    make(map[string]string),
		}
		_ = node
	}
}

// BenchmarkNodeJSONMarshal 基准测试JSON序列化
func BenchmarkNodeJSONMarshal(b *testing.B) {
	node := Node{
		Type:     "shadowsocks",
		Name:     "Benchmark Node",
		Server:   "example.com",
		Port:     8388,
		Password: "password123",
		Method:   "aes-256-gcm",
		Extra: map[string]string{
			"plugin": "v2ray-plugin",
			"param":  "value",
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := json.Marshal(node)
		if err != nil {
			b.Fatal(err)
		}
	}
}
