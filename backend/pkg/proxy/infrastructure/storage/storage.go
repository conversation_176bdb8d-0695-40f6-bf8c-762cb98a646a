package storage

import (
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"time"

	"a8.tools/backend/pkg/proxy/infrastructure/port"
	"a8.tools/backend/pkg/proxy/types"
)

// SubscriptionStorage 订阅源存储结构
type SubscriptionStorage struct {
	Version       string             `json:"version"`       // 存储格式版本
	LastModified  time.Time          `json:"last_modified"` // 最后修改时间
	Subscriptions []SubscriptionData `json:"subscriptions"` // 订阅源列表
	mutex         sync.RWMutex       `json:"-"`             // 读写锁，不序列化
	filePath      string             `json:"-"`             // 存储文件路径，不序列化
}

// SubscriptionData 订阅源数据结构
type SubscriptionData struct {
	ID          string                 `json:"id"`                    // 唯一标识符
	Name        string                 `json:"name"`                  // 订阅源名称
	URL         string                 `json:"url"`                   // 订阅链接
	Description string                 `json:"description,omitempty"` // 描述信息
	Type        types.SubscriptionType `json:"type"`                  // 订阅类型
	CreatedAt   time.Time              `json:"created_at"`            // 创建时间
	UpdatedAt   time.Time              `json:"updated_at"`            // 更新时间
	LastUpdate  *time.Time             `json:"last_update,omitempty"` // 最后拉取时间
	NodeCount   int                    `json:"node_count"`            // 节点数量
	Status      string                 `json:"status"`                // 状态
	ErrorMsg    string                 `json:"error_msg,omitempty"`   // 错误信息
	UpdatedBy   string                 `json:"updated_by,omitempty"`  // 更新者
	Nodes       []NodeData             `json:"nodes,omitempty"`       // 解析出的节点列表
}

// NodeData 节点数据结构
type NodeData struct {
	ID       string     `json:"id"`                  // 节点唯一标识
	Name     string     `json:"name"`                // 节点名称
	Server   string     `json:"server"`              // 服务器地址
	Port     int        `json:"port"`                // 端口
	Type     string     `json:"type"`                // 节点类型
	RawData  types.Node `json:"raw_data"`            // 原始节点数据
	Status   string     `json:"status"`              // 节点状态
	LastUsed *time.Time `json:"last_used,omitempty"` // 最后使用时间
}

// RuntimeData 运行时数据结构（内存中的状态，不持久化）
type RuntimeData struct {
	RunningInstances map[string]*InstanceInfo    `json:"-"` // 正在运行的代理实例
	PortAllocator    *port.AdvancedPortAllocator `json:"-"` // 端口分配器
	mutex            sync.RWMutex                `json:"-"` // 读写锁
}

// InstanceInfo 代理实例信息
type InstanceInfo struct {
	ID         string    `json:"id"`          // 实例ID
	NodeID     string    `json:"node_id"`     // 关联节点ID
	LocalPort  int       `json:"local_port"`  // 本地端口
	PID        int       `json:"pid"`         // 进程ID
	Status     string    `json:"status"`      // 状态
	ConfigPath string    `json:"config_path"` // 配置文件路径
	StartTime  time.Time `json:"start_time"`  // 启动时间
	ErrorMsg   string    `json:"error_msg"`   // 错误信息
}

// 状态常量定义
const (
	// 订阅源状态
	StatusActive   = "active"
	StatusInactive = "inactive"
	StatusError    = "error"

	// 节点状态
	NodeStatusAvailable = "available"
	NodeStatusRunning   = "running"
	NodeStatusError     = "error"

	// 实例状态
	InstanceStatusStarting = "starting"
	InstanceStatusRunning  = "running"
	InstanceStatusStopped  = "stopped"
	InstanceStatusFailed   = "failed"

	// 存储版本
	StorageVersion = "1.0"
)

// NewSubscriptionStorage 创建新的订阅源存储
func NewSubscriptionStorage(dataDir string) (*SubscriptionStorage, error) {
	filePath := filepath.Join(dataDir, "subscriptions.json")

	storage := &SubscriptionStorage{
		Version:       StorageVersion,
		LastModified:  time.Now(),
		Subscriptions: make([]SubscriptionData, 0),
		filePath:      filePath,
	}

	// 如果文件存在，则加载数据
	if _, err := os.Stat(filePath); err == nil {
		if err := storage.Load(); err != nil {
			return nil, fmt.Errorf("failed to load existing storage: %w", err)
		}
	}

	return storage, nil
}

// Load 从文件加载数据
func (s *SubscriptionStorage) Load() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	data, err := os.ReadFile(s.filePath)
	if err != nil {
		return fmt.Errorf("failed to read storage file: %w", err)
	}

	if err := json.Unmarshal(data, s); err != nil {
		return fmt.Errorf("failed to unmarshal storage data: %w", err)
	}

	return nil
}

// Save 保存数据到文件
func (s *SubscriptionStorage) Save() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.LastModified = time.Now()

	// 确保目录存在
	dir := filepath.Dir(s.filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to ensure directory: %w", err)
	}

	data, err := json.MarshalIndent(s, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal storage data: %w", err)
	}

	if err := os.WriteFile(s.filePath, data, 0644); err != nil {
		return fmt.Errorf("failed to write storage file: %w", err)
	}

	return nil
}

// GetAll 获取所有订阅源
func (s *SubscriptionStorage) GetAll() []SubscriptionData {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	result := make([]SubscriptionData, len(s.Subscriptions))
	copy(result, s.Subscriptions)
	return result
}

// GetByID 根据ID获取订阅源
func (s *SubscriptionStorage) GetByID(id string) (*SubscriptionData, bool) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	for i := range s.Subscriptions {
		if s.Subscriptions[i].ID == id {
			return &s.Subscriptions[i], true
		}
	}
	return nil, false
}

// Add 添加订阅源
func (s *SubscriptionStorage) Add(subscription SubscriptionData) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 检查ID是否已存在
	for _, sub := range s.Subscriptions {
		if sub.ID == subscription.ID {
			return fmt.Errorf("subscription with ID %s already exists", subscription.ID)
		}
	}

	s.Subscriptions = append(s.Subscriptions, subscription)
	return s.save()
}

// Update 更新订阅源
func (s *SubscriptionStorage) Update(subscription SubscriptionData) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for i := range s.Subscriptions {
		if s.Subscriptions[i].ID == subscription.ID {
			s.Subscriptions[i] = subscription
			return s.save()
		}
	}

	return fmt.Errorf("subscription with ID %s not found", subscription.ID)
}

// Delete 删除订阅源
func (s *SubscriptionStorage) Delete(id string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for i := range s.Subscriptions {
		if s.Subscriptions[i].ID == id {
			s.Subscriptions = append(s.Subscriptions[:i], s.Subscriptions[i+1:]...)
			return s.save()
		}
	}

	return fmt.Errorf("subscription with ID %s not found", id)
}

// save 内部保存方法（已持有锁）
func (s *SubscriptionStorage) save() error {
	s.LastModified = time.Now()

	data, err := json.MarshalIndent(s, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal storage data: %w", err)
	}

	if err := os.WriteFile(s.filePath, data, 0644); err != nil {
		return fmt.Errorf("failed to write storage file: %w", err)
	}

	return nil
}

// NewRuntimeData 创建新的运行时数据
func NewRuntimeData() *RuntimeData {
	config := &port.PortAllocatorConfig{
		Ranges: []port.PortRange{
			{Start: 8080, End: 8999},
			{Start: 9080, End: 9999},
		},
		DefaultAddress: "127.0.0.1",
	}
	allocator, _ := port.NewAdvancedPortAllocator(config)
	return &RuntimeData{
		RunningInstances: make(map[string]*InstanceInfo),
		PortAllocator:    allocator,
		mutex:            sync.RWMutex{},
	}
}

// AddInstance 添加运行实例
func (r *RuntimeData) AddInstance(instance *InstanceInfo) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	r.RunningInstances[instance.ID] = instance
}

// RemoveInstance 移除运行实例
func (r *RuntimeData) RemoveInstance(id string) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	delete(r.RunningInstances, id)
}

// GetAllInstances 获取所有运行实例
func (r *RuntimeData) GetAllInstances() map[string]*InstanceInfo {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	result := make(map[string]*InstanceInfo)
	for k, v := range r.RunningInstances {
		result[k] = v
	}
	return result
}

// BackupOptions 备份选项
type BackupOptions struct {
	IncludeHistory bool   `json:"include_history"` // 是否包含历史记录
	Compress       bool   `json:"compress"`        // 是否压缩
	BackupPath     string `json:"backup_path"`     // 备份路径
}

// BackupInfo 备份信息
type BackupInfo struct {
	BackupTime  time.Time `json:"backup_time"`  // 备份时间
	Version     string    `json:"version"`      // 版本信息
	DataVersion string    `json:"data_version"` // 数据版本
	RecordCount int       `json:"record_count"` // 记录数量
	FileSize    int64     `json:"file_size"`    // 文件大小
	Compressed  bool      `json:"compressed"`   // 是否压缩
	Checksum    string    `json:"checksum"`     // 校验和
	BackupPath  string    `json:"backup_path"`  // 备份文件路径
}

// RestoreOptions 恢复选项
type RestoreOptions struct {
	BackupPath     string `json:"backup_path"`     // 备份文件路径
	OverwriteExist bool   `json:"overwrite_exist"` // 是否覆盖现有数据
	ValidateData   bool   `json:"validate_data"`   // 是否验证数据完整性
	CreateBackup   bool   `json:"create_backup"`   // 恢复前是否创建当前数据的备份
}

// MigrationInfo 数据迁移信息
type MigrationInfo struct {
	FromVersion string    `json:"from_version"` // 源版本
	ToVersion   string    `json:"to_version"`   // 目标版本
	MigratedAt  time.Time `json:"migrated_at"`  // 迁移时间
	RecordCount int       `json:"record_count"` // 迁移记录数
}

// StorageStats 存储统计信息
type StorageStats struct {
	TotalSubscriptions  int       `json:"total_subscriptions"`  // 总订阅源数量
	ActiveSubscriptions int       `json:"active_subscriptions"` // 活跃订阅源数量
	TotalNodes          int       `json:"total_nodes"`          // 总节点数量
	LastModified        time.Time `json:"last_modified"`        // 最后修改时间
	FileSize            int64     `json:"file_size"`            // 文件大小
	DataVersion         string    `json:"data_version"`         // 数据版本
}

// Backup 创建数据备份
func (s *SubscriptionStorage) Backup(options BackupOptions) (*BackupInfo, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// 生成备份文件名
	timestamp := time.Now().Format("20060102_150405")
	backupFileName := fmt.Sprintf("subscriptions_backup_%s.json", timestamp)
	backupPath := options.BackupPath
	if backupPath == "" {
		backupPath = filepath.Join(filepath.Dir(s.filePath), "backups", backupFileName)
	}

	// 确保备份目录存在
	backupDir := filepath.Dir(backupPath)
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create backup directory: %w", err)
	}

	// 准备备份数据
	backupData := struct {
		*SubscriptionStorage
		BackupInfo BackupInfo `json:"backup_info"`
	}{
		SubscriptionStorage: s,
		BackupInfo: BackupInfo{
			BackupTime:  time.Now(),
			Version:     "1.0",
			DataVersion: s.Version,
			RecordCount: len(s.Subscriptions),
			BackupPath:  backupPath,
		},
	}

	// 序列化数据
	data, err := json.MarshalIndent(backupData, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal backup data: %w", err)
	}

	// 计算校验和
	checksum := fmt.Sprintf("%x", sha256.Sum256(data))
	backupData.BackupInfo.Checksum = checksum
	backupData.BackupInfo.FileSize = int64(len(data))

	// 重新序列化包含校验和的数据
	data, err = json.MarshalIndent(backupData, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal final backup data: %w", err)
	}

	// 写入备份文件
	if err := os.WriteFile(backupPath, data, 0644); err != nil {
		return nil, fmt.Errorf("failed to write backup file: %w", err)
	}

	backupInfo := backupData.BackupInfo
	backupInfo.FileSize = int64(len(data))

	return &backupInfo, nil
}

// Restore 从备份恢复数据
func (s *SubscriptionStorage) Restore(options RestoreOptions) (*MigrationInfo, error) {
	// 验证备份文件是否存在
	if _, err := os.Stat(options.BackupPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("backup file does not exist: %s", options.BackupPath)
	}

	// 读取备份文件
	data, err := os.ReadFile(options.BackupPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read backup file: %w", err)
	}

	// 解析备份数据
	var backupData struct {
		*SubscriptionStorage
		BackupInfo BackupInfo `json:"backup_info"`
	}

	if err := json.Unmarshal(data, &backupData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal backup data: %w", err)
	}

	// 验证数据完整性
	if options.ValidateData {
		if err := s.validateBackupData(&backupData); err != nil {
			return nil, fmt.Errorf("backup data validation failed: %w", err)
		}
	}

	// 创建当前数据备份（如果需要）
	if options.CreateBackup {
		backupOptions := BackupOptions{
			BackupPath: filepath.Join(filepath.Dir(s.filePath), "backups",
				fmt.Sprintf("pre_restore_backup_%s.json", time.Now().Format("20060102_150405"))),
		}
		if _, err := s.Backup(backupOptions); err != nil {
			return nil, fmt.Errorf("failed to create pre-restore backup: %w", err)
		}
	}

	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 记录迁移信息
	migrationInfo := &MigrationInfo{
		FromVersion: s.Version,
		ToVersion:   backupData.Version,
		MigratedAt:  time.Now(),
		RecordCount: len(backupData.Subscriptions),
	}

	// 恢复数据
	if options.OverwriteExist {
		s.Subscriptions = backupData.Subscriptions
	} else {
		// 合并数据，避免覆盖现有记录
		existingIDs := make(map[string]bool)
		for _, sub := range s.Subscriptions {
			existingIDs[sub.ID] = true
		}

		for _, sub := range backupData.Subscriptions {
			if !existingIDs[sub.ID] {
				s.Subscriptions = append(s.Subscriptions, sub)
			}
		}
	}

	s.Version = backupData.Version
	s.LastModified = time.Now()

	// 保存恢复后的数据
	if err := s.save(); err != nil {
		return nil, fmt.Errorf("failed to save restored data: %w", err)
	}

	return migrationInfo, nil
}

// validateBackupData 验证备份数据
func (s *SubscriptionStorage) validateBackupData(backupData *struct {
	*SubscriptionStorage
	BackupInfo BackupInfo `json:"backup_info"`
}) error {
	// 验证基本结构
	if backupData.SubscriptionStorage == nil {
		return fmt.Errorf("invalid backup data structure")
	}

	// 验证版本兼容性
	if backupData.Version == "" {
		return fmt.Errorf("missing data version in backup")
	}

	// 验证记录数量
	if len(backupData.Subscriptions) != backupData.BackupInfo.RecordCount {
		return fmt.Errorf("record count mismatch: expected %d, got %d",
			backupData.BackupInfo.RecordCount, len(backupData.Subscriptions))
	}

	// 验证每个订阅源的数据完整性
	for i, sub := range backupData.Subscriptions {
		if sub.ID == "" {
			return fmt.Errorf("subscription at index %d has empty ID", i)
		}
		if sub.Name == "" {
			return fmt.Errorf("subscription at index %d has empty name", i)
		}
		if sub.URL == "" {
			return fmt.Errorf("subscription at index %d has empty URL", i)
		}
	}

	return nil
}

// GetStats 获取存储统计信息
func (s *SubscriptionStorage) GetStats() (*StorageStats, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	stats := &StorageStats{
		TotalSubscriptions: len(s.Subscriptions),
		LastModified:       s.LastModified,
		DataVersion:        s.Version,
	}

	// 统计活跃订阅源和节点数量
	for _, sub := range s.Subscriptions {
		if sub.Status == StatusActive {
			stats.ActiveSubscriptions++
		}
		stats.TotalNodes += sub.NodeCount
	}

	// 获取文件大小
	if fileInfo, err := os.Stat(s.filePath); err == nil {
		stats.FileSize = fileInfo.Size()
	}

	return stats, nil
}

// CleanupOldBackups 清理旧备份文件
func (s *SubscriptionStorage) CleanupOldBackups(backupDir string, keepCount int) error {
	if keepCount <= 0 {
		return fmt.Errorf("keepCount must be positive")
	}

	// 读取备份目录
	entries, err := os.ReadDir(backupDir)
	if err != nil {
		if os.IsNotExist(err) {
			return nil // 目录不存在，无需清理
		}
		return fmt.Errorf("failed to read backup directory: %w", err)
	}

	// 过滤备份文件并按修改时间排序
	var backupFiles []struct {
		Name    string
		ModTime time.Time
		Path    string
	}

	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}

		name := entry.Name()
		if !strings.HasPrefix(name, "subscriptions_backup_") || !strings.HasSuffix(name, ".json") {
			continue
		}

		info, err := entry.Info()
		if err != nil {
			continue
		}

		backupFiles = append(backupFiles, struct {
			Name    string
			ModTime time.Time
			Path    string
		}{
			Name:    name,
			ModTime: info.ModTime(),
			Path:    filepath.Join(backupDir, name),
		})
	}

	// 如果备份文件数量不超过保留数量，无需清理
	if len(backupFiles) <= keepCount {
		return nil
	}

	// 按修改时间排序（最新的在前）
	sort.Slice(backupFiles, func(i, j int) bool {
		return backupFiles[i].ModTime.After(backupFiles[j].ModTime)
	})

	// 删除多余的备份文件
	for i := keepCount; i < len(backupFiles); i++ {
		if err := os.Remove(backupFiles[i].Path); err != nil {
			return fmt.Errorf("failed to remove old backup %s: %w", backupFiles[i].Name, err)
		}
	}

	return nil
}

// CompactStorage 压缩存储，移除无效数据
func (s *SubscriptionStorage) CompactStorage() (*MigrationInfo, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 移除无效的订阅源
	validSubscriptions := make([]SubscriptionData, 0, len(s.Subscriptions))

	for _, sub := range s.Subscriptions {
		// 基本验证
		if sub.ID == "" || sub.Name == "" || sub.URL == "" {
			continue // 跳过无效记录
		}

		// 清理节点数据
		validNodes := make([]NodeData, 0, len(sub.Nodes))
		for _, node := range sub.Nodes {
			if node.ID != "" && node.Name != "" && node.Server != "" && node.Port > 0 {
				validNodes = append(validNodes, node)
			}
		}

		// 更新节点列表和计数
		sub.Nodes = validNodes
		sub.NodeCount = len(validNodes)

		validSubscriptions = append(validSubscriptions, sub)
	}

	s.Subscriptions = validSubscriptions
	s.LastModified = time.Now()

	// 保存压缩后的数据
	if err := s.save(); err != nil {
		return nil, fmt.Errorf("failed to save compacted data: %w", err)
	}

	migrationInfo := &MigrationInfo{
		FromVersion: s.Version,
		ToVersion:   s.Version,
		MigratedAt:  time.Now(),
		RecordCount: len(s.Subscriptions),
	}

	return migrationInfo, nil
}
