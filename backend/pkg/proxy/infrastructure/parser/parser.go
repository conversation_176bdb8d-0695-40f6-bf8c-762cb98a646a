package parser

import (
	"encoding/base64"
	"errors"
	"fmt"
	"regexp"
	"strings"

	"a8.tools/backend/pkg/proxy/types"
)

// ParseSubscription detects the subscription type and parses the content
func ParseSubscription(content []byte) (types.SubscriptionType, []types.Node, error) {
	// Try to detect the subscription type
	subscriptionType := detectSubscriptionType(content)

	// For mixed content, try to parse all types
	if subscriptionType == types.TypeMixed {
		var allNodes []types.Node

		// Try to parse as SSR
		ssrNodes, _ := parseSSR(content)
		if len(ssrNodes) > 0 {
			allNodes = append(allNodes, ssrNodes...)
		}

		// Try to parse as VMess
		vmessNodes, _ := parseVMess(content)
		if len(vmessNodes) > 0 {
			allNodes = append(allNodes, vmessNodes...)
		}

		// Try to parse as Shadowsocks
		ssNodes, _ := parseShadowsocks(content)
		if len(ssNodes) > 0 {
			allNodes = append(allNodes, ssNodes...)
		}

		if len(allNodes) > 0 {
			return types.TypeMixed, allNodes, nil
		}

		return types.TypeUnknown, nil, errors.New("failed to parse mixed content")
	}

	// Parse the content based on the detected type
	var nodes []types.Node
	var err error

	switch subscriptionType {
	case types.TypeShadowsocks:
		nodes, err = parseShadowsocks(content)
	case types.TypeSSR:
		nodes, err = parseSSR(content)
	case types.TypeVMess:
		nodes, err = parseVMess(content)
	case types.TypeClash:
		nodes, err = parseClash(content)
	default:
		return types.TypeUnknown, nil, errors.New("unknown subscription type")
	}

	if err != nil {
		return subscriptionType, nil, err
	}

	return subscriptionType, nodes, nil
}

// detectSubscriptionType tries to determine the type of subscription
func detectSubscriptionType(content []byte) types.SubscriptionType {
	contentStr := string(content)

	// Check if it's a Clash configuration
	if strings.Contains(contentStr, "proxies:") {
		return types.TypeClash
	}

	// Check for protocol prefixes with more precise matching
	// Use regex to match actual protocol URLs, not just substrings
	hasVMess := strings.Contains(contentStr, "vmess://")
	hasSSR := strings.Contains(contentStr, "ssr://")
	// Only match ss:// at the beginning of a line or after whitespace
	hasSS := regexp.MustCompile(`(?:^|\s)ss://`).MatchString(contentStr)

	// If we have multiple types, it's mixed content
	if (hasVMess && hasSSR) || (hasVMess && hasSS) || (hasSSR && hasSS) {
		return types.TypeMixed
	}

	// Check for direct protocol identifiers first (without decoding)
	if hasVMess {
		return types.TypeVMess
	}

	if hasSSR {
		return types.TypeSSR
	}

	if hasSS {
		return types.TypeShadowsocks
	}

	// Try to decode base64 if needed
	decodedContent := tryBase64Decode(contentStr)
	if decodedContent != contentStr {
		contentStr = decodedContent

		// Check for mixed content after decoding
		hasVMess = strings.Contains(contentStr, "vmess://")
		hasSSR = strings.Contains(contentStr, "ssr://")
		hasSS = regexp.MustCompile(`(?:^|\s)ss://`).MatchString(contentStr)

		// If we have multiple types, it's mixed content
		if (hasVMess && hasSSR) || (hasVMess && hasSS) || (hasSSR && hasSS) {
			return types.TypeMixed
		}

		// Check again after decoding
		if hasVMess {
			return types.TypeVMess
		}

		if hasSSR {
			return types.TypeSSR
		}

		if hasSS {
			return types.TypeShadowsocks
		}
	}

	return types.TypeUnknown
}

// tryBase64Decode attempts to decode a base64 string
func tryBase64Decode(s string) string {
	// Remove any whitespace
	s = strings.TrimSpace(s)

	// Try standard base64 decoding
	decoded, err := base64.StdEncoding.DecodeString(s)
	if err == nil {
		return string(decoded)
	}

	// Try URL-safe base64 decoding
	decoded, err = base64.URLEncoding.DecodeString(s)
	if err == nil {
		return string(decoded)
	}

	// Try with padding if needed
	if len(s)%4 != 0 {
		padded := s + strings.Repeat("=", 4-len(s)%4)
		decoded, err = base64.StdEncoding.DecodeString(padded)
		if err == nil {
			return string(decoded)
		}
		decoded, err = base64.URLEncoding.DecodeString(padded)
		if err == nil {
			return string(decoded)
		}
	}

	// Return original if decoding fails
	return s
}

// parseShadowsocks parses Shadowsocks subscription content
func parseShadowsocks(content []byte) ([]types.Node, error) {
	contentStr := string(content)

	// Try to decode base64 if needed
	decodedContent := tryBase64Decode(contentStr)
	if decodedContent != contentStr {
		contentStr = decodedContent
	}

	// Split by newline to get individual server entries
	lines := strings.Split(contentStr, "\n")

	var nodes []types.Node
	ssRegex := regexp.MustCompile(`ss://([A-Za-z0-9+/=_-]+)@([^:]+):(\d+)(?:#(.+))?`)

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if !strings.HasPrefix(line, "ss://") {
			continue
		}

		matches := ssRegex.FindStringSubmatch(line)
		if len(matches) < 4 {
			continue
		}

		// Format: ss://base64(method:password)@server:port#tag
		var method, password, server, tag string
		var port int

		// Decode the base64 encoded method:password part
		decodedStr := tryBase64Decode(matches[1])
		if decodedStr == matches[1] {
			// Decoding failed, skip this line
			continue
		}

		// Parse method:password
		parts := strings.SplitN(decodedStr, ":", 2)
		if len(parts) != 2 {
			continue
		}

		method = parts[0]
		password = parts[1]
		server = matches[2]
		fmt.Sscanf(matches[3], "%d", &port)

		if len(matches) >= 5 && matches[4] != "" {
			tag = matches[4]
		}

		nodes = append(nodes, types.Node{
			Type:     "shadowsocks",
			Name:     tag,
			Server:   server,
			Port:     port,
			Password: password,
			Method:   method,
		})
	}

	return nodes, nil
}

// parseSSR parses ShadowsocksR subscription content
func parseSSR(content []byte) ([]types.Node, error) {
	contentStr := string(content)

	// Try to decode base64 if needed
	decodedContent := tryBase64Decode(contentStr)
	if decodedContent != contentStr {
		contentStr = decodedContent
	}

	// Split by newline to get individual server entries
	lines := strings.Split(contentStr, "\n")

	var nodes []types.Node

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if !strings.HasPrefix(line, "ssr://") {
			continue
		}

		// Remove the ssr:// prefix
		encodedStr := line[6:]

		// Decode the base64 string
		decodedBytes, err := base64.URLEncoding.DecodeString(encodedStr)
		if err != nil {
			// Try with padding
			if len(encodedStr)%4 != 0 {
				encodedStr = encodedStr + strings.Repeat("=", 4-len(encodedStr)%4)
				decodedBytes, err = base64.URLEncoding.DecodeString(encodedStr)
				if err != nil {
					continue
				}
			} else {
				continue
			}
		}

		// Format: server:port:protocol:method:obfs:base64pass/?obfsparam=base64param&protoparam=base64param&remarks=base64remarks&group=base64group
		decodedStr := string(decodedBytes)

		// Split the main part and the parameters
		parts := strings.SplitN(decodedStr, "/?", 2)
		mainPart := parts[0]
		var params string
		if len(parts) > 1 {
			params = parts[1]
		}

		// Parse the main part
		mainParts := strings.Split(mainPart, ":")
		if len(mainParts) < 6 {
			continue
		}

		server := mainParts[0]
		port := 0
		fmt.Sscanf(mainParts[1], "%d", &port)
		protocol := mainParts[2]
		method := mainParts[3]
		obfs := mainParts[4]

		// The password is base64 encoded
		passwordBase64 := mainParts[5]
		passwordBytes, err := base64.URLEncoding.DecodeString(passwordBase64)
		if err != nil {
			// Try with padding
			if len(passwordBase64)%4 != 0 {
				passwordBase64 = passwordBase64 + strings.Repeat("=", 4-len(passwordBase64)%4)
				passwordBytes, err = base64.URLEncoding.DecodeString(passwordBase64)
				if err != nil {
					continue
				}
			} else {
				continue
			}
		}
		password := string(passwordBytes)

		// Parse the parameters
		var obfsParam, protocolParam, remarks string
		if params != "" {
			paramPairs := strings.Split(params, "&")
			for _, pair := range paramPairs {
				kv := strings.SplitN(pair, "=", 2)
				if len(kv) != 2 {
					continue
				}

				key := kv[0]
				value := kv[1]

				// Decode the value if needed
				decodedValue, err := base64.URLEncoding.DecodeString(value)
				if err != nil {
					// Try with padding
					if len(value)%4 != 0 {
						value = value + strings.Repeat("=", 4-len(value)%4)
						decodedValue, err = base64.URLEncoding.DecodeString(value)
						if err != nil {
							decodedValue = []byte(value)
						}
					} else {
						decodedValue = []byte(value)
					}
				}

				switch key {
				case "obfsparam":
					obfsParam = string(decodedValue)
				case "protoparam":
					protocolParam = string(decodedValue)
				case "remarks":
					remarks = string(decodedValue)
				}
			}
		}

		nodes = append(nodes, types.Node{
			Type:        "ssr",
			Name:        remarks,
			Server:      server,
			Port:        port,
			Password:    password,
			Method:      method,
			Protocol:    protocol,
			ProtocolArg: protocolParam,
			Obfs:        obfs,
			ObfsArg:     obfsParam,
		})
	}

	return nodes, nil
}
