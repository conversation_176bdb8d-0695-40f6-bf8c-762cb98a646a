package port

import (
	"fmt"
	"sync"
	"time"
)

// AdvancedPortAllocator provides thread-safe port allocation with advanced features
type AdvancedPortAllocator struct {
	config      *PortAllocatorConfig
	allocations map[int]*PortAllocation // port -> allocation info
	portSet     map[int]bool            // All available ports
	mutex       sync.RWMutex            // Protects concurrent access

	// Advanced features
	availabilityChecker *PortAvailabilityChecker
	conflictHistory     map[int]*PortConflictInfo // port -> conflict info

	// Statistics
	stats struct {
		allocationCount int64
		releaseCount    int64
		failureCount    int64
		conflictCount   int64
	}
}

// NewAdvancedPortAllocator creates a new advanced port allocator
func NewAdvancedPortAllocator(config *PortAllocatorConfig) (*AdvancedPortAllocator, error) {
	if config == nil {
		config = &PortAllocatorConfig{
			Ranges: []PortRange{
				{Start: 8080, End: 8999}, // Default range
				{Start: 9080, End: 9999}, // Secondary range
			},
			DefaultAddress:    "127.0.0.1",
			AllocationTimeout: 5 * time.Second,
			RetryAttempts:     3,
			RetryInterval:     100 * time.Millisecond,
		}
	}

	if err := validateConfig(config); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	allocator := &AdvancedPortAllocator{
		config:              config,
		allocations:         make(map[int]*PortAllocation),
		portSet:             make(map[int]bool),
		availabilityChecker: NewPortAvailabilityChecker(config.AllocationTimeout),
		conflictHistory:     make(map[int]*PortConflictInfo),
	}

	// Initialize port set
	allocator.initializePortSet()

	return allocator, nil
}

// initializePortSet initializes the set of available ports
func (a *AdvancedPortAllocator) initializePortSet() {
	for _, portRange := range a.config.Ranges {
		for port := portRange.Start; port <= portRange.End; port++ {
			// Skip excluded ports
			if a.isPortExcluded(port) {
				continue
			}
			a.portSet[port] = true
		}
	}
}

// isPortExcluded checks if a port is in the excluded list
func (a *AdvancedPortAllocator) isPortExcluded(port int) bool {
	for _, excluded := range a.config.ExcludedPorts {
		if port == excluded {
			return true
		}
	}
	return false
}

// AllocatePort allocates an available port
func (a *AdvancedPortAllocator) AllocatePort(req AllocationRequest) (*AllocationResult, error) {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	// Use default address if not specified
	address := req.Address
	if address == "" {
		address = a.config.DefaultAddress
	}

	// Use default timeout if not specified
	timeout := req.Timeout
	if timeout == 0 {
		timeout = a.config.AllocationTimeout
	}

	// Find available port
	port, err := a.findAvailablePort(req.Range)
	if err != nil {
		a.stats.failureCount++
		return &AllocationResult{
			Success: false,
			Error:   err,
		}, err
	}

	// Check port availability with retries
	available, conflicts := a.checkPortAvailabilityWithRetries(port, address, timeout)
	if !available {
		a.stats.failureCount++
		a.recordPortConflict(port, "bind_failed")
		return &AllocationResult{
			Port:      port,
			Success:   false,
			Error:     fmt.Errorf("port %d is not available", port),
			Conflicts: conflicts,
		}, fmt.Errorf("port %d is not available", port)
	}

	// Create allocation
	allocation := &PortAllocation{
		Port:        port,
		ProcessID:   req.ProcessID,
		AllocatedAt: time.Now(),
		Address:     address,
	}

	// Store allocation
	a.allocations[port] = allocation
	a.stats.allocationCount++

	return &AllocationResult{
		Port:       port,
		Allocation: allocation,
		Success:    true,
	}, nil
}

// ReleasePort releases an allocated port
func (a *AdvancedPortAllocator) ReleasePort(port int) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	if _, exists := a.allocations[port]; !exists {
		return fmt.Errorf("port %d is not allocated", port)
	}

	delete(a.allocations, port)
	a.stats.releaseCount++

	return nil
}

// GetStats returns current allocation statistics
func (a *AdvancedPortAllocator) GetStats() *PortAllocatorStats {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	stats := &PortAllocatorStats{
		TotalPorts:      len(a.portSet),
		AllocatedPorts:  len(a.allocations),
		AvailablePorts:  len(a.portSet) - len(a.allocations),
		AllocationCount: a.stats.allocationCount,
		ReleaseCount:    a.stats.releaseCount,
		FailureCount:    a.stats.failureCount,
		ConflictCount:   a.stats.conflictCount,
		Allocations:     make(map[int]*PortAllocation),
		RangeStats:      make(map[string]*RangeStats),
		ConflictHistory: make(map[int]*PortConflictInfo),
	}

	// Copy allocations
	for port, allocation := range a.allocations {
		stats.Allocations[port] = allocation
	}

	// Copy conflict history
	for port, conflict := range a.conflictHistory {
		stats.ConflictHistory[port] = conflict
	}

	// Generate range statistics
	for i, portRange := range a.config.Ranges {
		rangeKey := fmt.Sprintf("range_%d", i)
		rangeStats := a.calculateRangeStats(portRange)
		stats.RangeStats[rangeKey] = rangeStats
	}

	return stats
}

// validateConfig validates the port allocator configuration
func validateConfig(config *PortAllocatorConfig) error {
	if len(config.Ranges) == 0 {
		return fmt.Errorf("at least one port range must be specified")
	}

	for i, portRange := range config.Ranges {
		if portRange.Start < 1 || portRange.Start > 65535 {
			return fmt.Errorf("invalid start port in range %d: %d", i, portRange.Start)
		}
		if portRange.End < 1 || portRange.End > 65535 {
			return fmt.Errorf("invalid end port in range %d: %d", i, portRange.End)
		}
		if portRange.Start > portRange.End {
			return fmt.Errorf("invalid port range %d: start (%d) > end (%d)", i, portRange.Start, portRange.End)
		}
	}

	if config.AllocationTimeout <= 0 {
		return fmt.Errorf("allocation timeout must be positive")
	}

	if config.RetryAttempts < 0 {
		return fmt.Errorf("retry attempts must be non-negative")
	}

	if config.RetryInterval < 0 {
		return fmt.Errorf("retry interval must be non-negative")
	}

	return nil
}