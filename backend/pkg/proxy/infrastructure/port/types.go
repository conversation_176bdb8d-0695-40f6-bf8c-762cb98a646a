package port

import (
	"time"
)

// PortRange defines a range of ports for allocation
type PortRange struct {
	Start int `json:"start"` // Starting port (inclusive)
	End   int `json:"end"`   // Ending port (inclusive)
}

// PortAllocation represents an allocated port with metadata
type PortAllocation struct {
	Port        int       `json:"port"`         // Allocated port number
	ProcessID   string    `json:"process_id"`   // ID of the process using this port
	AllocatedAt time.Time `json:"allocated_at"` // When the port was allocated
	Address     string    `json:"address"`      // Address the port is bound to (e.g., "127.0.0.1")
}

// PortAllocatorConfig defines configuration for the port allocator
type PortAllocatorConfig struct {
	Ranges            []PortRange   `json:"ranges"`             // Available port ranges
	ExcludedPorts     []int         `json:"excluded_ports"`     // Ports to never allocate
	DefaultAddress    string        `json:"default_address"`    // Default bind address
	AllocationTimeout time.Duration `json:"allocation_timeout"` // Timeout for port availability check
	RetryAttempts     int           `json:"retry_attempts"`     // Number of retry attempts for allocation
	RetryInterval     time.Duration `json:"retry_interval"`     // Interval between retry attempts
}

// PortAllocatorStats provides statistics about port allocation
type PortAllocatorStats struct {
	TotalPorts      int                       `json:"total_ports"`      // Total number of allocatable ports
	AllocatedPorts  int                       `json:"allocated_ports"`  // Number of currently allocated ports
	AvailablePorts  int                       `json:"available_ports"`  // Number of available ports
	AllocationCount int64                     `json:"allocation_count"` // Total number of allocations made
	ReleaseCount    int64                     `json:"release_count"`    // Total number of releases made
	FailureCount    int64                     `json:"failure_count"`    // Number of allocation failures
	ConflictCount   int64                     `json:"conflict_count"`   // Number of port conflicts detected
	Allocations     map[int]*PortAllocation   `json:"allocations"`      // Current allocations
	RangeStats      map[string]*RangeStats    `json:"range_stats"`      // Statistics per range
	ConflictHistory map[int]*PortConflictInfo `json:"conflict_history"` // Port conflict history
}

// RangeStats provides statistics for a specific port range
type RangeStats struct {
	Range           PortRange `json:"range"`
	TotalPorts      int       `json:"total_ports"`
	AllocatedPorts  int       `json:"allocated_ports"`
	AvailablePorts  int       `json:"available_ports"`
	AllocationCount int64     `json:"allocation_count"`
}

// PortConflictInfo stores information about port conflicts
type PortConflictInfo struct {
	Port           int       `json:"port"`
	ConflictCount  int       `json:"conflict_count"`
	LastConflictAt time.Time `json:"last_conflict_at"`
	ConflictType   string    `json:"conflict_type"` // "already_in_use", "bind_failed", etc.
}

// PortAvailabilityChecker provides port availability checking functionality
type PortAvailabilityChecker struct {
	timeout time.Duration
}

// AllocationRequest represents a port allocation request
type AllocationRequest struct {
	ProcessID string        // ID of the process requesting the port
	Address   string        // Preferred bind address (optional)
	Timeout   time.Duration // Allocation timeout (optional)
	Range     *PortRange    // Preferred port range (optional)
}

// AllocationResult represents the result of a port allocation
type AllocationResult struct {
	Port       int                `json:"port"`
	Allocation *PortAllocation    `json:"allocation"`
	Success    bool               `json:"success"`
	Error      error              `json:"error,omitempty"`
	Conflicts  []*PortConflictInfo `json:"conflicts,omitempty"`
}