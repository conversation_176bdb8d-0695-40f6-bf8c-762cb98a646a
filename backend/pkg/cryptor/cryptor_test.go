package cryptor

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// 测试加密器
func TestDefaultEncryptor(t *testing.T) {
	// 创建加密器
	encryptor := NewEncryptor("test-master-key")

	// 测试加密解密
	plainTexts := []string{
		"这是一条测试明文",
		"这是另一条测试明文",
		"",                           // 空字符串
		"12345678901234567890123456", // 长文本
		"特殊字符!@#$%^&*()_+{}[]",       // 特殊字符
	}

	for _, plainText := range plainTexts {
		// 加密
		cipherText, err := encryptor.Encrypt(plainText)
		if plainText == "" {
			// 对于空字符串，我们期望正常返回
			assert.NoError(t, err)
			assert.NotEmpty(t, cipherText)
			continue
		}

		assert.NoError(t, err)
		assert.NotEqual(t, plainText, cipherText)

		// 解密
		decrypted, err := encryptor.Decrypt(cipherText)
		assert.NoError(t, err)
		assert.Equal(t, plainText, decrypted)
	}

	// 测试错误情况
	// 1. 未初始化密钥
	emptyEncryptor := &DefaultEncryptor{}
	_, err := emptyEncryptor.Encrypt("test")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "encryption key not initialized")

	// 2. 解密无效数据
	_, err = encryptor.Decrypt("invalid-base64")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "base64 解码失败")

	// 3. 解密数据长度不足
	_, err = encryptor.Decrypt("AQ==") // 只有一个字节的base64数据
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "加密数据长度不足")
}

// 模拟加密器，用于测试错误情况
type MockEncryptor struct {
	ShouldEncryptFail bool
	ShouldDecryptFail bool
}

func (m *MockEncryptor) Encrypt(plainText string) (string, error) {
	if m.ShouldEncryptFail {
		return "", errors.New("模拟加密失败")
	}
	return "ENCRYPTED:" + plainText, nil
}

func (m *MockEncryptor) Decrypt(cipherText string) (string, error) {
	if m.ShouldDecryptFail {
		return "", errors.New("模拟解密失败")
	}
	return cipherText[10:], nil // 移除 "ENCRYPTED:" 前缀
}

// 定义测试用的模型
type TestUser struct {
	ID            uint   `gorm:"primarykey"`
	Name          string
	Email         string `encrypt:"true"`
	Password      string `encrypt:"true"`
	PlainField    string
	NestedField   TestNested  `gorm:"serializer:json"` // 使用JSON序列化存储复杂结构
	NestedPtr     *TestNested `gorm:"-"`              // 在GORM测试中忽略这些字段
	NestedSlice   []TestNested `gorm:"-"`
	NestedMap     map[string]TestNested `gorm:"-"`
	NestedPtrMap  map[string]*TestNested `gorm:"-"`
	NestedPtrList []*TestNested `gorm:"-"`
}

type TestNested struct {
	Secret  string `encrypt:"true" json:"secret"`
	Public  string `json:"public"`
	SubNest TestSubNested `json:"sub_nest"` // 在JSON中作为子字段
}

type TestSubNested struct {
	DeepSecret string `encrypt:"true" json:"deep_secret"`
}

// TestProcessTaggedFields 测试标签字段处理
func TestProcessTaggedFields(t *testing.T) {
	// 初始化加密器
	encryptor := NewEncryptor("test-key")

	// 创建测试用户
	user := &TestUser{
		ID:       1,
		Name:     "测试用户",
		Email:    "<EMAIL>",
		Password: "password123",
		NestedField: TestNested{
			Secret: "nested-secret",
			Public: "public-info",
			SubNest: TestSubNested{
				DeepSecret: "deep-secret-value",
			},
		},
		NestedPtr: &TestNested{
			Secret: "ptr-secret",
			SubNest: TestSubNested{
				DeepSecret: "ptr-deep-secret",
			},
		},
		NestedSlice: []TestNested{
			{
				Secret: "slice-secret-1",
				SubNest: TestSubNested{
					DeepSecret: "slice-deep-1",
				},
			},
			{
				Secret: "slice-secret-2",
				SubNest: TestSubNested{
					DeepSecret: "slice-deep-2",
				},
			},
		},
		NestedMap: map[string]TestNested{
			"key1": {
				Secret: "map-secret-1",
				SubNest: TestSubNested{
					DeepSecret: "map-deep-1",
				},
			},
			"key2": {
				Secret: "map-secret-2",
				SubNest: TestSubNested{
					DeepSecret: "map-deep-2",
				},
			},
		},
		NestedPtrMap: map[string]*TestNested{
			"ptr1": {
				Secret: "ptr-map-secret-1",
				SubNest: TestSubNested{
					DeepSecret: "ptr-map-deep-1",
				},
			},
		},
		NestedPtrList: []*TestNested{
			{
				Secret: "ptr-list-secret",
				SubNest: TestSubNested{
					DeepSecret: "ptr-list-deep",
				},
			},
		},
	}

	// 保存原始值用于后续比较
	originalEmail := user.Email
	originalPassword := user.Password
	originalNestedSecret := user.NestedField.Secret
	originalNestedDeepSecret := user.NestedField.SubNest.DeepSecret
	originalPtrSecret := user.NestedPtr.Secret
	originalPtrDeepSecret := user.NestedPtr.SubNest.DeepSecret
	originalSliceSecret1 := user.NestedSlice[0].Secret
	originalSliceDeepSecret1 := user.NestedSlice[0].SubNest.DeepSecret
	originalMapSecret1 := user.NestedMap["key1"].Secret
	originalMapDeepSecret1 := user.NestedMap["key1"].SubNest.DeepSecret
	originalPtrMapSecret := user.NestedPtrMap["ptr1"].Secret
	originalPtrMapDeepSecret := user.NestedPtrMap["ptr1"].SubNest.DeepSecret
	originalPtrListSecret := user.NestedPtrList[0].Secret
	originalPtrListDeepSecret := user.NestedPtrList[0].SubNest.DeepSecret

	// 加密处理
	err := ProcessTaggedFields(user, encryptor, true)
	assert.NoError(t, err)

	// 验证加密
	assert.NotEqual(t, originalEmail, user.Email)
	assert.NotEqual(t, originalPassword, user.Password)
	assert.NotEqual(t, originalNestedSecret, user.NestedField.Secret)
	assert.NotEqual(t, originalNestedDeepSecret, user.NestedField.SubNest.DeepSecret)
	assert.NotEqual(t, originalPtrSecret, user.NestedPtr.Secret)
	assert.NotEqual(t, originalPtrDeepSecret, user.NestedPtr.SubNest.DeepSecret)
	assert.NotEqual(t, originalSliceSecret1, user.NestedSlice[0].Secret)
	assert.NotEqual(t, originalSliceDeepSecret1, user.NestedSlice[0].SubNest.DeepSecret)
	assert.NotEqual(t, originalMapSecret1, user.NestedMap["key1"].Secret)
	assert.NotEqual(t, originalMapDeepSecret1, user.NestedMap["key1"].SubNest.DeepSecret)
	assert.NotEqual(t, originalPtrMapSecret, user.NestedPtrMap["ptr1"].Secret)
	assert.NotEqual(t, originalPtrMapDeepSecret, user.NestedPtrMap["ptr1"].SubNest.DeepSecret)
	assert.NotEqual(t, originalPtrListSecret, user.NestedPtrList[0].Secret)
	assert.NotEqual(t, originalPtrListDeepSecret, user.NestedPtrList[0].SubNest.DeepSecret)

	// 解密处理
	err = ProcessTaggedFields(user, encryptor, false)
	assert.NoError(t, err)

	// 验证解密后恢复原值
	assert.Equal(t, originalEmail, user.Email)
	assert.Equal(t, originalPassword, user.Password)
	assert.Equal(t, originalNestedSecret, user.NestedField.Secret)
	assert.Equal(t, originalNestedDeepSecret, user.NestedField.SubNest.DeepSecret)
	assert.Equal(t, originalPtrSecret, user.NestedPtr.Secret)
	assert.Equal(t, originalPtrDeepSecret, user.NestedPtr.SubNest.DeepSecret)
	assert.Equal(t, originalSliceSecret1, user.NestedSlice[0].Secret)
	assert.Equal(t, originalSliceDeepSecret1, user.NestedSlice[0].SubNest.DeepSecret)
	assert.Equal(t, originalMapSecret1, user.NestedMap["key1"].Secret)
	assert.Equal(t, originalMapDeepSecret1, user.NestedMap["key1"].SubNest.DeepSecret)
	assert.Equal(t, originalPtrMapSecret, user.NestedPtrMap["ptr1"].Secret)
	assert.Equal(t, originalPtrMapDeepSecret, user.NestedPtrMap["ptr1"].SubNest.DeepSecret)
	assert.Equal(t, originalPtrListSecret, user.NestedPtrList[0].Secret)
	assert.Equal(t, originalPtrListDeepSecret, user.NestedPtrList[0].SubNest.DeepSecret)

	// 测试错误处理
	mockEncryptor := &MockEncryptor{ShouldEncryptFail: true}
	err = ProcessTaggedFields(user, mockEncryptor, true)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "加密字段")

	mockEncryptor = &MockEncryptor{ShouldDecryptFail: true}
	err = ProcessTaggedFields(user, mockEncryptor, false)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "解密字段")
}

// 测试空值和nil处理
func TestNilHandling(t *testing.T) {
	encryptor := NewEncryptor("test-key")

	// nil指针
	var nilUser *TestUser = nil
	err := ProcessTaggedFields(nilUser, encryptor, true)
	assert.NoError(t, err)

	// nil加密器
	user := &TestUser{Email: "<EMAIL>"}
	err = ProcessTaggedFields(user, nil, true)
	assert.NoError(t, err)

	// nil嵌套指针
	user.NestedPtr = nil
	err = ProcessTaggedFields(user, encryptor, true)
	assert.NoError(t, err)

	// nil Map
	user.NestedMap = nil
	err = ProcessTaggedFields(user, encryptor, true)
	assert.NoError(t, err)

	// nil切片
	user.NestedSlice = nil
	err = ProcessTaggedFields(user, encryptor, true)
	assert.NoError(t, err)
}

// 测试上下文相关函数
func TestContextFunctions(t *testing.T) {
	encryptor := NewEncryptor("test-key")

	// 测试WithEncryptor和上下文传递
	ctx := context.Background()
	ctxWithEncryptor := WithEncryptor(ctx, encryptor)
	retrievedEncryptor, ok := ctxWithEncryptor.Value(ENCRYPTORKEY).(Encryptor)
	assert.True(t, ok)
	assert.NotNil(t, retrievedEncryptor)

	// 测试GORM会话相关函数
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(t, err)

	// 自动注册加密回调
	RegisterEncryptionCallbacks(db)

	// 测试设置和获取加密器
	dbWithEncryptor := SetEncryptor(db, encryptor)
	retrievedEncryptor = GetEncryptor(dbWithEncryptor)
	assert.NotNil(t, retrievedEncryptor)

	// 测试事务加密器传递
	txDB := WithTxEncryptor(dbWithEncryptor)
	retrievedEncryptor = GetEncryptor(txDB)
	assert.NotNil(t, retrievedEncryptor)
}

// 测试GORM回调集成
func TestGORMCallbacks(t *testing.T) {
	// 简化测试逻辑，只测试字段的加密和解密能力

	// 1. 测试创建操作中的加密功能
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // 静默模式，避免过多日志
	})

	assert.NoError(t, err, "创建数据库连接失败")

	// 设置加密器和注册回调
	encryptor := NewEncryptor("test-integration-key")
	db = SetEncryptor(db, encryptor)
	RegisterEncryptionCallbacks(db)

	// 创建表
	err = db.AutoMigrate(&TestUser{})
	assert.NoError(t, err, "创建数据表失败")

	// 定义测试数据
	testEmail := "<EMAIL>"
	testPassword := "password123"
	testNestedSecret := "nested-secret-value"

	// 准备测试数据
	user := &TestUser{
		Name:     "测试用户",
		Email:    testEmail,
		Password: testPassword,
		NestedField: TestNested{
			Secret: testNestedSecret,
		},
	}

	// 2. 测试创建时是否触发加密
	// 插入数据，应该触发BeforeCreate回调对数据加密
	err = db.Create(user).Error
	assert.NoError(t, err, "创建用户失败")

	// 直接查询原始数据，确认存储的是加密数据
	var rawData TestUser
	err = db.Raw("SELECT * FROM test_users WHERE id = ?", user.ID).Scan(&rawData).Error
	assert.NoError(t, err, "查询原始数据失败")

	// 验证数据已经被加密(存储的值与原始值不同)
	if len(rawData.Email) > 0 { // 只有当有数据返回时才验证
		assert.NotEqual(t, testEmail, rawData.Email, "邮箱字段应该已加密")
		assert.NotEqual(t, testPassword, rawData.Password, "密码字段应该已加密")
	}

	// 3. 测试查询时的解密功能
	// 简单查询，应该触发AfterFind回调自动解密
	var retrievedUser TestUser
	err = db.First(&retrievedUser, user.ID).Error
	assert.NoError(t, err, "查询用户失败")

	// 验证字段已被自动解密为原始值
	assert.Equal(t, testEmail, retrievedUser.Email, "邮箱应该已被解密")
	assert.Equal(t, testPassword, retrievedUser.Password, "密码应该已被解密")
	assert.Equal(t, testNestedSecret, retrievedUser.NestedField.Secret, "嵌套字段应该已被解密")
}

// 性能测试
func BenchmarkEncryption(b *testing.B) {
	encryptor := NewEncryptor("benchmark-key")
	text := "这是一个性能测试用的明文，需要反复加密解密"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		encrypted, _ := encryptor.Encrypt(text)
		_, _ = encryptor.Decrypt(encrypted)
	}
}

// 并发安全性测试
func TestConcurrency(t *testing.T) {
	encryptor := NewEncryptor("concurrent-test-key")

	done := make(chan bool)
	errs := make(chan error, 10)

	// 创建10个并发goroutine进行加密解密
	for i := 0; i < 10; i++ {
		go func(id int) {
			plainText := fmt.Sprintf("并发测试文本-%d", id)
			for j := 0; j < 100; j++ {
				encrypted, err := encryptor.Encrypt(plainText)
				if err != nil {
					errs <- fmt.Errorf("加密错误(goroutine %d): %v", id, err)
					return
				}

				decrypted, err := encryptor.Decrypt(encrypted)
				if err != nil {
					errs <- fmt.Errorf("解密错误(goroutine %d): %v", id, err)
					return
				}

				if decrypted != plainText {
					errs <- fmt.Errorf("数据不匹配(goroutine %d): 期望 %s, 得到 %s", id, plainText, decrypted)
					return
				}
			}
			done <- true
		}(i)
	}

	// 等待所有goroutine完成
	for i := 0; i < 10; i++ {
		select {
		case <-done:
			// 正常完成
		case err := <-errs:
			t.Fatal(err)
		}
	}
}
