package cryptor

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"reflect"

	"golang.org/x/crypto/pbkdf2"
	"gorm.io/gorm"
)

// 上下文键类型
type contextKey string

// 上下文键常量
const (
	ENCRYPTORKEY contextKey = "encryptor"
)

const (
	keyLength   = 32 // AES-256
	saltLength  = 32
	iterations  = 10000
	versionByte = 1 // 加密版本标识
)

// Encryptor 定义了加密器接口
type Encryptor interface {
	Encrypt(plainText string) (string, error)
	Decrypt(cipherText string) (string, error)
}

// DefaultEncryptor 默认加密器实现
type DefaultEncryptor struct {
	masterKey []byte
}

// NewEncryptor 创建新的加密器实例
func NewEncryptor(key string) *DefaultEncryptor {
	return &DefaultEncryptor{
		masterKey: []byte(key),
	}
}

// Encrypt 加密字符串
func (e *DefaultEncryptor) Encrypt(text string) (string, error) {
	if len(e.masterKey) == 0 {
		return "", errors.New("encryption key not initialized")
	}

	// 生成随机盐值
	salt := make([]byte, saltLength)
	if _, err := io.ReadFull(rand.Reader, salt); err != nil {
		return "", fmt.Errorf("生成盐值失败: %v", err)
	}

	// 使用 PBKDF2 派生密钥
	key := pbkdf2.Key(e.masterKey, salt, iterations, keyLength, sha256.New)

	// 创建 AES-GCM cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("创建 cipher 失败: %v", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("创建 GCM 失败: %v", err)
	}

	// 生成随机 nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("生成 nonce 失败: %v", err)
	}

	// 加密数据
	ciphertext := gcm.Seal(nonce, nonce, []byte(text), nil)

	// 组合最终的加密数据：版本 + 盐值 + 密文
	finalData := make([]byte, 1+len(salt)+len(ciphertext))
	finalData[0] = versionByte
	copy(finalData[1:], salt)
	copy(finalData[1+len(salt):], ciphertext)

	return base64.URLEncoding.EncodeToString(finalData), nil
}

// Decrypt 解密字符串
func (e *DefaultEncryptor) Decrypt(cryptoText string) (string, error) {
	if len(e.masterKey) == 0 {
		return "", errors.New("encryption key not initialized")
	}

	// 解码 base64
	data, err := base64.URLEncoding.DecodeString(cryptoText)
	if err != nil {
		return "", fmt.Errorf("base64 解码失败: %v", err)
	}

	// 验证数据长度
	if len(data) < 1+saltLength {
		return "", errors.New("加密数据长度不足")
	}

	// 验证版本
	if data[0] != versionByte {
		return "", fmt.Errorf("不支持的加密版本: %d", data[0])
	}

	// 提取盐值和密文
	salt := data[1 : 1+saltLength]
	ciphertext := data[1+saltLength:]

	// 使用 PBKDF2 派生密钥
	key := pbkdf2.Key(e.masterKey, salt, iterations, keyLength, sha256.New)

	// 创建 AES-GCM cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("创建 cipher 失败: %v", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("创建 GCM 失败: %v", err)
	}

	// 验证数据长度
	if len(ciphertext) < gcm.NonceSize() {
		return "", errors.New("密文长度不足")
	}

	// 提取 nonce 和实际密文
	nonce := ciphertext[:gcm.NonceSize()]
	ciphertext = ciphertext[gcm.NonceSize():]

	// 解密数据
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", fmt.Errorf("解密失败: %v", err)
	}

	return string(plaintext), nil
}

// BeforeCreate GORM 创建前回调 - 修改为同时处理接口和标签
func BeforeCreate(db *gorm.DB) {
	encryptor := GetEncryptor(db)
	if encryptor == nil {
		ctxEncryptor, ok := db.Statement.Context.Value(ENCRYPTORKEY).(Encryptor)
		if !ok {
			db.AddError(fmt.Errorf("未找到加密器"))
			return
		}
		encryptor = ctxEncryptor
	}

	// 处理带encrypt标签的字段
	if err := ProcessTaggedFields(db.Statement.Dest, encryptor, true); err != nil {
		db.AddError(err)
	}
}

// AfterFind GORM 查询后回调 - 修改为同时处理接口和标签
func AfterFind(db *gorm.DB) {
	encryptor := GetEncryptor(db)
	if encryptor == nil {
		ctxEncryptor, ok := db.Statement.Context.Value(ENCRYPTORKEY).(Encryptor)
		if !ok {
			db.AddError(fmt.Errorf("未找到加密器"))
			return
		}
		encryptor = ctxEncryptor
	}

	// 处理带encrypt标签的字段
	if err := ProcessTaggedFields(db.Statement.Dest, encryptor, false); err != nil {
		db.AddError(err)
	}
}

// BeforeUpdate GORM 更新前回调 - 修改为同时处理接口和标签
func BeforeUpdate(db *gorm.DB) {
	encryptor := GetEncryptor(db)
	if encryptor == nil {
		ctxEncryptor, ok := db.Statement.Context.Value(ENCRYPTORKEY).(Encryptor)
		if !ok {
			db.AddError(fmt.Errorf("未找到加密器"))
			return
		}
		encryptor = ctxEncryptor
	}

	// 处理带encrypt标签的字段
	if err := ProcessTaggedFields(db.Statement.Dest, encryptor, true); err != nil {
		db.AddError(err)
	}
}

// 注意: GORM中的Save操作实际上是根据记录是否存在而自动选择Create或Update
// 因此我们不需要单独注册BeforeSave回调，只需要注册BeforeCreate和BeforeUpdate即可

// RegisterEncryptionCallbacks 注册加密相关的GORM回调
func RegisterEncryptionCallbacks(db *gorm.DB) {
	// 注册创建前的加密回调
	db.Callback().Create().Before("gorm:create").Register("encrypt_fields_before_create", BeforeCreate)
	// 注册查询后的解密回调
	db.Callback().Query().After("gorm:after_find").Register("decrypt_fields_after_find", AfterFind)
	// 注册更新前的加密回调
	db.Callback().Update().Before("gorm:update").Register("encrypt_fields_before_update", BeforeUpdate)
	// 注意: GORM中的Save操作会自动调用Create或Update，因此不需要单独的Save回调
}

// WithEncryptor 创建带有加密器的上下文
func WithEncryptor(ctx context.Context, encryptor Encryptor) context.Context {
	return context.WithValue(ctx, ENCRYPTORKEY, encryptor)
}

// SetEncryptor 为GORM会话设置加密器
func SetEncryptor(db *gorm.DB, encryptor Encryptor) *gorm.DB {
	return db.Set(string(ENCRYPTORKEY), encryptor)
}

// GetEncryptor 从GORM会话中获取加密器
func GetEncryptor(db *gorm.DB) Encryptor {
	value, exists := db.Get(string(ENCRYPTORKEY))
	if !exists {
		return nil
	}
	return value.(Encryptor)
}

// WithTxEncryptor 在事务中传递加密器
func WithTxEncryptor(db *gorm.DB) *gorm.DB {
	// 从父会话获取加密器
	encryptor := GetEncryptor(db)
	if encryptor != nil {
		// 为事务设置相同的加密器
		return db.Session(&gorm.Session{
			Context: WithEncryptor(db.Statement.Context, encryptor),
		})
	}
	return db
}

// ProcessTaggedFields 处理标记为需要加密的字段
func ProcessTaggedFields(model interface{}, encryptor Encryptor, encrypt bool) error {
	if model == nil || encryptor == nil {
		return nil
	}

	val := reflect.ValueOf(model)
	if val.Kind() == reflect.Ptr {
		if val.IsNil() {
			return nil
		}
		val = val.Elem()
	}

	if val.Kind() != reflect.Struct {
		return nil
	}

	typ := val.Type()
	for i := 0; i < val.NumField(); i++ {
		fieldType := typ.Field(i)
		if !fieldType.IsExported() {
			continue
		}

		field := val.Field(i)
		if !field.IsValid() {
			continue
		}

		// 检查字段是否标记为需要加密
		if tag := fieldType.Tag.Get("encrypt"); tag == "true" && field.Kind() == reflect.String && field.CanSet() {
			if encrypt {
				// 加密
				plainText := field.String()
				if plainText != "" {
					cipherText, err := encryptor.Encrypt(plainText)
					if err != nil {
						return fmt.Errorf("加密字段 %s 失败: %w", fieldType.Name, err)
					}
					field.SetString(cipherText)
				}
			} else {
				// 解密
				cipherText := field.String()
				if cipherText != "" {
					plainText, err := encryptor.Decrypt(cipherText)
					if err != nil {
						return fmt.Errorf("解密字段 %s 失败: %w", fieldType.Name, err)
					}
					field.SetString(plainText)
				}
			}
			continue
		}

		// 处理内嵌字段
		switch field.Kind() {
		case reflect.Struct:
			if field.CanAddr() {
				if err := ProcessTaggedFields(field.Addr().Interface(), encryptor, encrypt); err != nil {
					return fmt.Errorf("处理内嵌结构体字段 %s 失败: %w", fieldType.Name, err)
				}
			}
		case reflect.Ptr:
			if !field.IsNil() {
				if err := ProcessTaggedFields(field.Interface(), encryptor, encrypt); err != nil {
					return fmt.Errorf("处理指针字段 %s 失败: %w", fieldType.Name, err)
				}
			}
		case reflect.Slice, reflect.Array:
			for j := 0; j < field.Len(); j++ {
				elem := field.Index(j)
				if elem.Kind() == reflect.Struct {
					if elem.CanAddr() {
						if err := ProcessTaggedFields(elem.Addr().Interface(), encryptor, encrypt); err != nil {
							return fmt.Errorf("处理切片/数组元素[%d]失败: %w", j, err)
						}
					}
				} else if elem.Kind() == reflect.Ptr {
					if !elem.IsNil() {
						if err := ProcessTaggedFields(elem.Interface(), encryptor, encrypt); err != nil {
							return fmt.Errorf("处理切片/数组指针元素[%d]失败: %w", j, err)
						}
					}
				}
			}
		case reflect.Map:
			if field.IsNil() {
				continue
			}
			
			// 获取所有键
			keys := field.MapKeys()
			for _, key := range keys {
				mapElem := field.MapIndex(key)
				
				// 对于Map，我们需要特殊处理因为MapIndex返回的值不能直接修改
				if mapElem.Kind() == reflect.Struct || (mapElem.Kind() == reflect.Ptr && !mapElem.IsNil()) {
					// 创建一个可修改的副本
					mapElemCopy := reflect.New(mapElem.Type()).Elem()
					mapElemCopy.Set(mapElem)
					
					var err error
					if mapElemCopy.Kind() == reflect.Ptr {
						err = ProcessTaggedFields(mapElemCopy.Interface(), encryptor, encrypt)
					} else if mapElemCopy.CanAddr() {
						err = ProcessTaggedFields(mapElemCopy.Addr().Interface(), encryptor, encrypt)
					}
					
					if err != nil {
						return fmt.Errorf("处理Map元素[%v]失败: %w", key.Interface(), err)
					}
					
					// 将修改后的值设置回Map
					field.SetMapIndex(key, mapElemCopy)
				}
			}
		}
	}

	return nil
}
