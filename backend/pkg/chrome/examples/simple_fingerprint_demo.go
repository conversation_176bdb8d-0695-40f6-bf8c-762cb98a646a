package main

import (
	"fmt"
	"log"
	"time"

	"a8.tools/backend/pkg/chrome"
	"a8.tools/backend/pkg/rod"
)

func demoFingerprint() {
	fmt.Println("🌟 Chrome 指纹浏览器演示")
	fmt.Println("================================")

	// 创建指纹浏览器管理器
	fmt.Println("📱 正在初始化指纹浏览器管理器...")
	fbm, err := chrome.NewFingerprintBrowserManager("./demo_browser_data")
	if err != nil {
		log.Fatalf("❌ 初始化失败: %v", err)
	}
	defer fbm.CloseAllSessions()

	// 生成随机指纹
	fmt.Println("🎯 正在生成随机指纹配置...")
		fingerprint, err := fbm.FingerprintManager.GenerateRandomFingerprint("windows")
	if err != nil {
		log.Fatalf("❌ 指纹生成失败: %v", err)
	}

	// 显示生成的指纹信息
	fmt.Printf("✅ 指纹生成成功!\n")
	fmt.Printf("   📱 用户代理: %s\n", fingerprint.UserAgent)
	fmt.Printf("   🌍 语言设置: %s\n", fingerprint.Language)
	fmt.Printf("   🕐 时区设置: %s\n", fingerprint.Timezone)
	fmt.Printf("   📺 屏幕分辨率: %dx%d\n", fingerprint.ScreenResolution.Width, fingerprint.ScreenResolution.Height)
	fmt.Printf("   💻 硬件并发: %d 核心\n", fingerprint.Hardware.HardwareConcurrency)
	fmt.Printf("   🔒 WebRTC模式: %s\n", fingerprint.WebRTC.Mode)
	fmt.Printf("   🎨 Canvas噪声: %t (级别: %.2f)\n", fingerprint.Canvas.NoiseEnabled, fingerprint.Canvas.NoiseLevel)

	// 创建指纹配置文件
	fmt.Println("\n📂 正在创建指纹配置文件...")
		profile, err := fbm.FingerprintManager.CreateFingerprintProfile(
		"演示配置",
		"指纹浏览器演示配置文件",
		fingerprint,
	)
	if err != nil {
		log.Fatalf("❌ 配置文件创建失败: %v", err)
	}
	fmt.Printf("✅ 配置文件创建成功! ID: %s\n", profile.Profile.ID)

	// 配置启动选项
	launchOptions := &chrome.LaunchOptionsFingerprint{
		LaunchOptions: &chrome.LaunchOptions{
			Headless:  false, // 显示浏览器窗口以便观察
			Timeout:   30 * time.Second,
			NoSandbox: true,
		},
		ApplyFingerprint:  true,
		StealthMode:       true,
		AntiDetectLevel:   "high",
		EnableAutomation:  true,
		AutomationTimeout: "60s",
		KeepSessionAlive:  false,
	}

	// 启动自动化会话
	fmt.Println("\n🚀 正在启动自动化会话...")
	session, err := fbm.LaunchWithAutomation(profile.Profile.ID, launchOptions)
	if err != nil {
		log.Fatalf("❌ 会话启动失败: %v", err)
	}
	fmt.Printf("✅ 会话启动成功! ID: %s\n", session.SessionID)

	// 创建演示任务计划
	fmt.Println("\n📋 正在创建演示任务...")
	plan, err := fbm.CreateAutomationPlan(session.SessionID, "指纹演示", "展示指纹浏览器的功能")
	if err != nil {
		log.Fatalf("❌ 任务计划创建失败: %v", err)
	}

	// 演示任务序列
	tasks := []struct {
		description string
		action      func() error
	}{
		{
			"🌐 访问指纹检测网站",
			func() error {
				return session.BrowserAutomation.AddNavigateTask(plan, "https://bot.sannysoft.com/", "访问机器人检测页面")
			},
		},
		{
			"⏳ 等待页面加载完成",
			func() error {
				session.BrowserAutomation.AddWaitVisibleTask(plan, "body", rod.SelectorQuery, "15s", "等待页面完全加载")
				return nil
			},
		},
		{
			"🕐 模拟人类阅读时间",
			func() error {
				session.BrowserAutomation.AddSleepTask(plan, "3s", "模拟用户阅读页面的时间")
				return nil
			},
		},
		{
			"🔍 检测用户代理信息",
			func() error {
				session.BrowserAutomation.AddEvaluateTask(plan,
					`document.querySelector('#user-agent') ? document.querySelector('#user-agent').textContent : navigator.userAgent`,
					"detected_user_agent", "获取检测到的用户代理")
				return nil
			},
		},
		{
			"🤖 检查WebDriver检测结果",
			func() error {
				session.BrowserAutomation.AddEvaluateTask(plan,
					`document.querySelector('#webdriver') ? document.querySelector('#webdriver').textContent : 'undefined'`,
					"webdriver_status", "检查WebDriver检测状态")
				return nil
			},
		},
		{
			"🔧 检查Chrome Runtime",
			func() error {
				session.BrowserAutomation.AddEvaluateTask(plan,
					`document.querySelector('#chrome-runtime') ? document.querySelector('#chrome-runtime').textContent : 'undefined'`,
					"chrome_runtime_status", "检查Chrome Runtime状态")
				return nil
			},
		},
		{
			"📷 截取检测结果截图",
			func() error {
				session.BrowserAutomation.AddFullScreenshotTask(plan, "fingerprint_demo_result.png", "保存检测结果截图")
				return nil
			},
		},
	}

	// 执行任务序列
	for _, task := range tasks {
		fmt.Printf("   %s\n", task.description)
		if err := task.action(); err != nil {
			log.Printf("⚠️ 任务失败: %v", err)
		}
	}

	// 执行完整的演示计划
	fmt.Println("\n⚡ 正在执行演示任务...")
	startTime := time.Now()
	err = fbm.ExecuteAutomationPlan(session.SessionID, plan)
	executionTime := time.Since(startTime)

	if err != nil {
		log.Printf("❌ 任务执行失败: %v", err)
	} else {
		fmt.Printf("✅ 任务执行成功! 耗时: %v\n", executionTime)
	}

	// 获取执行结果
	fmt.Println("\n📊 检测结果分析:")
	variables := session.BrowserAutomation.GetVariables()

	if userAgent, ok := variables["detected_user_agent"]; ok {
		fmt.Printf("   📱 检测到的用户代理: %v\n", userAgent)
	}

	if webdriverStatus, ok := variables["webdriver_status"]; ok {
		fmt.Printf("   🤖 WebDriver检测状态: %v\n", webdriverStatus)
		if webdriverStatus == "undefined" || webdriverStatus == "false" {
			fmt.Printf("   ✅ WebDriver检测: 成功规避\n")
		} else {
			fmt.Printf("   ❌ WebDriver检测: 被检测到\n")
		}
	}

	if chromeRuntime, ok := variables["chrome_runtime_status"]; ok {
		fmt.Printf("   🔧 Chrome Runtime状态: %v\n", chromeRuntime)
	}

	// 进行指纹测试
	fmt.Println("\n🧪 正在进行指纹效果测试...")
	testResults, err := fbm.TestFingerprint(session.SessionID, "")
	if err != nil {
		log.Printf("⚠️ 指纹测试失败: %v", err)
	} else {
		fmt.Printf("✅ 指纹测试完成!\n")
		fmt.Printf("   🎨 Canvas指纹: %s\n", testResults.CanvasFingerprint[:min(50, len(testResults.CanvasFingerprint))]+"...")
		fmt.Printf("   🖥️ WebGL指纹: %s\n", testResults.WebGLFingerprint)
		fmt.Printf("   ⏰ 测试时间: %s\n", testResults.TestedAt.Format("15:04:05"))
	}

	// 显示会话统计信息
	fmt.Println("\n📈 会话统计:")
	fmt.Printf("   🆔 会话ID: %s\n", session.SessionID)
	fmt.Printf("   📂 配置文件: %s\n", session.ProfileID)
	fmt.Printf("   📅 创建时间: %s\n", session.CreatedAt.Format("15:04:05"))
	fmt.Printf("   ⏱️ 最后活动: %s\n", session.LastActivity.Format("15:04:05"))
	fmt.Printf("   📊 任务数量: %d\n", session.TaskCount)
	fmt.Printf("   ❌ 错误数量: %d\n", session.ErrorCount)

	// 获取管理器统计信息
	fmt.Println("\n🌟 系统统计:")
	stats := fbm.GetStats()
	for key, value := range stats {
		if key != "fingerprint_stats" {
			fmt.Printf("   %s: %v\n", key, value)
		}
	}

	// 等待用户观察结果
	fmt.Println("\n⏳ 演示将在5秒后结束，请查看浏览器窗口和截图文件...")
	time.Sleep(5 * time.Second)

	// 清理会话
	fmt.Println("\n🧹 正在清理演示会话...")
	err = fbm.CloseSession(session.SessionID)
	if err != nil {
		log.Printf("⚠️ 会话清理失败: %v", err)
	} else {
		fmt.Printf("✅ 会话清理完成!\n")
	}

	fmt.Println("\n🎉 指纹浏览器演示完成!")
	fmt.Println("📁 检查当前目录下的 'fingerprint_demo_result.png' 查看检测结果截图")
	fmt.Println("📂 演示数据保存在 './demo_browser_data' 目录中")
}

// 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
