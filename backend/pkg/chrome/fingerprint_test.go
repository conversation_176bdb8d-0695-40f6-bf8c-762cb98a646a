package chrome

import (
	"encoding/json"
	"os"
	"testing"
	"time"
)

func TestFingerprintConfig_Validate(t *testing.T) {
	tests := []struct {
		name    string
		config  *FingerprintConfig
		wantErr bool
	}{
		{
			name: "Valid fingerprint config",
			config: &FingerprintConfig{
				UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
				ScreenResolution: ScreenConfig{
					Width:  1920,
					Height: 1080,
				},
				Hardware: HardwareConfig{
					HardwareConcurrency: 8,
				},
			},
			wantErr: false,
		},
		{
			name: "Missing user agent",
			config: &FingerprintConfig{
				UserAgent: "",
				ScreenResolution: ScreenConfig{
					Width:  1920,
					Height: 1080,
				},
				Hardware: HardwareConfig{
					HardwareConcurrency: 8,
				},
			},
			wantErr: true,
		},
		{
			name: "Invalid screen resolution",
			config: &FingerprintConfig{
				UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
				ScreenResolution: ScreenConfig{
					Width:  0,
					Height: 0,
				},
				Hardware: HardwareConfig{
					HardwareConcurrency: 8,
				},
			},
			wantErr: true,
		},
		{
			name: "Invalid hardware concurrency",
			config: &FingerprintConfig{
				UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
				ScreenResolution: ScreenConfig{
					Width:  1920,
					Height: 1080,
				},
				Hardware: HardwareConfig{
					HardwareConcurrency: 0,
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("FingerprintConfig.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestFingerprintConfig_ToJSON(t *testing.T) {
	config := &FingerprintConfig{
		UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		Language:  "en-US",
		Timezone:  "America/New_York",
		ScreenResolution: ScreenConfig{
			Width:  1920,
			Height: 1080,
		},
		Hardware: HardwareConfig{
			HardwareConcurrency: 8,
			Platform:            "Win32",
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Version:   "1.0",
	}

	data, err := config.ToJSON()
	if err != nil {
		t.Fatalf("ToJSON() failed: %v", err)
	}

	if len(data) == 0 {
		t.Error("ToJSON() returned empty data")
	}

	// Test that we can parse it back
	var parsed FingerprintConfig
	if err := json.Unmarshal(data, &parsed); err != nil {
		t.Errorf("Failed to parse JSON back: %v", err)
	}

	if parsed.UserAgent != config.UserAgent {
		t.Errorf("UserAgent mismatch: got %s, want %s", parsed.UserAgent, config.UserAgent)
	}
}

func TestFingerprintConfig_FromJSON(t *testing.T) {
	jsonData := `{
		"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		"language": "en-US",
		"timezone": "America/New_York",
		"screenResolution": {
			"width": 1920,
			"height": 1080,
			"deviceScaleFactor": 1.0,
			"colorDepth": 24,
			"pixelDepth": 24
		},
		"hardware": {
			"hardwareConcurrency": 8,
			"platform": "Win32"
		},
		"version": "1.0"
	}`

	var config FingerprintConfig
	err := config.FromJSON([]byte(jsonData))
	if err != nil {
		t.Fatalf("FromJSON() failed: %v", err)
	}

	if config.UserAgent != "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" {
		t.Errorf("UserAgent mismatch: got %s", config.UserAgent)
	}

	if config.ScreenResolution.Width != 1920 {
		t.Errorf("Screen width mismatch: got %d, want 1920", config.ScreenResolution.Width)
	}

	if config.Hardware.HardwareConcurrency != 8 {
		t.Errorf("Hardware concurrency mismatch: got %d, want 8", config.Hardware.HardwareConcurrency)
	}
}

func TestFingerprintConfig_Clone(t *testing.T) {
	original := &FingerprintConfig{
		UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		Language:  "en-US",
		Timezone:  "America/New_York",
		ScreenResolution: ScreenConfig{
			Width:  1920,
			Height: 1080,
		},
		Hardware: HardwareConfig{
			HardwareConcurrency: 8,
			Platform:            "Win32",
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Version:   "1.0",
	}

	cloned := original.Clone()
	if cloned == nil {
		t.Fatal("Clone() returned nil")
	}

	if cloned == original {
		t.Error("Clone() returned same instance")
	}

	if cloned.UserAgent != original.UserAgent {
		t.Errorf("UserAgent mismatch: got %s, want %s", cloned.UserAgent, original.UserAgent)
	}

	if cloned.ScreenResolution.Width != original.ScreenResolution.Width {
		t.Errorf("Screen width mismatch: got %d, want %d", cloned.ScreenResolution.Width, original.ScreenResolution.Width)
	}

	// Modify cloned to ensure they are independent
	cloned.UserAgent = "Different User Agent"
	if original.UserAgent == cloned.UserAgent {
		t.Error("Original and cloned configs are not independent")
	}
}

func TestFingerprintConfig_GetHash(t *testing.T) {
	config1 := &FingerprintConfig{
		UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		Language:  "en-US",
		Version:   "1.0",
	}

	config2 := &FingerprintConfig{
		UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		Language:  "en-US",
		Version:   "1.0",
	}

	config3 := &FingerprintConfig{
		UserAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
		Language:  "en-US",
		Version:   "1.0",
	}

	hash1 := config1.GetHash()
	hash2 := config2.GetHash()
	hash3 := config3.GetHash()

	if hash1 == "" {
		t.Error("GetHash() returned empty string")
	}

	if hash1 != hash2 {
		t.Error("Same configs should have same hash")
	}

	if hash1 == hash3 {
		t.Error("Different configs should have different hash")
	}
}

func TestFingerprintConfig_IsExpired(t *testing.T) {
	now := time.Now()

	config := &FingerprintConfig{
		UpdatedAt: now.Add(-2 * time.Hour),
	}

	// Test not expired
	if config.IsExpired(3 * time.Hour) {
		t.Error("Config should not be expired")
	}

	// Test expired
	if !config.IsExpired(1 * time.Hour) {
		t.Error("Config should be expired")
	}
}

func TestFingerprintConfig_UpdateTimestamp(t *testing.T) {
	config := &FingerprintConfig{
		UpdatedAt: time.Now().Add(-1 * time.Hour),
	}

	oldTime := config.UpdatedAt
	config.UpdateTimestamp()

	if !config.UpdatedAt.After(oldTime) {
		t.Error("UpdateTimestamp() should update the timestamp")
	}

	if time.Since(config.UpdatedAt) > time.Second {
		t.Error("UpdateTimestamp() should set recent timestamp")
	}
}

func TestFingerprintProfile_Creation(t *testing.T) {
	// Create a temporary directory for testing
	tempDir, err := os.MkdirTemp("", "fingerprint_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create profile manager
	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	// Create base profile
	baseProfile, err := pm.CreateProfile("Test Profile", "Test Description")
	if err != nil {
		t.Fatalf("Failed to create base profile: %v", err)
	}

	// Create fingerprint config
	fingerprint := &FingerprintConfig{
		UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		Language:  "en-US",
		Timezone:  "America/New_York",
		ScreenResolution: ScreenConfig{
			Width:  1920,
			Height: 1080,
		},
		Hardware: HardwareConfig{
			HardwareConcurrency: 8,
			Platform:            "Win32",
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Version:   "1.0",
	}

	// Create fingerprint profile
	now := time.Now()
	fpProfile := &FingerprintProfile{
		Profile:           baseProfile,
		Fingerprint:       fingerprint,
		AutomationEnabled: true,
		LastFingerprint:   &now,
		FingerprintHash:   fingerprint.GetHash(),
	}

	// Validate fingerprint profile
	if fpProfile.Profile == nil {
		t.Error("Fingerprint profile should have base profile")
	}

	if fpProfile.Fingerprint == nil {
		t.Error("Fingerprint profile should have fingerprint config")
	}

	if !fpProfile.AutomationEnabled {
		t.Error("Automation should be enabled by default")
	}

	if fpProfile.FingerprintHash == "" {
		t.Error("Fingerprint hash should not be empty")
	}

	if fpProfile.LastFingerprint == nil {
		t.Error("Last fingerprint time should be set")
	}
}

func TestScreenConfig_Validation(t *testing.T) {
	tests := []struct {
		name   string
		config ScreenConfig
		valid  bool
	}{
		{
			name: "Valid screen config",
			config: ScreenConfig{
				Width:             1920,
				Height:            1080,
				DeviceScaleFactor: 1.0,
				ColorDepth:        24,
				PixelDepth:        24,
			},
			valid: true,
		},
		{
			name: "High DPI screen",
			config: ScreenConfig{
				Width:             2560,
				Height:            1440,
				DeviceScaleFactor: 2.0,
				ColorDepth:        24,
				PixelDepth:        24,
			},
			valid: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Basic validation - screen should have positive dimensions
			if tt.config.Width <= 0 || tt.config.Height <= 0 {
				if tt.valid {
					t.Error("Expected valid config but dimensions are invalid")
				}
			} else {
				if !tt.valid {
					t.Error("Expected invalid config but dimensions are valid")
				}
			}
		})
	}
}

func TestWebRTCConfig_Modes(t *testing.T) {
	validModes := []string{"disabled", "default", "public_only"}

	for _, mode := range validModes {
		config := WebRTCConfig{
			Mode:            mode,
			LocalIPHandling: "default",
		}

		if config.Mode != mode {
			t.Errorf("WebRTC mode mismatch: got %s, want %s", config.Mode, mode)
		}
	}
}

func TestCanvasConfig_NoiseSettings(t *testing.T) {
	config := CanvasConfig{
		NoiseEnabled:    true,
		NoiseLevel:      0.1,
		RandomValues:    true,
		ConsistentNoise: true,
		NoiseType:       "gaussian",
	}

	if !config.NoiseEnabled {
		t.Error("Noise should be enabled")
	}

	if config.NoiseLevel <= 0 || config.NoiseLevel > 1 {
		t.Errorf("Noise level should be between 0 and 1, got %f", config.NoiseLevel)
	}

	validNoiseTypes := []string{"uniform", "gaussian"}
	found := false
	for _, validType := range validNoiseTypes {
		if config.NoiseType == validType {
			found = true
			break
		}
	}
	if !found {
		t.Errorf("Invalid noise type: %s", config.NoiseType)
	}
}

func TestHardwareConfig_Concurrency(t *testing.T) {
	tests := []struct {
		name        string
		concurrency int
		valid       bool
	}{
		{"Single core", 1, true},
		{"Dual core", 2, true},
		{"Quad core", 4, true},
		{"Octa core", 8, true},
		{"High-end", 16, true},
		{"Invalid zero", 0, false},
		{"Invalid negative", -1, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := HardwareConfig{
				HardwareConcurrency: tt.concurrency,
			}

			if tt.valid && config.HardwareConcurrency <= 0 {
				t.Error("Valid concurrency should be positive")
			}

			if !tt.valid && config.HardwareConcurrency > 0 {
				t.Error("Invalid concurrency should not be positive")
			}
		})
	}
}

func TestAntiDetectConfig_Settings(t *testing.T) {
	config := AntiDetectConfig{
		StealthMode:              true,
		DisableWebDriver:         true,
		DisableAutomation:        true,
		MaskAutomationSignals:    true,
		RandomizeTimings:         true,
		SimulateHuman:            true,
		DisableHeadlessDetection: true,
		MouseMovementDelay:       100,
		TypingDelay:              50,
		ScrollDelay:              80,
		ClickDelay:               60,
		HumanLikeMovement:        true,
	}

	if !config.StealthMode {
		t.Error("Stealth mode should be enabled")
	}

	if config.MouseMovementDelay <= 0 {
		t.Error("Mouse movement delay should be positive")
	}

	if config.TypingDelay <= 0 {
		t.Error("Typing delay should be positive")
	}

	if config.ClickDelay <= 0 {
		t.Error("Click delay should be positive")
	}
}

func TestExtensionConfig_Types(t *testing.T) {
	validTypes := []string{"local", "store", "unpacked"}

	for _, extType := range validTypes {
		config := ExtensionConfig{
			ID:      "test-extension",
			Name:    "Test Extension",
			Type:    extType,
			Enabled: true,
		}

		if config.Type != extType {
			t.Errorf("Extension type mismatch: got %s, want %s", config.Type, extType)
		}
	}
}

func TestFingerprintTemplate_Creation(t *testing.T) {
	fingerprint := &FingerprintConfig{
		UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		Version:   "1.0",
	}

	template := &FingerprintTemplate{
		Name:        "Test Template",
		Description: "A test template",
		Platform:    "windows",
		Category:    "standard",
		Config:      fingerprint,
		CreatedAt:   time.Now(),
		Popular:     true,
		Verified:    true,
	}

	if template.Name == "" {
		t.Error("Template name should not be empty")
	}

	if template.Config == nil {
		t.Error("Template should have a config")
	}

	if template.Platform == "" {
		t.Error("Template should have a platform")
	}

	validCategories := []string{"standard", "gaming", "business", "mobile", "custom"}
	found := false
	for _, category := range validCategories {
		if template.Category == category {
			found = true
			break
		}
	}
	if !found {
		t.Errorf("Invalid template category: %s", template.Category)
	}
}

func TestTestResults_Creation(t *testing.T) {
	results := &TestResults{
		CanvasFingerprint: "test-canvas-fingerprint",
		WebGLFingerprint:  "test-webgl-fingerprint",
		AudioFingerprint:  "test-audio-fingerprint",
		FontFingerprint:   "test-font-fingerprint",
		TestedAt:          time.Now(),
		TestURL:           "https://example.com",
		Success:           true,
	}

	if results.TestedAt.IsZero() {
		t.Error("Test time should be set")
	}

	if results.TestURL == "" {
		t.Error("Test URL should not be empty")
	}

	if !results.Success {
		t.Error("Test should be marked as successful")
	}

	if results.CanvasFingerprint == "" {
		t.Error("Canvas fingerprint should not be empty")
	}
}

func TestDetectionEvent_Creation(t *testing.T) {
	event := DetectionEvent{
		ProfileID:   "test-profile",
		URL:         "https://example.com",
		DetectedAt:  time.Now(),
		EventType:   "captcha",
		Severity:    "medium",
		UserAgent:   "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		Description: "CAPTCHA challenge detected",
	}

	if event.ProfileID == "" {
		t.Error("Profile ID should not be empty")
	}

	if event.DetectedAt.IsZero() {
		t.Error("Detection time should be set")
	}

	validEventTypes := []string{"captcha", "blocked", "challenge"}
	found := false
	for _, eventType := range validEventTypes {
		if event.EventType == eventType {
			found = true
			break
		}
	}
	if !found {
		t.Errorf("Invalid event type: %s", event.EventType)
	}

	validSeverities := []string{"low", "medium", "high"}
	found = false
	for _, severity := range validSeverities {
		if event.Severity == severity {
			found = true
			break
		}
	}
	if !found {
		t.Errorf("Invalid severity: %s", event.Severity)
	}
}

func TestLaunchOptionsFingerprint_Validation(t *testing.T) {
	options := &LaunchOptionsFingerprint{
		LaunchOptions: &LaunchOptions{
			Headless: false,
			Timeout:  30 * time.Second,
		},
		ApplyFingerprint:    true,
		FingerprintProfile:  "test-profile",
		AntiDetectLevel:     "high",
		StealthMode:         true,
		EnableAutomation:    true,
		AutomationTimeout:   "60s",
		KeepSessionAlive:    true,
	}

	if options.LaunchOptions == nil {
		t.Error("Launch options should not be nil")
	}

	if !options.ApplyFingerprint {
		t.Error("Apply fingerprint should be enabled")
	}

	validAntiDetectLevels := []string{"low", "medium", "high"}
	found := false
	for _, level := range validAntiDetectLevels {
		if options.AntiDetectLevel == level {
			found = true
			break
		}
	}
	if !found {
		t.Errorf("Invalid anti-detect level: %s", options.AntiDetectLevel)
	}

	if !options.StealthMode {
		t.Error("Stealth mode should be enabled")
	}
}

// Benchmark tests
func BenchmarkFingerprintConfig_GetHash(b *testing.B) {
	config := &FingerprintConfig{
		UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		Language:  "en-US",
		Timezone:  "America/New_York",
		ScreenResolution: ScreenConfig{
			Width:  1920,
			Height: 1080,
		},
		Hardware: HardwareConfig{
			HardwareConcurrency: 8,
		},
		Version: "1.0",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = config.GetHash()
	}
}

func BenchmarkFingerprintConfig_ToJSON(b *testing.B) {
	config := &FingerprintConfig{
		UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		Language:  "en-US",
		Timezone:  "America/New_York",
		ScreenResolution: ScreenConfig{
			Width:  1920,
			Height: 1080,
		},
		Hardware: HardwareConfig{
			HardwareConcurrency: 8,
		},
		Version: "1.0",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = config.ToJSON()
	}
}

func BenchmarkFingerprintConfig_Clone(b *testing.B) {
	config := &FingerprintConfig{
		UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		Language:  "en-US",
		Timezone:  "America/New_York",
		ScreenResolution: ScreenConfig{
			Width:  1920,
			Height: 1080,
		},
		Hardware: HardwareConfig{
			HardwareConcurrency: 8,
		},
		Version: "1.0",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = config.Clone()
	}
}