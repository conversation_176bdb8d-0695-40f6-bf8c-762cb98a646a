package chrome

import (
	"encoding/json"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"time"
)

// ConfigManager handles profile configuration persistence
type ConfigManager struct {
	configDir string
}

// NewConfigManager creates a new configuration manager
func NewConfigManager(configDir string) *ConfigManager {
	return &ConfigManager{
		configDir: configDir,
	}
}

// SaveProfile saves a profile configuration to disk
func (cm *ConfigManager) SaveProfile(profile *Profile) error {
	if profile == nil {
		return fmt.Errorf("profile cannot be nil")
	}

	if err := cm.ensureConfigDir(); err != nil {
		return fmt.Errorf("failed to ensure config directory: %w", err)
	}

	profileFile := filepath.Join(cm.configDir, fmt.Sprintf("%s.json", profile.ID))
	
	// Update timestamp
	profile.UpdatedAt = time.Now()
	
	data, err := json.MarshalIndent(profile, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal profile: %w", err)
	}

	if err := os.WriteFile(profileFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write profile file: %w", err)
	}

	return nil
}

// LoadProfile loads a profile configuration from disk
func (cm *ConfigManager) LoadProfile(profileID string) (*Profile, error) {
	if profileID == "" {
		return nil, fmt.Errorf("profile ID cannot be empty")
	}

	profileFile := filepath.Join(cm.configDir, fmt.Sprintf("%s.json", profileID))
	
	data, err := os.ReadFile(profileFile)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, fmt.Errorf("profile %s not found", profileID)
		}
		return nil, fmt.Errorf("failed to read profile file: %w", err)
	}

	var profile Profile
	if err := json.Unmarshal(data, &profile); err != nil {
		return nil, fmt.Errorf("failed to unmarshal profile: %w", err)
	}

	return &profile, nil
}

// LoadAllProfiles loads all profile configurations from disk
func (cm *ConfigManager) LoadAllProfiles() ([]*Profile, error) {
	if err := cm.ensureConfigDir(); err != nil {
		return nil, fmt.Errorf("failed to ensure config directory: %w", err)
	}

	entries, err := os.ReadDir(cm.configDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read config directory: %w", err)
	}

	var profiles []*Profile
	for _, entry := range entries {
		if entry.IsDir() || filepath.Ext(entry.Name()) != ".json" {
			continue
		}

		profileID := entry.Name()[:len(entry.Name())-5] // Remove .json extension
		profile, err := cm.LoadProfile(profileID)
		if err != nil {
			// Log error but continue loading other profiles
			continue
		}
		profiles = append(profiles, profile)
	}

	return profiles, nil
}

// DeleteProfile removes a profile configuration from disk
func (cm *ConfigManager) DeleteProfile(profileID string) error {
	if profileID == "" {
		return fmt.Errorf("profile ID cannot be empty")
	}

	profileFile := filepath.Join(cm.configDir, fmt.Sprintf("%s.json", profileID))
	
	if err := os.Remove(profileFile); err != nil {
		if os.IsNotExist(err) {
			return fmt.Errorf("profile %s not found", profileID)
		}
		return fmt.Errorf("failed to delete profile file: %w", err)
	}

	return nil
}

// ProfileExists checks if a profile configuration exists on disk
func (cm *ConfigManager) ProfileExists(profileID string) bool {
	if profileID == "" {
		return false
	}

	profileFile := filepath.Join(cm.configDir, fmt.Sprintf("%s.json", profileID))
	_, err := os.Stat(profileFile)
	return err == nil
}

// ExportProfile exports a profile configuration to a specified file
func (cm *ConfigManager) ExportProfile(profileID, exportPath string) error {
	profile, err := cm.LoadProfile(profileID)
	if err != nil {
		return fmt.Errorf("failed to load profile: %w", err)
	}

	exportData := &ExportData{
		Version:    ExportDataVersion,
		Profile:    profile,
		ExportedAt: time.Now(),
	}

	data, err := json.MarshalIndent(exportData, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal export data: %w", err)
	}

	if err := os.WriteFile(exportPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write export file: %w", err)
	}

	return nil
}

// ImportProfile imports a profile configuration from a specified file
func (cm *ConfigManager) ImportProfile(importPath string, options *ImportOptions) (*Profile, error) {
	data, err := os.ReadFile(importPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read import file: %w", err)
	}

	var exportData ExportData
	if err := json.Unmarshal(data, &exportData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal export data: %w", err)
	}

	if exportData.Profile == nil {
		return nil, fmt.Errorf("no profile data found in import file")
	}

	profile := exportData.Profile
	
	// Handle import options
	if options != nil {
		if options.NewProfileID != "" {
			profile.ID = options.NewProfileID
		}
		
		if !options.OverwriteExisting && cm.ProfileExists(profile.ID) {
			return nil, fmt.Errorf("profile %s already exists and overwrite is not allowed", profile.ID)
		}
	}

	// Update timestamps
	profile.UpdatedAt = time.Now()
	if profile.CreatedAt.IsZero() {
		profile.CreatedAt = time.Now()
	}

	// Save the imported profile
	if err := cm.SaveProfile(profile); err != nil {
		return nil, fmt.Errorf("failed to save imported profile: %w", err)
	}

	return profile, nil
}

// BackupProfile creates a backup of a profile configuration
func (cm *ConfigManager) BackupProfile(profileID, backupPath string) error {
	profile, err := cm.LoadProfile(profileID)
	if err != nil {
		return fmt.Errorf("failed to load profile: %w", err)
	}

	// Ensure backup directory exists
	backupDir := filepath.Dir(backupPath)
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		return fmt.Errorf("failed to create backup directory: %w", err)
	}

	data, err := json.MarshalIndent(profile, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal profile: %w", err)
	}

	if err := os.WriteFile(backupPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write backup file: %w", err)
	}

	return nil
}

// ensureConfigDir creates the configuration directory if it doesn't exist
func (cm *ConfigManager) ensureConfigDir() error {
	if _, err := os.Stat(cm.configDir); os.IsNotExist(err) {
		if err := os.MkdirAll(cm.configDir, 0755); err != nil {
			return fmt.Errorf("failed to create config directory: %w", err)
		}
	} else if err != nil {
		return fmt.Errorf("failed to check config directory: %w", err)
	}
	return nil
}

// GetConfigDir returns the configuration directory path
func (cm *ConfigManager) GetConfigDir() string {
	return cm.configDir
}

// ListProfileFiles returns all profile configuration files
func (cm *ConfigManager) ListProfileFiles() ([]string, error) {
	if err := cm.ensureConfigDir(); err != nil {
		return nil, fmt.Errorf("failed to ensure config directory: %w", err)
	}

	var profileFiles []string
	err := filepath.WalkDir(cm.configDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if !d.IsDir() && filepath.Ext(path) == ".json" {
			profileFiles = append(profileFiles, path)
		}
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to walk config directory: %w", err)
	}

	return profileFiles, nil
}