package chrome

import "a8.tools/backend/utils/file"

type ChromeAP<PERSON> struct {
	profileManager *ProfileManager
}

func NewChromeAPI() *ChromeAPI {

	path, _ := file.GetBrowserDataDir()
	profileManager, err := NewProfileManager(path)
	if err != nil {
		return nil
	}
	return &ChromeAPI{profileManager: profileManager}
}

func (c *ChromeAPI) CreateProfile(name, description string) {
	c.profileManager.CreateProfile(name, description)
}
