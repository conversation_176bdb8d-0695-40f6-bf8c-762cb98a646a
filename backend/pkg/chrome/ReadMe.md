# Chrome Profile Manager

Chrome Profile Manager 是一个用于管理Chrome浏览器配置文件的Go包。它提供了创建、管理、导入导出Chrome配置文件的完整功能。

## 功能特性

- 跨平台支持（Windows、macOS、Linux）
- 配置文件的创建、删除、重命名
- JSON格式的配置持久化
- 配置文件导入导出
- 进程状态管理
- 完整的错误处理

## 核心组件

### 数据结构

#### Profile
配置文件的核心数据结构，包含以下字段：
- `ID`: 唯一标识符
- `Name`: 配置文件名称
- `Description`: 描述信息
- `IconPath`: 图标路径
- `UserDataDir`: 用户数据目录
- `LaunchParams`: 启动参数
- `ProxyConfig`: 代理配置
- `WindowConfig`: 窗口配置
- `CreatedAt/UpdatedAt`: 时间戳
- `Status`: 运行状态
- `ProcessID`: 进程ID

#### ProfileStatus
配置文件状态枚举：
- `ProfileStatusStopped`: 已停止
- `ProfileStatusRunning`: 运行中
- `ProfileStatusStarting`: 启动中
- `ProfileStatusStopping`: 停止中

### 管理器

#### ProfileManager
主要的配置文件管理器，实现了`Manager`接口：

```go
type Manager interface {
    // Profile management
    CreateProfile(name, description string) (*Profile, error)
    DeleteProfile(profileID string) error
    ListProfiles() ([]*Profile, error)
    GetProfile(profileID string) (*Profile, error)
    RenameProfile(profileID, newName string) error
    SetProfileIcon(profileID, iconPath string) error
    
    // Instance management
    LaunchProfile(profileID string, options *LaunchOptions) (*ProcessInfo, error)
    StopProfile(profileID string) error
    GetProfileStatus(profileID string) (ProfileStatus, error)
    ListRunningProfiles() ([]*ProcessInfo, error)
    
    // Import/Export
    ExportProfile(profileID string) (*ExportData, error)
    ImportProfile(data *ExportData, options *ImportOptions) (*Profile, error)
    BackupProfile(profileID, backupPath string) error
    
    // Cleanup
    CleanupUnusedProfiles() error
    CleanupTempFiles() error
}
```

#### ConfigManager
负责配置文件的JSON序列化和文件系统操作：

```go
type ConfigManager struct {
    configDir string
}
```

## 使用示例

### 创建配置文件管理器

```go
package main

import (
    "fmt"
    "log"
    "a8.tools/backend/pkg/chrome"
)

func main() {
    // 使用默认数据目录
    pm, err := chrome.NewProfileManager()
    if err != nil {
        log.Fatal(err)
    }

    // 或使用自定义数据目录
    pm, err = chrome.NewProfileManager("/path/to/custom/dir")
    if err != nil {
        log.Fatal(err)
    }
}
```

### 创建配置文件

```go
profile, err := pm.CreateProfile("工作环境", "用于工作的Chrome配置")
if err != nil {
    log.Fatal(err)
}

fmt.Printf("创建配置文件: %s (ID: %s)\n", profile.Name, profile.ID)
```

### 列出所有配置文件

```go
profiles, err := pm.ListProfiles()
if err != nil {
    log.Fatal(err)
}

for _, profile := range profiles {
    fmt.Printf("配置文件: %s - %s\n", profile.Name, profile.Description)
}
```

### 重命名配置文件

```go
err := pm.RenameProfile(profile.ID, "新的工作环境")
if err != nil {
    log.Fatal(err)
}
```

### 设置配置文件图标

```go
err := pm.SetProfileIcon(profile.ID, "/path/to/icon.png")
if err != nil {
    log.Fatal(err)
}
```

### 导出配置文件

```go
exportData, err := pm.ExportProfile(profile.ID)
if err != nil {
    log.Fatal(err)
}

// 保存到文件（使用ConfigManager）
cm := chrome.NewConfigManager("/export/dir")
err = cm.ExportProfile(profile.ID, "/path/to/export.json")
```

### 导入配置文件

```go
options := &chrome.ImportOptions{
    OverwriteExisting: false,
    NewProfileID:     "custom-import-id",
}

importedProfile, err := pm.ImportProfile(exportData, options)
if err != nil {
    log.Fatal(err)
}
```

### 删除配置文件

```go
err := pm.DeleteProfile(profile.ID)
if err != nil {
    log.Fatal(err)
}
```

## 错误处理

包提供了丰富的错误类型：

- `ProfileError`: 配置文件相关错误
- `ProcessError`: 进程相关错误
- `FileSystemError`: 文件系统错误
- `ValidationError`: 验证错误
- `ConfigError`: 配置错误

### 错误检查示例

```go
_, err := pm.GetProfile("non-existent")
if chrome.IsProfileNotFound(err) {
    fmt.Println("配置文件不存在")
}

err = pm.DeleteProfile("running-profile")
if chrome.IsProfileInUse(err) {
    fmt.Println("配置文件正在使用中")
}
```

## 平台支持

### 数据目录位置

- **Windows**: `%APPDATA%\ChromeProfiles`
- **macOS**: `~/Library/Application Support/ChromeProfiles`
- **Linux**: `~/.local/share/ChromeProfiles` 或 `$XDG_DATA_HOME/ChromeProfiles`

### Chrome可执行文件检测

包会自动检测Chrome的安装位置：

```go
chromePath, err := chrome.GetChromeExecutable()
if err != nil {
    log.Fatal("未找到Chrome可执行文件")
}

version, err := chrome.GetChromeVersion(chromePath)
if err == nil {
    fmt.Printf("Chrome版本: %s\n", version)
}
```

## 测试

运行单元测试：

```bash
go test -v ./...
```

包含25个测试用例，涵盖所有主要功能和错误场景。

## 许可证

根据项目根目录的许可证文件。