package chrome

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"os"
	"path/filepath"
	"strings"
	"time"

	"a8.tools/backend/pkg/rod"
)

// AutomationSession 自动化会话
type AutomationSession struct {
	ProfileID         string                  `json:"profileId"`
	SessionID         string                  `json:"sessionId"`
	BrowserAutomation *rod.BrowserAutomation  `json:"-"` // 不序列化
	Profile           *FingerprintProfile     `json:"profile"`
	ProcessInfo       *ProcessInfo            `json:"processInfo"`
	CreatedAt         time.Time               `json:"createdAt"`
	LastActivity      time.Time               `json:"lastActivity"`
	Status            string                  `json:"status"` // active, idle, closed
	TaskCount         int                     `json:"taskCount"`
	ErrorCount        int                     `json:"errorCount"`
}

// FingerprintBrowserManager 指纹浏览器管理器
type FingerprintBrowserManager struct {
	profileManager     *ProfileManager
	FingerprintManager *FingerprintManager
	activeSessions     map[string]*AutomationSession
	dataDir            string
	sessionTimeout     time.Duration
}

// NewFingerprintBrowserManager 创建指纹浏览器管理器
func NewFingerprintBrowserManager(dataDir string) (*FingerprintBrowserManager, error) {
	pm, err := NewProfileManager(dataDir)
	if err != nil {
		return nil, fmt.Errorf("failed to create profile manager: %w", err)
	}

	fm := NewFingerprintManager(pm)

	fbm := &FingerprintBrowserManager{
		profileManager:     pm,
		FingerprintManager: fm,
		activeSessions:     make(map[string]*AutomationSession),
		dataDir:            dataDir,
		sessionTimeout:     30 * time.Minute,
	}

	// 加载已保存的会话
	fbm.loadSessionsFromDisk()

	return fbm, nil
}

// LaunchWithAutomation 启动配置文件并创建自动化会话
func (fbm *FingerprintBrowserManager) LaunchWithAutomation(profileID string, options *LaunchOptionsFingerprint) (*AutomationSession, error) {
	// 加载指纹配置文件
	fpProfile, err := fbm.FingerprintManager.LoadFingerprintProfile(profileID)
	if err != nil {
		return nil, fmt.Errorf("failed to load fingerprint profile: %w", err)
	}

	// 检查是否已有活动会话
	for _, session := range fbm.activeSessions {
		if session.ProfileID == profileID && session.Status == "active" {
			return session, nil
		}
	}

	// 准备启动选项
	launchOpts := &LaunchOptions{
		Headless:    false,
		Timeout:     30 * time.Second,
		NoSandbox:   true,
		Environment: make(map[string]string),
	}

	if options != nil && options.LaunchOptions != nil {
		launchOpts = options.LaunchOptions
	}

	// 应用指纹特定的环境变量
	fbm.applyFingerprintEnvironment(fpProfile, launchOpts)

	// 启动Chrome实例
	processInfo, err := fbm.profileManager.LaunchProfile(profileID, launchOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to launch profile: %w", err)
	}

	// 创建Rod自动化实例
	ba := rod.NewBrowserAutomation(fpProfile.Profile.UserDataDir)

	// 应用指纹配置到Rod设置
	if err := fbm.applyFingerprintToRod(ba, fpProfile, options); err != nil {
		// 如果应用指纹失败，停止Chrome进程
		fbm.profileManager.StopProfile(profileID)
		return nil, fmt.Errorf("failed to apply fingerprint to Rod: %w", err)
	}

	// 创建自动化会话
	session := &AutomationSession{
		ProfileID:         profileID,
		SessionID:         fmt.Sprintf("%s_%d_%d", profileID, processInfo.PID, time.Now().Unix()),
		BrowserAutomation: ba,
		Profile:           fpProfile,
		ProcessInfo:       processInfo,
		CreatedAt:         time.Now(),
		LastActivity:      time.Now(),
		Status:            "active",
		TaskCount:         0,
		ErrorCount:        0,
	}

	// 保存会话
	fbm.activeSessions[session.SessionID] = session

	// 异步保存会话到磁盘
	go fbm.saveSessionToDisk(session)

	// 启动会话监控
	go fbm.monitorSession(session)

	return session, nil
}

// applyFingerprintToRod 将指纹配置应用到Rod设置
func (fbm *FingerprintBrowserManager) applyFingerprintToRod(ba *rod.BrowserAutomation, fpProfile *FingerprintProfile, options *LaunchOptionsFingerprint) error {
	// 创建Rod任务计划用于初始化指纹
	plan := ba.CreateNewPlan("FingerprintSetup", "Apply fingerprint configuration")

	// 设置基础配置
	plan.Settings.UserAgent = fpProfile.Fingerprint.UserAgent
	plan.Settings.ViewportWidth = fpProfile.Fingerprint.ScreenResolution.Width
	plan.Settings.ViewportHeight = fpProfile.Fingerprint.ScreenResolution.Height
	plan.Settings.AntiDetection = fpProfile.Fingerprint.AntiDetect.StealthMode

	// 设置语言
	plan.Settings.Headers = make(map[string]string)
	plan.Settings.Headers["Accept-Language"] = fpProfile.Fingerprint.Network.AcceptLanguage
	plan.Settings.Headers["Accept-Encoding"] = fpProfile.Fingerprint.Network.AcceptEncoding

	// 添加自定义头部
	for key, value := range fpProfile.Fingerprint.Network.CustomHeaders {
		plan.Settings.Headers[key] = value
	}

	// 设置代理
	if fpProfile.Fingerprint.Proxy != nil {
		if fpProfile.Fingerprint.Proxy.Username != "" && fpProfile.Fingerprint.Proxy.Password != "" {
			plan.Settings.Proxy = fmt.Sprintf("%s://%s:%s@%s:%d",
				fpProfile.Fingerprint.Proxy.Type,
				fpProfile.Fingerprint.Proxy.Username,
				fpProfile.Fingerprint.Proxy.Password,
				fpProfile.Fingerprint.Proxy.Host,
				fpProfile.Fingerprint.Proxy.Port)
		} else {
			plan.Settings.Proxy = fmt.Sprintf("%s://%s:%d",
				fpProfile.Fingerprint.Proxy.Type,
				fpProfile.Fingerprint.Proxy.Host,
				fpProfile.Fingerprint.Proxy.Port)
		}
	}

	// 设置反检测级别
	if options != nil {
		switch options.AntiDetectLevel {
		case "high":
			plan.Settings.AntiDetection = true
			plan.Settings.HandleChallenges = true
			plan.Settings.MonitorChallenges = true
		case "medium":
			plan.Settings.AntiDetection = true
			plan.Settings.HandleChallenges = true
		case "low":
			plan.Settings.AntiDetection = false
		}
	}

	// 应用指纹脚本
	fingerprintJS := fbm.generateFingerprintJavaScript(fpProfile.Fingerprint)
	ba.AddEvaluateTask(plan, fingerprintJS, "", "Apply fingerprint modifications")

	// 如果启用了扩展，添加扩展安装任务
	for _, ext := range fpProfile.Fingerprint.Extensions {
		if ext.Enabled && ext.Path != "" {
			ba.AddInstallExtensionTask(plan, ext.Path, fmt.Sprintf("Install extension: %s", ext.Name))
		}
	}

	return nil
}

// generateFingerprintJavaScript 生成指纹修改JavaScript
func (fbm *FingerprintBrowserManager) generateFingerprintJavaScript(fingerprint *FingerprintConfig) string {
	// 计算时区偏移
	timezoneOffset := fbm.getTimezoneOffset(fingerprint.Timezone)

	// 构建完整的指纹脚本
	scriptParts := []string{
		// 基础navigator属性修改
		fmt.Sprintf(`
		// 修改Navigator属性
		Object.defineProperty(navigator, 'platform', {
			get: () => '%s'
		});
		
		Object.defineProperty(navigator, 'hardwareConcurrency', {
			get: () => %d
		});
		
		Object.defineProperty(navigator, 'deviceMemory', {
			get: () => %d
		});
		
		Object.defineProperty(navigator, 'maxTouchPoints', {
			get: () => %d
		});
		`,
			fingerprint.Hardware.Platform,
			fingerprint.Hardware.HardwareConcurrency,
			fingerprint.Hardware.DeviceMemory,
			fingerprint.Hardware.MaxTouchPoints,
		),

		// 屏幕属性修改
		fmt.Sprintf(`
		// 修改Screen属性
		Object.defineProperty(screen, 'width', {
			get: () => %d
		});
		
		Object.defineProperty(screen, 'height', {
			get: () => %d
		});
		
		Object.defineProperty(screen, 'availWidth', {
			get: () => %d
		});
		
		Object.defineProperty(screen, 'availHeight', {
			get: () => %d
		});
		
		Object.defineProperty(screen, 'colorDepth', {
			get: () => %d
		});
		
		Object.defineProperty(screen, 'pixelDepth', {
			get: () => %d
		});
		`,
			fingerprint.ScreenResolution.Width,
			fingerprint.ScreenResolution.Height,
			fingerprint.ScreenResolution.AvailWidth,
			fingerprint.ScreenResolution.AvailHeight,
			fingerprint.ScreenResolution.ColorDepth,
			fingerprint.ScreenResolution.PixelDepth,
		),

		// 时区修改
		fmt.Sprintf(`
		// 修改时区
		const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
		Date.prototype.getTimezoneOffset = function() {
			return %d;
		};
		
		// 修改Intl.DateTimeFormat
		const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
		Intl.DateTimeFormat.prototype.resolvedOptions = function() {
			const options = originalResolvedOptions.call(this);
			options.timeZone = '%s';
			return options;
		};
		`,
			timezoneOffset,
			fingerprint.Timezone,
		),
	}

	// WebRTC处理
	if fingerprint.WebRTC.Mode == "disabled" {
		scriptParts = append(scriptParts, `
		// 禁用WebRTC
		navigator.getUserMedia = undefined;
		navigator.webkitGetUserMedia = undefined;
		navigator.mozGetUserMedia = undefined;
		navigator.mediaDevices = undefined;
		window.RTCPeerConnection = undefined;
		window.webkitRTCPeerConnection = undefined;
		window.mozRTCPeerConnection = undefined;
		`)
	} else if fingerprint.WebRTC.Mode == "public_only" {
		scriptParts = append(scriptParts, `
		// WebRTC IP处理 - 仅公开IP
		const originalCreateOffer = RTCPeerConnection.prototype.createOffer;
		RTCPeerConnection.prototype.createOffer = function(options) {
			if (options) {
				options.iceRestart = false;
			}
			return originalCreateOffer.call(this, options);
		};
		`)
	}

	// Canvas指纹处理
	if fingerprint.Canvas.NoiseEnabled {
		scriptParts = append(scriptParts, fmt.Sprintf(`
		// Canvas指纹噪声处理
		const getContext = HTMLCanvasElement.prototype.getContext;
		HTMLCanvasElement.prototype.getContext = function(type) {
			const context = getContext.call(this, type);
			if (type === '2d') {
				const getImageData = context.getImageData;
				context.getImageData = function(...args) {
					const imageData = getImageData.apply(this, args);
					// 添加噪声
					for (let i = 0; i < imageData.data.length; i += 4) {
						if (Math.random() < %f) {
							imageData.data[i] += Math.floor((Math.random() - 0.5) * %f);
							imageData.data[i + 1] += Math.floor((Math.random() - 0.5) * %f);
							imageData.data[i + 2] += Math.floor((Math.random() - 0.5) * %f);
						}
					}
					return imageData;
				};
			}
			return context;
		};
		`,
			fingerprint.Canvas.NoiseLevel,
			fingerprint.Canvas.NoiseLevel*10,
			fingerprint.Canvas.NoiseLevel*10,
			fingerprint.Canvas.NoiseLevel*10,
		))
	}

	// WebGL指纹处理
	if fingerprint.WebGL.RandomizeParameters {
		scriptParts = append(scriptParts, fmt.Sprintf(`
		// WebGL指纹修改
		const getParameter = WebGLRenderingContext.prototype.getParameter;
		WebGLRenderingContext.prototype.getParameter = function(parameter) {
			if (parameter === this.VENDOR) {
				return '%s';
			}
			if (parameter === this.RENDERER) {
				return '%s';
			}
			return getParameter.call(this, parameter);
		};
		
		const getExtension = WebGLRenderingContext.prototype.getExtension;
		WebGLRenderingContext.prototype.getExtension = function(name) {
			if (name === 'WEBGL_debug_renderer_info') {
				const ext = getExtension.call(this, name);
				if (ext) {
					const getParam = this.getParameter.bind(this);
					this.getParameter = function(param) {
						if (param === ext.UNMASKED_VENDOR_WEBGL) {
							return '%s';
						}
						if (param === ext.UNMASKED_RENDERER_WEBGL) {
							return '%s';
						}
						return getParam(param);
					};
				}
				return ext;
			}
			return getExtension.call(this, name);
		};
		`,
			fingerprint.WebGL.Vendor,
			fingerprint.WebGL.Renderer,
			fingerprint.WebGL.UnmaskedVendor,
			fingerprint.WebGL.UnmaskedRenderer,
		))
	}

	// 字体指纹处理
	if fingerprint.Fonts.MaskFonts {
		scriptParts = append(scriptParts, `
		// 字体指纹处理
		const originalOffsetWidth = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetWidth');
		const originalOffsetHeight = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetHeight');
		
		Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
			get: function() {
				const width = originalOffsetWidth.get.call(this);
				return width + Math.floor(Math.random() * 3) - 1;
			}
		});
		
		Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {
			get: function() {
				const height = originalOffsetHeight.get.call(this);
				return height + Math.floor(Math.random() * 3) - 1;
			}
		});
		`)
	}

	// 反检测脚本
	if fingerprint.AntiDetect.DisableWebDriver {
		scriptParts = append(scriptParts, `
		// 反自动化检测
		Object.defineProperty(navigator, 'webdriver', {
			get: () => undefined
		});
		
		// 移除自动化相关属性
		delete window.chrome.runtime.onConnect;
		delete window.chrome.runtime.onMessage;
		
		// 修改plugins
		Object.defineProperty(navigator, 'plugins', {
			get: () => new Array(3).fill(0).map(() => ({
				name: 'Chrome PDF Plugin',
				description: 'Portable Document Format',
				filename: 'internal-pdf-viewer'
			}))
		});
		
		// 修改语言
		Object.defineProperty(navigator, 'languages', {
			get: () => ['en-US', 'en']
		});
		`)
	}

	// 模拟人类行为
	if fingerprint.AntiDetect.SimulateHuman {
		scriptParts = append(scriptParts, fmt.Sprintf(`
		// 人类行为模拟
		const originalAddEventListener = EventTarget.prototype.addEventListener;
		EventTarget.prototype.addEventListener = function(type, listener, options) {
			if (type === 'mousemove' || type === 'click') {
				const wrappedListener = function(event) {
					// 添加随机延迟
					setTimeout(() => {
						listener.call(this, event);
					}, Math.random() * %d);
				};
				return originalAddEventListener.call(this, type, wrappedListener, options);
			}
			return originalAddEventListener.call(this, type, listener, options);
		};
		`,
			fingerprint.AntiDetect.MouseMovementDelay,
		))
	}

	// 添加控制台日志
	scriptParts = append(scriptParts, `
		console.log('[FingerprintBrowser] Fingerprint applied successfully');
		console.log('[FingerprintBrowser] UserAgent:', navigator.userAgent);
		console.log('[FingerprintBrowser] Platform:', navigator.platform);
		console.log('[FingerprintBrowser] Screen:', screen.width + 'x' + screen.height);
		console.log('[FingerprintBrowser] Timezone offset:', new Date().getTimezoneOffset());
	`)

	return strings.Join(scriptParts, "\n")
}

// ExecuteAutomationPlan 执行自动化计划
func (fbm *FingerprintBrowserManager) ExecuteAutomationPlan(sessionID string, plan *rod.TaskPlan) error {
	session, exists := fbm.activeSessions[sessionID]
	if !exists {
		return fmt.Errorf("session %s not found", sessionID)
	}

	if session.Status != "active" {
		return fmt.Errorf("session %s is not active", sessionID)
	}

	// 更新活动时间
	session.LastActivity = time.Now()
	session.TaskCount++

	// 执行计划
	err := session.BrowserAutomation.ExecutePlan(plan)
	if err != nil {
		session.ErrorCount++
		return fmt.Errorf("failed to execute plan: %w", err)
	}

	// 保存会话状态
	go fbm.saveSessionToDisk(session)

	return nil
}

// CreateAutomationPlan 创建自动化计划，集成指纹配置
func (fbm *FingerprintBrowserManager) CreateAutomationPlan(sessionID, name, description string) (*rod.TaskPlan, error) {
	session, exists := fbm.activeSessions[sessionID]
	if !exists {
		return nil, fmt.Errorf("session %s not found", sessionID)
	}

	plan := session.BrowserAutomation.CreateNewPlan(name, description)

	// 根据指纹配置调整计划设置
	if session.Profile.Fingerprint.AntiDetect.SimulateHuman {
		// 设置人类化的延迟
		plan.Settings.DefaultWait = fmt.Sprintf("%dms", session.Profile.Fingerprint.AntiDetect.ClickDelay)
	}

	return plan, nil
}

// GetSession 获取会话
func (fbm *FingerprintBrowserManager) GetSession(sessionID string) (*AutomationSession, error) {
	session, exists := fbm.activeSessions[sessionID]
	if !exists {
		return nil, fmt.Errorf("session %s not found", sessionID)
	}
	return session, nil
}

// ListSessions 列出所有会话
func (fbm *FingerprintBrowserManager) ListSessions() []*AutomationSession {
	var sessions []*AutomationSession
	for _, session := range fbm.activeSessions {
		sessions = append(sessions, session)
	}
	return sessions
}

// CloseSession 关闭自动化会话
func (fbm *FingerprintBrowserManager) CloseSession(sessionID string) error {
	session, exists := fbm.activeSessions[sessionID]
	if !exists {
		return fmt.Errorf("session %s not found", sessionID)
	}

	// 关闭自动化实例
	if session.BrowserAutomation != nil {
		session.BrowserAutomation.Shutdown()
	}

	// 停止Chrome进程
	if err := fbm.profileManager.StopProfile(session.ProfileID); err != nil {
		// 记录错误但继续清理
		fmt.Printf("Warning: failed to stop profile %s: %v\n", session.ProfileID, err)
	}

	// 更新会话状态
	session.Status = "closed"
	session.LastActivity = time.Now()

	// 保存会话状态
	fbm.saveSessionToDisk(session)

	// 移除会话
	delete(fbm.activeSessions, sessionID)

	return nil
}

// CloseAllSessions 关闭所有会话
func (fbm *FingerprintBrowserManager) CloseAllSessions() error {
	var errors []error

	for sessionID := range fbm.activeSessions {
		if err := fbm.CloseSession(sessionID); err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("failed to close some sessions: %v", errors)
	}

	return nil
}

// 辅助方法

func (fbm *FingerprintBrowserManager) applyFingerprintEnvironment(fpProfile *FingerprintProfile, launchOpts *LaunchOptions) {
	// 设置环境变量以支持指纹
	if launchOpts.Environment == nil {
		launchOpts.Environment = make(map[string]string)
	}

	// 设置时区
	launchOpts.Environment["TZ"] = fpProfile.Fingerprint.Timezone

	// 设置语言
	launchOpts.Environment["LANG"] = fpProfile.Fingerprint.Language
	launchOpts.Environment["LC_ALL"] = fpProfile.Fingerprint.Language + ".UTF-8"

	// 反检测环境变量
	if fpProfile.Fingerprint.AntiDetect.DisableAutomation {
		launchOpts.Environment["CHROME_NO_AUTOMATION"] = "1"
	}
}

func (fbm *FingerprintBrowserManager) getTimezoneOffset(timezone string) int {
	// 简化的时区偏移计算
	timezoneOffsets := map[string]int{
		"America/New_York":    300,  // UTC-5
		"America/Los_Angeles": 480,  // UTC-8
		"America/Chicago":     360,  // UTC-6
		"America/Denver":      420,  // UTC-7
		"Europe/London":       0,    // UTC+0
		"Europe/Paris":        -60,  // UTC+1
		"Europe/Berlin":       -60,  // UTC+1
		"Europe/Rome":         -60,  // UTC+1
		"Asia/Tokyo":          -540, // UTC+9
		"Asia/Shanghai":       -480, // UTC+8
		"Asia/Seoul":          -540, // UTC+9
		"Asia/Kolkata":        -330, // UTC+5:30
		"Australia/Sydney":    -660, // UTC+11
		"Australia/Melbourne": -660, // UTC+11
		"Pacific/Auckland":    -720, // UTC+12
	}

	if offset, exists := timezoneOffsets[timezone]; exists {
		return offset
	}

	return 0 // 默认UTC
}

func (fbm *FingerprintBrowserManager) monitorSession(session *AutomationSession) {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 检查会话是否超时
			if time.Since(session.LastActivity) > fbm.sessionTimeout {
				fmt.Printf("Session %s timed out, closing...\n", session.SessionID)
				fbm.CloseSession(session.SessionID)
				return
			}

			// 检查进程是否还在运行
			if session.ProcessInfo != nil && !IsProcessRunning(session.ProcessInfo.PID) {
				fmt.Printf("Process for session %s is no longer running, closing session...\n", session.SessionID)
				session.Status = "closed"
				delete(fbm.activeSessions, session.SessionID)
				return
			}

		default:
			// 检查会话状态
			if session.Status == "closed" {
				return
			}
			time.Sleep(10 * time.Second)
		}
	}
}

// 持久化方法

func (fbm *FingerprintBrowserManager) saveSessionToDisk(session *AutomationSession) {
	sessionsDir := filepath.Join(fbm.dataDir, "sessions")
	if err := EnsureDirectoryExists(sessionsDir); err != nil {
		return
	}

	data, err := json.MarshalIndent(session, "", "  ")
	if err != nil {
		return
	}

	sessionPath := filepath.Join(sessionsDir, session.SessionID+".json")
	os.WriteFile(sessionPath, data, 0644)
}

func (fbm *FingerprintBrowserManager) loadSessionsFromDisk() {
	sessionsDir := filepath.Join(fbm.dataDir, "sessions")
	if _, err := os.Stat(sessionsDir); os.IsNotExist(err) {
		return
	}

	files, err := filepath.Glob(filepath.Join(sessionsDir, "*.json"))
	if err != nil {
		return
	}

	for _, file := range files {
		data, err := os.ReadFile(file)
		if err != nil {
			continue
		}

		var session AutomationSession
		if err := json.Unmarshal(data, &session); err != nil {
			continue
		}

		// 只加载活动会话
		if session.Status == "active" {
			// 检查进程是否还在运行
			if session.ProcessInfo != nil && IsProcessRunning(session.ProcessInfo.PID) {
				fbm.activeSessions[session.SessionID] = &session
			} else {
				// 更新会话状态为已关闭
				session.Status = "closed"
				fbm.saveSessionToDisk(&session)
			}
		}
	}
}

// TestFingerprint 测试指纹是否生效
func (fbm *FingerprintBrowserManager) TestFingerprint(sessionID string, testURL string) (*TestResults, error) {
	session, exists := fbm.activeSessions[sessionID]
	if !exists {
		return nil, fmt.Errorf("session %s not found", sessionID)
	}

	if testURL == "" {
		testURL = "https://browserleaks.com/canvas" // 默认测试页面
	}

	// 创建测试计划
	plan := session.BrowserAutomation.CreateNewPlan("FingerprintTest", "Test fingerprint effectiveness")

	// 导航到测试页面
	if err := session.BrowserAutomation.AddNavigateTask(plan, testURL, "Navigate to test page"); err != nil {
		return nil, err
	}

	// 等待页面加载
	session.BrowserAutomation.AddWaitVisibleTask(plan, "body", rod.SelectorQuery, "10s", "Wait for page load")

	// 获取Canvas指纹
	session.BrowserAutomation.AddEvaluateTask(plan,
		`
		try {
			const canvas = document.createElement('canvas');
			const ctx = canvas.getContext('2d');
			ctx.textBaseline = 'top';
			ctx.font = '14px Arial';
			ctx.fillText('Canvas fingerprint test', 2, 2);
			return canvas.toDataURL();
		} catch(e) {
			return 'error: ' + e.message;
		}
		`,
		"canvas_fingerprint", "Get Canvas fingerprint")

	// 获取WebGL指纹
	session.BrowserAutomation.AddEvaluateTask(plan,
		`
		try {
			const canvas = document.createElement('canvas');
			const gl = canvas.getContext('webgl');
			const vendor = gl.getParameter(gl.VENDOR);
			const renderer = gl.getParameter(gl.RENDERER);
			return vendor + ' | ' + renderer;
		} catch(e) {
			return 'error: ' + e.message;
		}
		`,
		"webgl_fingerprint", "Get WebGL fingerprint")

	// 执行测试
	if err := session.BrowserAutomation.ExecutePlan(plan); err != nil {
		return nil, fmt.Errorf("failed to execute test plan: %w", err)
	}

	// 获取结果
	variables := session.BrowserAutomation.GetVariables()

	results := &TestResults{
		TestedAt: time.Now(),
		TestURL:  testURL,
		Success:  true,
	}

	if canvasFingerprint, ok := variables["canvas_fingerprint"].(string); ok {
		results.CanvasFingerprint = canvasFingerprint
	}

	if webglFingerprint, ok := variables["webgl_fingerprint"].(string); ok {
		results.WebGLFingerprint = webglFingerprint
	}

	// 更新配置文件的测试结果
	session.Profile.TestResults = results

	// 保存更新的配置文件
	fbm.FingerprintManager.SaveFingerprintProfile(session.Profile)

	return results, nil
}

// GenerateRandomDelay 生成人类化的随机延迟
func (fbm *FingerprintBrowserManager) GenerateRandomDelay(baseDelay int, variance int) time.Duration {
	if variance <= 0 {
		return time.Duration(baseDelay) * time.Millisecond
	}

	// 生成 baseDelay ± variance 范围内的随机延迟
	minDelay := baseDelay - variance
	maxDelay := baseDelay + variance

	if minDelay < 0 {
		minDelay = 0
	}

	randomDelay := rand.Intn(maxDelay-minDelay+1) + minDelay
	return time.Duration(randomDelay) * time.Millisecond
}

// SetSessionTimeout 设置会话超时时间
func (fbm *FingerprintBrowserManager) SetSessionTimeout(timeout time.Duration) {
	fbm.sessionTimeout = timeout
}

// GetStats 获取管理器统计信息
func (fbm *FingerprintBrowserManager) GetStats() map[string]interface{} {
	stats := make(map[string]interface{})

	stats["total_sessions"] = len(fbm.activeSessions)

	activeCount := 0
	idleCount := 0
	totalTasks := 0
	totalErrors := 0

	for _, session := range fbm.activeSessions {
		switch session.Status {
		case "active":
			activeCount++
		case "idle":
			idleCount++
		}
		totalTasks += session.TaskCount
		totalErrors += session.ErrorCount
	}

	stats["active_sessions"] = activeCount
	stats["idle_sessions"] = idleCount
	stats["total_tasks_executed"] = totalTasks
	stats["total_errors"] = totalErrors

	if totalTasks > 0 {
		stats["success_rate"] = float64(totalTasks-totalErrors) / float64(totalTasks)
	} else {
		stats["success_rate"] = 1.0
	}

	// 获取指纹管理器统计信息
	fpStats := fbm.FingerprintManager.GetStats()
	stats["fingerprint_stats"] = fpStats

	return stats
}

func (fbm *FingerprintBrowserManager) GenerateRandomFingerprint(platformHint string) (*FingerprintConfig, error) {
	return fbm.FingerprintManager.GenerateRandomFingerprint(platformHint)
}

func (fbm *FingerprintBrowserManager) CreateFingerprintProfile(name, description string, fingerprint *FingerprintConfig) (*FingerprintProfile, error) {
	return fbm.FingerprintManager.CreateFingerprintProfile(name, description, fingerprint)
}

func (fbm *FingerprintBrowserManager) ListTemplates() []*FingerprintTemplate {
	return fbm.FingerprintManager.ListTemplates()
}

func (fbm *FingerprintBrowserManager) GenerateFromTemplate(templateName string) (*FingerprintConfig, error) {
	return fbm.FingerprintManager.GenerateFromTemplate(templateName)
}

func (fbm *FingerprintBrowserManager) SaveTemplate(template *FingerprintTemplate) error {
	return fbm.FingerprintManager.SaveTemplate(template)
}
