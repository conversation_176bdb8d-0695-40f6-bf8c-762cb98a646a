package chrome

import (
	"encoding/json"
	"fmt"
	"time"
)

// FingerprintConfig 包含完整的浏览器指纹配置
type FingerprintConfig struct {
	// 基础指纹
	UserAgent        string           `json:"userAgent"`
	Language         string           `json:"language"`
	Timezone         string           `json:"timezone"`
	ScreenResolution ScreenConfig     `json:"screenResolution"`

	// WebRTC指纹
	WebRTC WebRTCConfig `json:"webRTC"`

	// Canvas指纹
	Canvas CanvasConfig `json:"canvas"`

	// WebGL指纹
	WebGL WebGLConfig `json:"webGL"`

	// 字体指纹
	Fonts FontConfig `json:"fonts"`

	// 硬件指纹
	Hardware HardwareConfig `json:"hardware"`

	// 网络指纹
	Network NetworkConfig `json:"network"`

	// 代理配置
	Proxy *ProxyConfig `json:"proxy,omitempty"`

	// 扩展配置
	Extensions []ExtensionConfig `json:"extensions,omitempty"`

	// 反检测配置
	AntiDetect AntiDetectConfig `json:"antiDetect"`

	// 元数据
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
	Version   string    `json:"version"`
}

// ScreenConfig 屏幕配置
type ScreenConfig struct {
	Width             int     `json:"width"`
	Height            int     `json:"height"`
	DeviceScaleFactor float64 `json:"deviceScaleFactor"`
	ColorDepth        int     `json:"colorDepth"`
	PixelDepth        int     `json:"pixelDepth"`
	AvailWidth        int     `json:"availWidth"`
	AvailHeight       int     `json:"availHeight"`
	Orientation       string  `json:"orientation"` // portrait, landscape
}

// WebRTCConfig WebRTC配置
type WebRTCConfig struct {
	Mode            string `json:"mode"` // disabled, default, public_only
	LocalIPHandling string `json:"localIPHandling"`
	PublicIP        string `json:"publicIP,omitempty"`
	LocalIP         string `json:"localIP,omitempty"`
	FakeIP          string `json:"fakeIP,omitempty"`
	BlockMediaDevices bool `json:"blockMediaDevices"`
}

// CanvasConfig Canvas指纹配置
type CanvasConfig struct {
	NoiseEnabled     bool    `json:"noiseEnabled"`
	NoiseLevel       float64 `json:"noiseLevel"`
	BlockEnabled     bool    `json:"blockEnabled"`
	CustomSignature  string  `json:"customSignature,omitempty"`
	RandomValues     bool    `json:"randomValues"`
	ConsistentNoise  bool    `json:"consistentNoise"`
	NoiseType        string  `json:"noiseType"` // uniform, gaussian
}

// WebGLConfig WebGL指纹配置
type WebGLConfig struct {
	Vendor              string            `json:"vendor"`
	Renderer            string            `json:"renderer"`
	UnmaskedVendor      string            `json:"unmaskedVendor"`
	UnmaskedRenderer    string            `json:"unmaskedRenderer"`
	RandomizeParameters bool              `json:"randomizeParameters"`
	CustomParameters    map[string]string `json:"customParameters,omitempty"`
	BlockEnabled        bool              `json:"blockEnabled"`
}

// FontConfig 字体配置
type FontConfig struct {
	MaskFonts       bool     `json:"maskFonts"`
	CustomFonts     []string `json:"customFonts"`
	FontBlacklist   []string `json:"fontBlacklist"`
	FontWhitelist   []string `json:"fontWhitelist,omitempty"`
	RandomizeFonts  bool     `json:"randomizeFonts"`
	FakeFontMetrics bool     `json:"fakeFontMetrics"`
}

// HardwareConfig 硬件配置
type HardwareConfig struct {
	CPUClass            string `json:"cpuClass"`
	HardwareConcurrency int    `json:"hardwareConcurrency"`
	DeviceMemory        int    `json:"deviceMemory"`
	Platform            string `json:"platform"`
	MaxTouchPoints      int    `json:"maxTouchPoints"`
	Vendor              string `json:"vendor,omitempty"`
	Product             string `json:"product,omitempty"`
	OscpuOverride       string `json:"oscpuOverride,omitempty"`
}

// NetworkConfig 网络配置
type NetworkConfig struct {
	ConnectionType     string            `json:"connectionType"`
	DownloadSpeed      float64           `json:"downloadSpeed"`
	UploadSpeed        float64           `json:"uploadSpeed"`
	RTT                int               `json:"rtt"` // Round Trip Time
	EffectiveType      string            `json:"effectiveType"`
	CustomHeaders      map[string]string `json:"customHeaders"`
	DNSServers         []string          `json:"dnsServers"`
	AcceptLanguage     string            `json:"acceptLanguage"`
	AcceptEncoding     string            `json:"acceptEncoding"`
	DoNotTrack         bool              `json:"doNotTrack"`
}

// ExtensionConfig 扩展配置
type ExtensionConfig struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Enabled bool   `json:"enabled"`
	Path    string `json:"path,omitempty"`
	Version string `json:"version,omitempty"`
	Type    string `json:"type"` // local, store, unpacked
}

// AntiDetectConfig 反检测配置
type AntiDetectConfig struct {
	StealthMode           bool `json:"stealthMode"`
	DisableWebDriver      bool `json:"disableWebDriver"`
	DisableAutomation     bool `json:"disableAutomation"`
	MaskAutomationSignals bool `json:"maskAutomationSignals"`
	RandomizeTimings      bool `json:"randomizeTimings"`
	SimulateHuman         bool `json:"simulateHuman"`
	
	// 高级反检测
	MaskPlugins         bool `json:"maskPlugins"`
	MaskMimeTypes       bool `json:"maskMimeTypes"`
	MaskPermissions     bool `json:"maskPermissions"`
	FakeNotifications   bool `json:"fakeNotifications"`
	DisableHeadlessDetection bool `json:"disableHeadlessDetection"`
	
	// 行为模拟
	MouseMovementDelay  int `json:"mouseMovementDelay"`  // milliseconds
	TypingDelay         int `json:"typingDelay"`         // milliseconds
	ScrollDelay         int `json:"scrollDelay"`         // milliseconds
	ClickDelay          int `json:"clickDelay"`          // milliseconds
	HumanLikeMovement   bool `json:"humanLikeMovement"`
}

// FingerprintProfile 扩展的配置文件，包含指纹信息
type FingerprintProfile struct {
	*Profile                           // 嵌入原有Profile
	Fingerprint       *FingerprintConfig `json:"fingerprint"`
	AutomationEnabled bool               `json:"automationEnabled"`
	LastFingerprint   *time.Time         `json:"lastFingerprint,omitempty"`
	FingerprintHash   string             `json:"fingerprintHash"` // 指纹哈希值，用于快速比较
	TestResults       *TestResults       `json:"testResults,omitempty"`
}

// TestResults 指纹测试结果
type TestResults struct {
	CanvasFingerprint string    `json:"canvasFingerprint"`
	WebGLFingerprint  string    `json:"webglFingerprint"`
	AudioFingerprint  string    `json:"audioFingerprint"`
	FontFingerprint   string    `json:"fontFingerprint"`
	TestedAt          time.Time `json:"testedAt"`
	TestURL           string    `json:"testURL"`
	Success           bool      `json:"success"`
}

// LaunchOptionsFingerprint 扩展的启动选项，包含指纹特定设置
type LaunchOptionsFingerprint struct {
	*LaunchOptions
	
	// 指纹特定选项
	ApplyFingerprint    bool   `json:"applyFingerprint"`
	FingerprintProfile  string `json:"fingerprintProfile,omitempty"`
	AntiDetectLevel     string `json:"antiDetectLevel"` // low, medium, high
	StealthMode         bool   `json:"stealthMode"`
	
	// 自动化选项
	EnableAutomation    bool   `json:"enableAutomation"`
	AutomationTimeout   string `json:"automationTimeout"`
	KeepSessionAlive    bool   `json:"keepSessionAlive"`
}

// FingerprintTemplate 指纹模板，预定义的指纹配置
type FingerprintTemplate struct {
	Name        string             `json:"name"`
	Description string             `json:"description"`
	Platform    string             `json:"platform"` // windows, macos, linux, mobile
	Category    string             `json:"category"` // standard, gaming, business, mobile
	Config      *FingerprintConfig `json:"config"`
	CreatedAt   time.Time          `json:"createdAt"`
	Popular     bool               `json:"popular"`
	Verified    bool               `json:"verified"`
}

// FingerprintStats 指纹统计信息
type FingerprintStats struct {
	TotalProfiles     int               `json:"totalProfiles"`
	ActiveProfiles    int               `json:"activeProfiles"`
	SuccessRate       float64           `json:"successRate"`
	PlatformDistrib   map[string]int    `json:"platformDistribution"`
	UsageCount        map[string]int    `json:"usageCount"`
	LastUsed          map[string]time.Time `json:"lastUsed"`
	DetectionEvents   []DetectionEvent  `json:"detectionEvents"`
}

// DetectionEvent 检测事件记录
type DetectionEvent struct {
	ProfileID   string    `json:"profileId"`
	URL         string    `json:"url"`
	DetectedAt  time.Time `json:"detectedAt"`
	EventType   string    `json:"eventType"` // captcha, blocked, challenge
	Severity    string    `json:"severity"`  // low, medium, high
	UserAgent   string    `json:"userAgent"`
	Description string    `json:"description"`
}

// ToJSON 将指纹配置序列化为JSON
func (fc *FingerprintConfig) ToJSON() ([]byte, error) {
	return json.MarshalIndent(fc, "", "  ")
}

// FromJSON 从JSON反序列化指纹配置
func (fc *FingerprintConfig) FromJSON(data []byte) error {
	return json.Unmarshal(data, fc)
}

// Clone 克隆指纹配置
func (fc *FingerprintConfig) Clone() *FingerprintConfig {
	data, err := fc.ToJSON()
	if err != nil {
		return nil
	}
	
	var clone FingerprintConfig
	if err := clone.FromJSON(data); err != nil {
		return nil
	}
	
	return &clone
}

// Validate 验证指纹配置
func (fc *FingerprintConfig) Validate() error {
	if fc.UserAgent == "" {
		return NewValidationError("userAgent", "", "required", "user agent is required")
	}
	
	if fc.ScreenResolution.Width <= 0 || fc.ScreenResolution.Height <= 0 {
		return NewValidationError("screenResolution", "", "invalid", "screen resolution must be positive")
	}
	
	if fc.Hardware.HardwareConcurrency <= 0 {
		return NewValidationError("hardwareConcurrency", "", "invalid", "hardware concurrency must be positive")
	}
	
	return nil
}

// GetHash 获取指纹配置的哈希值
func (fc *FingerprintConfig) GetHash() string {
	data, err := fc.ToJSON()
	if err != nil {
		return ""
	}
	
	// 使用简单的字符串哈希
	hash := 0
	for _, b := range data {
		hash = hash*31 + int(b)
	}
	
	return fmt.Sprintf("%x", hash)
}

// UpdateTimestamp 更新时间戳
func (fc *FingerprintConfig) UpdateTimestamp() {
	fc.UpdatedAt = time.Now()
}

// IsExpired 检查指纹是否过期（基于版本和时间）
func (fc *FingerprintConfig) IsExpired(maxAge time.Duration) bool {
	return time.Since(fc.UpdatedAt) > maxAge
}