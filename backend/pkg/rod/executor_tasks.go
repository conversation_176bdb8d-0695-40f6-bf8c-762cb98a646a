package rod

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/go-rod/rod/lib/proto"
)

// executeNavigate navigates to a URL
func (e *Executor) executeNavigate(task Task) error {
	e.logger.Printf("Navigating to %s", task.URL)
	err := e.page.Navigate(task.URL)
	if err != nil {
		return NewError(ErrCodePageNavigation, "Failed to navigate to URL", err).
			WithContext("url", task.URL)
	}
	return nil
}

// executeNavigateBack navigates back in history
func (e *Executor) executeNavigateBack(task Task) error {
	e.logger.Println("Navigating back")
	err := e.page.NavigateBack()
	if err != nil {
		return NewError(ErrCodePageNavigation, "Failed to navigate back", err)
	}
	return nil
}

// executeNavigateForward navigates forward in history
func (e *Executor) executeNavigateForward(task Task) error {
	e.logger.Println("Navigating forward")
	err := e.page.NavigateForward()
	if err != nil {
		return NewError(ErrCodePageNavigation, "Failed to navigate forward", err)
	}
	return nil
}

// executeReload reloads the current page
func (e *Executor) executeReload(task Task) error {
	e.logger.Println("Reloading page")
	err := e.page.Reload()
	if err != nil {
		return NewError(ErrCodePageNavigation, "Failed to reload page", err)
	}
	return nil
}

// executeClick clicks on an element
func (e *Executor) executeClick(task Task, timeout time.Duration) error {
	e.logger.Printf("Clicking on %s", task.Selector)
	el, err := e.selector.FindElement(task.Selector, task.SelectorType, timeout)
	if err != nil {
		return NewElementNotFoundError(task.Selector, task.SelectorType, err)
	}

	// Check if element is visible and clickable
	visible, err := el.Visible()
	if err != nil || !visible {
		return NewError(ErrCodeElementNotVisible, "Element is not visible", err).
			WithContext("selector", task.Selector)
	}

	err = el.Click(proto.InputMouseButtonLeft, 1)
	if err != nil {
		return NewError(ErrCodeElementInteraction, "Failed to click element", err).
			WithContext("selector", task.Selector)
	}
	return nil
}

// executeDoubleClick double-clicks on an element
func (e *Executor) executeDoubleClick(task Task, timeout time.Duration) error {
	e.logger.Printf("Double-clicking on %s", task.Selector)
	el, err := e.selector.FindElement(task.Selector, task.SelectorType, timeout)
	if err != nil {
		return NewElementNotFoundError(task.Selector, task.SelectorType, err)
	}

	// Check if element is visible and clickable
	visible, err := el.Visible()
	if err != nil || !visible {
		return NewError(ErrCodeElementNotVisible, "Element is not visible", err).
			WithContext("selector", task.Selector)
	}

	err = el.Click(proto.InputMouseButtonLeft, 2)
	if err != nil {
		return NewError(ErrCodeElementInteraction, "Failed to double-click element", err).
			WithContext("selector", task.Selector)
	}
	return nil
}

// executeTypeText types text into an element
func (e *Executor) executeTypeText(task Task, timeout time.Duration) error {
	e.logger.Printf("Typing '%s' into %s", task.Value, task.Selector)
	el, err := e.selector.FindElement(task.Selector, task.SelectorType, timeout)
	if err != nil {
		return NewElementNotFoundError(task.Selector, task.SelectorType, err)
	}

	err = el.Input(task.Value)
	if err != nil {
		return NewError(ErrCodeElementInteraction, "Failed to type text into element", err).
			WithContext("selector", task.Selector).
			WithContext("value", task.Value)
	}
	return nil
}

// executeSelect selects an option in a select element
func (e *Executor) executeSelect(task Task, timeout time.Duration) error {
	e.logger.Printf("Selecting '%s' in %s", task.Value, task.Selector)
	el, err := e.selector.FindElement(task.Selector, task.SelectorType, timeout)
	if err != nil {
		return fmt.Errorf("failed to find element: %w", err)
	}

	// Execute JavaScript to select the option
	_, err = el.Eval(`function(value) {
		this.value = value;
		const event = new Event('change', { bubbles: true });
		this.dispatchEvent(event);
	}`, task.Value)

	return err
}

// executeClear clears an input element
func (e *Executor) executeClear(task Task, timeout time.Duration) error {
	e.logger.Printf("Clearing %s", task.Selector)
	el, err := e.selector.FindElement(task.Selector, task.SelectorType, timeout)
	if err != nil {
		return fmt.Errorf("failed to find element: %w", err)
	}

	// Use JavaScript to clear the input
	_, err = el.Eval(`function() { this.value = ''; }`)
	return err
}

// executeSubmit submits a form
func (e *Executor) executeSubmit(task Task, timeout time.Duration) error {
	e.logger.Printf("Submitting form %s", task.Selector)
	el, err := e.selector.FindElement(task.Selector, task.SelectorType, timeout)
	if err != nil {
		return fmt.Errorf("failed to find element: %w", err)
	}

	// Execute JavaScript to submit the form
	_, err = el.Eval(`function() {
		if (this.tagName.toLowerCase() === 'form') {
			this.submit();
		} else {
			const form = this.closest('form');
			if (form) {
				form.submit();
			}
		}
	}`)

	return err
}

// executeFocus focuses an element
func (e *Executor) executeFocus(task Task, timeout time.Duration) error {
	e.logger.Printf("Focusing %s", task.Selector)
	el, err := e.selector.FindElement(task.Selector, task.SelectorType, timeout)
	if err != nil {
		return fmt.Errorf("failed to find element: %w", err)
	}
	return el.Focus()
}

// executeBlur removes focus from an element
func (e *Executor) executeBlur(task Task, timeout time.Duration) error {
	e.logger.Printf("Blurring %s", task.Selector)
	el, err := e.selector.FindElement(task.Selector, task.SelectorType, timeout)
	if err != nil {
		return fmt.Errorf("failed to find element: %w", err)
	}

	// Execute JavaScript to blur the element
	_, err = el.Eval(`function() { this.blur(); }`)
	return err
}

// executeScrollIntoView scrolls an element into view
func (e *Executor) executeScrollIntoView(task Task, timeout time.Duration) error {
	e.logger.Printf("Scrolling %s into view", task.Selector)
	el, err := e.selector.FindElement(task.Selector, task.SelectorType, timeout)
	if err != nil {
		return fmt.Errorf("failed to find element: %w", err)
	}
	return el.ScrollIntoView()
}

// executeWaitVisible waits for an element to become visible
func (e *Executor) executeWaitVisible(task Task, timeout time.Duration) error {
	e.logger.Printf("Waiting for %s to become visible", task.Selector)
	_, err := e.selector.WaitForElementVisible(task.Selector, task.SelectorType, timeout)
	if err != nil {
		return NewError(ErrCodeElementNotVisible, "Element did not become visible within timeout", err).
			WithContext("selector", task.Selector).
			WithContext("timeout", timeout.String())
	}
	return nil
}

// executeWaitNotVisible waits for an element to become invisible
func (e *Executor) executeWaitNotVisible(task Task, timeout time.Duration) error {
	e.logger.Printf("Waiting for %s to become invisible", task.Selector)
	return e.selector.WaitForElementNotVisible(task.Selector, task.SelectorType, timeout)
}

// executeWaitPresent waits for an element to be present in the DOM
func (e *Executor) executeWaitPresent(task Task, timeout time.Duration) error {
	e.logger.Printf("Waiting for %s to be present", task.Selector)
	_, err := e.selector.WaitForElementPresent(task.Selector, task.SelectorType, timeout)
	return err
}

// executeWaitNotPresent waits for an element to not be present in the DOM
func (e *Executor) executeWaitNotPresent(task Task, timeout time.Duration) error {
	e.logger.Printf("Waiting for %s to not be present", task.Selector)
	return e.selector.WaitForElementNotPresent(task.Selector, task.SelectorType, timeout)
}

// executeWaitEnabled waits for an element to be enabled
func (e *Executor) executeWaitEnabled(task Task, timeout time.Duration) error {
	e.logger.Printf("Waiting for %s to be enabled", task.Selector)
	_, err := e.selector.WaitForElementEnabled(task.Selector, task.SelectorType, timeout)
	return err
}

// executeSleep waits for a specified duration
func (e *Executor) executeSleep(task Task) error {
	duration, err := ParseTimeout(task.WaitTime)
	if err != nil {
		return fmt.Errorf("invalid wait time: %w", err)
	}
	e.logger.Printf("Sleeping for %s", duration)
	time.Sleep(duration)
	return nil
}

// executeGetText gets the text content of an element
func (e *Executor) executeGetText(task Task, timeout time.Duration) (string, error) {
	e.logger.Printf("Getting text from %s", task.Selector)
	el, err := e.selector.FindElement(task.Selector, task.SelectorType, timeout)
	if err != nil {
		return "", NewElementNotFoundError(task.Selector, task.SelectorType, err)
	}

	text, err := el.Text()
	if err != nil {
		return "", NewError(ErrCodeElementInteraction, "Failed to get text from element", err).
			WithContext("selector", task.Selector)
	}
	return text, nil
}

// executeGetHTML gets the inner HTML of an element
func (e *Executor) executeGetHTML(task Task, timeout time.Duration) (string, error) {
	e.logger.Printf("Getting HTML from %s", task.Selector)
	el, err := e.selector.FindElement(task.Selector, task.SelectorType, timeout)
	if err != nil {
		return "", fmt.Errorf("failed to find element: %w", err)
	}

	html, err := el.Property("innerHTML")
	if err != nil {
		return "", fmt.Errorf("failed to get innerHTML: %w", err)
	}

	return html.String(), nil
}

// executeGetOuterHTML gets the outer HTML of an element
func (e *Executor) executeGetOuterHTML(task Task, timeout time.Duration) (string, error) {
	e.logger.Printf("Getting outer HTML from %s", task.Selector)
	el, err := e.selector.FindElement(task.Selector, task.SelectorType, timeout)
	if err != nil {
		return "", fmt.Errorf("failed to find element: %w", err)
	}

	html, err := el.Property("outerHTML")
	if err != nil {
		return "", fmt.Errorf("failed to get outerHTML: %w", err)
	}

	return html.String(), nil
}

// executeGetAttribute gets an attribute of an element
func (e *Executor) executeGetAttribute(task Task, timeout time.Duration) (string, error) {
	e.logger.Printf("Getting attribute %s from %s", task.Attribute, task.Selector)
	el, err := e.selector.FindElement(task.Selector, task.SelectorType, timeout)
	if err != nil {
		return "", fmt.Errorf("failed to find element: %w", err)
	}

	attr, err := el.Attribute(task.Attribute)
	if err != nil {
		return "", fmt.Errorf("failed to get attribute: %w", err)
	}

	if attr == nil {
		return "", nil
	}

	return *attr, nil
}

// executeGetValue gets the value of an input element
func (e *Executor) executeGetValue(task Task, timeout time.Duration) (string, error) {
	e.logger.Printf("Getting value from %s", task.Selector)
	el, err := e.selector.FindElement(task.Selector, task.SelectorType, timeout)
	if err != nil {
		return "", fmt.Errorf("failed to find element: %w", err)
	}

	value, err := el.Property("value")
	if err != nil {
		return "", fmt.Errorf("failed to get value: %w", err)
	}

	return value.String(), nil
}

// executeScreenshot takes a screenshot of an element
func (e *Executor) executeScreenshot(task Task, timeout time.Duration) ([]byte, error) {
	e.logger.Printf("Taking screenshot of %s", task.Selector)
	var screenshot []byte
	var err error

	if task.Selector == "" {
		// Full page screenshot
		screenshot, err = e.page.Screenshot(true, nil)
	} else {
		// Element screenshot
		el, err := e.selector.FindElement(task.Selector, task.SelectorType, timeout)
		if err != nil {
			return nil, fmt.Errorf("failed to find element: %w", err)
		}

		// Use JavaScript to take a screenshot of the element
		// First scroll the element into view
		err = el.ScrollIntoView()
		if err != nil {
			return nil, fmt.Errorf("failed to scroll element into view: %w", err)
		}

		// Wait for the page to stabilize
		err = e.page.WaitStable(1 * time.Second)
		if err != nil {
			return nil, fmt.Errorf("failed to wait for page to stabilize: %w", err)
		}

		// Take a full page screenshot
		screenshot, err = e.page.Screenshot(true, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to take screenshot: %w", err)
		}
	}

	// Save the screenshot to a file if a filename is provided
	if task.FileName != "" {
		err = os.WriteFile(task.FileName, screenshot, 0644)
		if err != nil {
			return nil, fmt.Errorf("failed to save screenshot: %w", err)
		}
	}

	return screenshot, nil
}

// executeFullScreenshot takes a full page screenshot
func (e *Executor) executeFullScreenshot(task Task) ([]byte, error) {
	e.logger.Printf("Taking full page screenshot")
	screenshot, err := e.page.Screenshot(true, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to take screenshot: %w", err)
	}

	// Save the screenshot to a file if a filename is provided
	if task.FileName != "" {
		err = os.WriteFile(task.FileName, screenshot, 0644)
		if err != nil {
			return nil, fmt.Errorf("failed to save screenshot: %w", err)
		}
	}

	return screenshot, nil
}

// executeEvaluate evaluates JavaScript code
func (e *Executor) executeEvaluate(task Task) (interface{}, error) {
	e.logger.Printf("Evaluating JavaScript: %s", task.JavaScript)
	result, err := e.page.Eval(task.JavaScript)
	if err != nil {
		return nil, fmt.Errorf("failed to evaluate JavaScript: %w", err)
	}

	// Convert the result to a Go value
	return result.Value.Raw(), nil
}

// executePoll polls until a JavaScript expression returns true
func (e *Executor) executePoll(task Task, timeout time.Duration) (interface{}, error) {
	e.logger.Printf("Polling JavaScript: %s", task.JavaScript)

	// Create a context with timeout
	ctx, cancel := context.WithTimeout(e.page.GetContext(), timeout)
	defer cancel()

	// Poll until the JavaScript returns true or timeout
	var lastResult interface{}
	for {
		select {
		case <-ctx.Done():
			return lastResult, fmt.Errorf("timeout polling JavaScript")
		default:
			// Evaluate the JavaScript
			result, err := e.page.Eval(task.JavaScript)
			if err != nil {
				return nil, fmt.Errorf("failed to evaluate JavaScript: %w", err)
			}

			// Store the last result
			lastResult = result.Value.Raw()

			// If the result is truthy, return it
			if result.Value.Bool() {
				return lastResult, nil
			}

			// Wait a bit before trying again
			time.Sleep(100 * time.Millisecond)
		}
	}
}

// executeIf executes tasks conditionally
func (e *Executor) executeIf(task Task) error {
	e.logger.Printf("Evaluating condition: %s", task.Condition)

	// Evaluate the condition
	result, err := e.page.Eval(task.Condition)
	if err != nil {
		return fmt.Errorf("failed to evaluate condition: %w", err)
	}

	// If the condition is true, execute the subtasks
	if result.Value.Bool() {
		e.logger.Println("Condition is true, executing subtasks")
		for i, subtask := range task.Tasks {
			// Substitute variables in the subtask
			SubstituteTaskVariables(&subtask, e.taskCtx.Variables)

			// Execute the subtask
			result, err := e.executeTask(subtask)
			if err != nil {
				return fmt.Errorf("subtask %d failed: %w", i, err)
			}

			// Store the result if a variable name was provided
			if subtask.Variable != "" {
				e.taskCtx.Results[subtask.Variable] = result
				e.taskCtx.Variables[subtask.Variable] = result.Value
			}
		}
	} else {
		e.logger.Println("Condition is false, skipping subtasks")
	}

	return nil
}

// executeLoop executes tasks in a loop
func (e *Executor) executeLoop(task Task) error {
	e.logger.Printf("Executing loop")

	// Get the number of iterations
	iterations := 1
	if task.Value != "" {
		var err error
		iterations, err = strconv.Atoi(task.Value)
		if err != nil {
			return fmt.Errorf("invalid loop count: %w", err)
		}
	}

	// Execute the subtasks in a loop
	for i := 0; i < iterations; i++ {
		e.logger.Printf("Loop iteration %d", i+1)
		for j, subtask := range task.Tasks {
			// Substitute variables in the subtask
			SubstituteTaskVariables(&subtask, e.taskCtx.Variables)

			// Execute the subtask
			result, err := e.executeTask(subtask)
			if err != nil {
				return fmt.Errorf("subtask %d failed: %w", j, err)
			}

			// Store the result if a variable name was provided
			if subtask.Variable != "" {
				e.taskCtx.Results[subtask.Variable] = result
				e.taskCtx.Variables[subtask.Variable] = result.Value
			}
		}
	}

	return nil
}

// executeForEach executes tasks for each item in an array
func (e *Executor) executeForEach(task Task) error {
	e.logger.Printf("Executing for-each")

	// Get the array to iterate over
	arrayVar := task.Value
	array, ok := e.taskCtx.Variables[arrayVar]
	if !ok {
		return fmt.Errorf("variable %s not found", arrayVar)
	}

	// Convert the array to a slice
	arraySlice, ok := array.([]interface{})
	if !ok {
		return fmt.Errorf("variable %s is not an array", arrayVar)
	}

	// Execute the subtasks for each item in the array
	for i, item := range arraySlice {
		e.logger.Printf("For-each iteration %d", i+1)

		// Add the current item to the variables
		e.taskCtx.Variables["item"] = item
		e.taskCtx.Variables["index"] = i

		for j, subtask := range task.Tasks {
			// Substitute variables in the subtask
			SubstituteTaskVariables(&subtask, e.taskCtx.Variables)

			// Execute the subtask
			result, err := e.executeTask(subtask)
			if err != nil {
				return fmt.Errorf("subtask %d failed: %w", j, err)
			}

			// Store the result if a variable name was provided
			if subtask.Variable != "" {
				e.taskCtx.Results[subtask.Variable] = result
				e.taskCtx.Variables[subtask.Variable] = result.Value
			}
		}
	}

	return nil
}

// executeDetectChallenge detects if there's a challenge on the current page
func (e *Executor) executeDetectChallenge(task Task) (string, error) {
	e.logger.Println("Detecting challenges")
	challengeType, err := e.challengeHandler.DetectChallenge(e.page)
	if err != nil {
		return "", fmt.Errorf("failed to detect challenge: %w", err)
	}
	return string(challengeType), nil
}

// executeHandleChallenge attempts to handle the detected challenge
func (e *Executor) executeHandleChallenge(task Task) error {
	e.logger.Println("Handling challenge")
	challengeType := ChallengeType(task.Value)
	if challengeType == "" {
		// Detect the challenge type
		var err error
		challengeType, err = e.challengeHandler.DetectChallenge(e.page)
		if err != nil {
			return fmt.Errorf("failed to detect challenge: %w", err)
		}
	}

	// Handle the challenge
	return e.challengeHandler.HandleChallenge(e.page, challengeType)
}

// executeSolveReCaptcha attempts to solve a reCAPTCHA challenge
func (e *Executor) executeSolveReCaptcha(task Task, timeout time.Duration) error {
	e.logger.Println("Solving reCAPTCHA")
	return e.challengeHandler.HandleChallenge(e.page, ChallengeReCaptcha)
}

// executeSolveCloudflare attempts to solve a Cloudflare challenge
func (e *Executor) executeSolveCloudflare(task Task, timeout time.Duration) error {
	e.logger.Println("Solving Cloudflare challenge")
	return e.challengeHandler.HandleChallenge(e.page, ChallengeCloudflare)
}

// executeMonitorChallenges starts monitoring for challenges
func (e *Executor) executeMonitorChallenges(task Task) error {
	e.logger.Println("Starting challenge monitoring")
	e.challengeHandler.MonitorForChallenges(e.page)
	return nil
}

// executeInstallExtension installs a Chrome extension
func (e *Executor) executeInstallExtension(task Task) error {
	e.logger.Printf("Installing extension: %s", task.Value)
	extensionPath := task.Value

	// Check if it's a URL or a local file
	if strings.HasPrefix(extensionPath, "http") {
		// URL - could be a Chrome Web Store URL or a direct download URL
		if strings.Contains(extensionPath, "chrome.google.com/webstore") {
			// Chrome Web Store URL
			return e.installExtensionFromChromeWebStore(extensionPath)
		} else {
			// Direct download URL
			return e.installExtensionFromURL(extensionPath)
		}
	} else {
		// Local file
		absPath, err := filepath.Abs(extensionPath)
		if err != nil {
			return fmt.Errorf("failed to get absolute path for extension: %w", err)
		}

		// Check if the file exists
		if _, err := os.Stat(absPath); os.IsNotExist(err) {
			return fmt.Errorf("extension file does not exist: %s", absPath)
		}

		// Rod doesn't support loading extensions at runtime
		// We need to restart the browser with the extension
		e.logger.Printf("Extensions can only be loaded at browser startup. Adding to settings for next launch.")

		// Add the extension to the settings for next launch
		e.settings.Extensions = append(e.settings.Extensions, absPath)
	}

	return nil
}

// installExtensionFromChromeWebStore installs an extension from the Chrome Web Store
func (e *Executor) installExtensionFromChromeWebStore(url string) error {
	e.logger.Printf("Installing extension from Chrome Web Store: %s", url)

	// Extract the extension ID from the URL
	extensionID := extractExtensionID(url)
	if extensionID == "" {
		return fmt.Errorf("could not extract extension ID from URL: %s", url)
	}

	e.logger.Printf("Extracted extension ID: %s", extensionID)

	// Navigate to the extension page
	err := e.page.Navigate(url)
	if err != nil {
		return fmt.Errorf("failed to navigate to extension page: %w", err)
	}

	// Wait for the page to load
	err = e.page.WaitLoad()
	if err != nil {
		return fmt.Errorf("failed to wait for page load: %w", err)
	}

	// Try to find and click the "Add to Chrome" button
	// This is a complex operation that might not work in all cases due to Chrome's security restrictions
	clicked, err := e.page.Eval(`() => {
		// Try to find the "Add to Chrome" button
		const addButton = Array.from(document.querySelectorAll('button')).find(
			button => button.textContent.includes('Add to Chrome') ||
					 button.textContent.includes('Add to Brave') ||
					 button.textContent.includes('Add to Edge')
		);

		if (addButton) {
			addButton.click();
			return true;
		}

		return false;
	}`)

	if err != nil {
		return fmt.Errorf("failed to find Add button: %w", err)
	}

	if !clicked.Value.Bool() {
		e.logger.Println("Could not find Add button. Extensions from Chrome Web Store may need to be installed manually.")
		e.logger.Println("Adding extension ID to settings for next launch if browser is started with --load-extension flag.")

		// Store the extension ID for future use
		e.settings.ExtensionIDs = append(e.settings.ExtensionIDs, extensionID)
		return fmt.Errorf("could not automatically install extension from Chrome Web Store, manual installation required")
	}

	// Wait for the confirmation dialog
	time.Sleep(2 * time.Second)

	// Try to click the confirmation button
	confirmed, err := e.page.Eval(`() => {
		// Try to find the confirmation button in the dialog
		const confirmButton = Array.from(document.querySelectorAll('button')).find(
			button => button.textContent.includes('Add extension') ||
					 button.textContent.includes('Add')
		);

		if (confirmButton) {
			confirmButton.click();
			return true;
		}

		return false;
	}`)

	if err != nil {
		return fmt.Errorf("failed to find confirmation button: %w", err)
	}

	if !confirmed.Value.Bool() {
		e.logger.Println("Could not find confirmation button. Extension may need to be installed manually.")
		return fmt.Errorf("could not automatically confirm extension installation")
	}

	// Wait for the installation to complete
	time.Sleep(5 * time.Second)

	e.logger.Println("Extension installation initiated. Note that the extension may not be fully functional until the browser is restarted.")

	// Store the extension ID for future use
	e.settings.ExtensionIDs = append(e.settings.ExtensionIDs, extensionID)

	return nil
}

// installExtensionFromURL installs an extension from a direct download URL
func (e *Executor) installExtensionFromURL(url string) error {
	e.logger.Printf("Installing extension from URL: %s", url)

	// Create a temporary directory to download the extension
	tempDir, err := os.MkdirTemp("", "rod-extension-*")
	if err != nil {
		return fmt.Errorf("failed to create temp directory: %w", err)
	}

	// Download the extension file
	resp, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("failed to download extension: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to download extension, status code: %d", resp.StatusCode)
	}

	// Determine the file extension
	contentType := resp.Header.Get("Content-Type")
	var ext string
	switch contentType {
	case "application/zip", "application/x-zip-compressed":
		ext = ".zip"
	case "application/x-chrome-extension":
		ext = ".crx"
	default:
		// Default to zip if we can't determine the type
		ext = ".zip"
	}

	// Create the extension file
	extFile := filepath.Join(tempDir, "extension"+ext)
	file, err := os.Create(extFile)
	if err != nil {
		return fmt.Errorf("failed to create extension file: %w", err)
	}

	// Save the extension file
	_, err = io.Copy(file, resp.Body)
	file.Close()
	if err != nil {
		return fmt.Errorf("failed to save extension file: %w", err)
	}

	e.logger.Printf("Downloaded extension to: %s", extFile)

	// If it's a CRX file, we need to extract it
	if ext == ".crx" {
		extractedDir := filepath.Join(tempDir, "extracted")
		err = os.MkdirAll(extractedDir, 0755)
		if err != nil {
			return fmt.Errorf("failed to create extraction directory: %w", err)
		}

		// Extract the CRX file (this is a simplified approach)
		e.logger.Println("Extracting CRX file...")
		// In a real implementation, we would need to handle the CRX format properly
		// For now, we'll just assume it's a ZIP file with a different header

		// Add the extracted directory to the settings
		e.settings.Extensions = append(e.settings.Extensions, extractedDir)
	} else {
		// For ZIP files, add the file directly
		e.settings.Extensions = append(e.settings.Extensions, extFile)
	}

	e.logger.Println("Extension added to settings for next launch")
	return nil
}

// extractExtensionID extracts the extension ID from a Chrome Web Store URL
func extractExtensionID(url string) string {
	// Chrome Web Store URLs are in the format:
	// https://chrome.google.com/webstore/detail/[extension-name]/[extension-id]
	// or sometimes just:
	// https://chrome.google.com/webstore/detail/[extension-id]

	parts := strings.Split(url, "/")
	for i, part := range parts {
		if part == "detail" && i+1 < len(parts) {
			// Check if the next part is the extension name or ID
			if i+2 < len(parts) && len(parts[i+2]) == 32 {
				// Format is /detail/name/id
				return parts[i+2]
			} else if len(parts[i+1]) == 32 {
				// Format is /detail/id
				return parts[i+1]
			}
		}
	}

	return ""
}
