package rod

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestBrowserAutomationError(t *testing.T) {
	// Test creating a new error
	err := NewError(ErrCodeElementNotFound, "Element not found", nil)
	assert.NotNil(t, err)
	assert.Equal(t, ErrCodeElementNotFound, err.Code)
	assert.Equal(t, "Element not found", err.Message)
	assert.Contains(t, err.Error(), ErrCodeElementNotFound)
	assert.Contains(t, err.Error(), "Element not found")
}

func TestBrowserAutomationErrorWithCause(t *testing.T) {
	cause := errors.New("underlying error")
	err := NewError(ErrCodeBrowserInit, "<PERSON><PERSON><PERSON> failed to initialize", cause)

	assert.Equal(t, cause, err.Unwrap())
	assert.Contains(t, err.Error(), "underlying error")
}

func TestBrowserAutomationErrorWithContext(t *testing.T) {
	err := NewElementNotFoundError("button#submit", Selector<PERSON><PERSON>y, nil)

	assert.Equal(t, ErrCodeElementNotFound, err.Code)
	assert.NotNil(t, err.Context)
	assert.Equal(t, "button#submit", err.Context["selector"])
	assert.Equal(t, "query", err.Context["selector_type"])
}

func TestErrorCheckers(t *testing.T) {
	// Test browser error checker
	browserErr := NewBrowserInitError(nil)
	assert.True(t, IsBrowserError(browserErr))
	assert.False(t, IsElementError(browserErr))
	assert.False(t, IsTimeoutError(browserErr))

	// Test element error checker
	elementErr := NewElementNotFoundError("div", SelectorQuery, nil)
	assert.True(t, IsElementError(elementErr))
	assert.False(t, IsBrowserError(elementErr))

	// Test timeout error
	timeoutErr := NewError(ErrCodeTaskTimeout, "Task timed out", nil)
	assert.True(t, IsTimeoutError(timeoutErr))
	assert.False(t, IsBrowserError(timeoutErr))
}

func TestErrorGroup(t *testing.T) {
	errorGroup := NewErrorGroup()

	// Test empty error group
	assert.False(t, errorGroup.HasErrors())
	assert.Nil(t, errorGroup.FirstError())

	// Add errors
	err1 := NewError(ErrCodeElementNotFound, "First error", nil)
	err2 := NewError(ErrCodeBrowserInit, "Second error", nil)

	errorGroup.Add(err1)
	errorGroup.Add(err2)

	assert.True(t, errorGroup.HasErrors())
	assert.Equal(t, err1, errorGroup.FirstError())
	assert.Len(t, errorGroup.Errors, 2)

	// Test error message
	errMsg := errorGroup.Error()
	assert.Contains(t, errMsg, "multiple errors (2)")
	assert.Contains(t, errMsg, "First error")
	assert.Contains(t, errMsg, "Second error")
}

func TestIsRetryableError(t *testing.T) {
	// Retryable errors
	retryableErr := NewError(ErrCodeNetworkTimeout, "Network timeout", nil)
	assert.True(t, IsRetryableError(retryableErr))

	networkErr := NewError(ErrCodeNetworkError, "Network error", nil)
	assert.True(t, IsRetryableError(networkErr))

	// Non-retryable errors
	configErr := NewError(ErrCodeConfigInvalid, "Invalid config", nil)
	assert.False(t, IsRetryableError(configErr))

	browserErr := NewBrowserInitError(nil)
	assert.False(t, IsRetryableError(browserErr))
}

func TestErrorWithContextChaining(t *testing.T) {
	err := NewError(ErrCodeElementNotFound, "Element not found", nil).
		WithContext("selector", "button#submit").
		WithContext("timeout", "5s").
		WithContext("retry_count", 3)

	assert.Equal(t, "button#submit", err.Context["selector"])
	assert.Equal(t, "5s", err.Context["timeout"])
	assert.Equal(t, 3, err.Context["retry_count"])
}

func TestPredefinedErrorConstructors(t *testing.T) {
	// Test NewTaskExecutionError
	taskErr := NewTaskExecutionError(TaskClick, "Click button", errors.New("click failed"))
	assert.Equal(t, ErrCodeTaskExecution, taskErr.Code)
	assert.Equal(t, "click", taskErr.Context["task_type"])
	assert.Equal(t, "Click button", taskErr.Context["description"])

	// Test NewChallengeHandlingError
	challengeErr := NewChallengeHandlingError(ChallengeCloudflare, errors.New("challenge failed"))
	assert.Equal(t, ErrCodeChallengeHandling, challengeErr.Code)
	assert.Equal(t, "cloudflare", challengeErr.Context["challenge_type"])

	// Test NewConfigValidationError
	validationErrors := []string{"error1", "error2"}
	configErr := NewConfigValidationError("Validation failed", validationErrors)
	assert.Equal(t, ErrCodeConfigValidation, configErr.Code)
	assert.Equal(t, validationErrors, configErr.Context["validation_errors"])
}
