package rod

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
)

// ExtensionManager handles Chrome extension management
type ExtensionManager struct {
	logger      Logger
	userDataDir string
}

// NewExtensionManager creates a new extension manager
func NewExtensionManager(logger Logger, userDataDir string) *ExtensionManager {
	return &ExtensionManager{
		logger:      logger,
		userDataDir: userDataDir,
	}
}

// ExtensionInfo represents information about an installed extension
type ExtensionInfo struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Version     string `json:"version"`
	Description string `json:"description"`
	Enabled     bool   `json:"enabled"`
	Path        string `json:"path"`
}

// ListInstalledExtensions returns a list of installed extensions
func (em *ExtensionManager) ListInstalledExtensions() ([]ExtensionInfo, error) {
	if em.userDataDir == "" {
		return nil, fmt.Errorf("user data directory not set")
	}

	// Extensions are stored in the "Extensions" directory in the user data directory
	extensionsDir := filepath.Join(em.userDataDir, "Extensions")
	if _, err := os.Stat(extensionsDir); os.IsNotExist(err) {
		// Extensions directory doesn't exist, which means no extensions are installed
		return []ExtensionInfo{}, nil
	}

	// Read the extensions directory
	entries, err := os.ReadDir(extensionsDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read extensions directory: %w", err)
	}

	var extensions []ExtensionInfo
	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}

		// Each subdirectory is an extension ID
		extensionID := entry.Name()

		// Get the extension versions
		versionsDir := filepath.Join(extensionsDir, extensionID)
		versionEntries, err := os.ReadDir(versionsDir)
		if err != nil {
			em.logger.Printf("Failed to read versions for extension %s: %v", extensionID, err)
			continue
		}

		// Use the latest version
		if len(versionEntries) == 0 {
			continue
		}

		// Sort versions and use the latest one
		// In a real implementation, we would parse and compare version numbers
		// For simplicity, we'll just use the first one
		version := versionEntries[0].Name()
		extensionDir := filepath.Join(versionsDir, version)

		// Read the manifest.json file to get extension information
		manifestPath := filepath.Join(extensionDir, "manifest.json")
		manifestData, err := os.ReadFile(manifestPath)
		if err != nil {
			em.logger.Printf("Failed to read manifest for extension %s: %v", extensionID, err)
			continue
		}

		var manifest struct {
			Name        string `json:"name"`
			Version     string `json:"version"`
			Description string `json:"description"`
		}

		err = json.Unmarshal(manifestData, &manifest)
		if err != nil {
			em.logger.Printf("Failed to parse manifest for extension %s: %v", extensionID, err)
			continue
		}

		// Check if the extension is enabled
		// Chrome stores this information in the Preferences file
		enabled := true // Default to enabled
		preferencesPath := filepath.Join(em.userDataDir, "Default", "Preferences")
		if _, err := os.Stat(preferencesPath); err == nil {
			preferencesData, err := os.ReadFile(preferencesPath)
			if err == nil {
				var preferences struct {
					Extensions struct {
						Settings map[string]struct {
							State int `json:"state"`
						} `json:"settings"`
					} `json:"extensions"`
				}

				err = json.Unmarshal(preferencesData, &preferences)
				if err == nil {
					if setting, ok := preferences.Extensions.Settings[extensionID]; ok {
						// State 1 = enabled, 0 = disabled
						enabled = setting.State == 1
					}
				}
			}
		}

		extensions = append(extensions, ExtensionInfo{
			ID:          extensionID,
			Name:        manifest.Name,
			Version:     manifest.Version,
			Description: manifest.Description,
			Enabled:     enabled,
			Path:        extensionDir,
		})
	}

	return extensions, nil
}

// EnableExtension enables an extension
func (em *ExtensionManager) EnableExtension(extensionID string) error {
	return em.setExtensionState(extensionID, true)
}

// DisableExtension disables an extension
func (em *ExtensionManager) DisableExtension(extensionID string) error {
	return em.setExtensionState(extensionID, false)
}

// setExtensionState sets the enabled state of an extension
func (em *ExtensionManager) setExtensionState(extensionID string, enabled bool) error {
	if em.userDataDir == "" {
		return fmt.Errorf("user data directory not set")
	}

	// Chrome stores extension state in the Preferences file
	preferencesPath := filepath.Join(em.userDataDir, "Default", "Preferences")
	if _, err := os.Stat(preferencesPath); os.IsNotExist(err) {
		return fmt.Errorf("preferences file not found")
	}

	// Read the preferences file
	preferencesData, err := os.ReadFile(preferencesPath)
	if err != nil {
		return fmt.Errorf("failed to read preferences file: %w", err)
	}

	// Parse the preferences
	var preferences map[string]interface{}
	err = json.Unmarshal(preferencesData, &preferences)
	if err != nil {
		return fmt.Errorf("failed to parse preferences file: %w", err)
	}

	// Get the extensions settings
	extensions, ok := preferences["extensions"].(map[string]interface{})
	if !ok {
		extensions = make(map[string]interface{})
		preferences["extensions"] = extensions
	}

	settings, ok := extensions["settings"].(map[string]interface{})
	if !ok {
		settings = make(map[string]interface{})
		extensions["settings"] = settings
	}

	// Update the extension state
	extensionSettings, ok := settings[extensionID].(map[string]interface{})
	if !ok {
		extensionSettings = make(map[string]interface{})
		settings[extensionID] = extensionSettings
	}

	// State 1 = enabled, 0 = disabled
	state := 0
	if enabled {
		state = 1
	}
	extensionSettings["state"] = state

	// Write the preferences back to the file
	updatedPreferencesData, err := json.MarshalIndent(preferences, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal preferences: %w", err)
	}

	err = os.WriteFile(preferencesPath, updatedPreferencesData, 0644)
	if err != nil {
		return fmt.Errorf("failed to write preferences file: %w", err)
	}

	return nil
}

// RemoveExtension removes an extension
func (em *ExtensionManager) RemoveExtension(extensionID string) error {
	if em.userDataDir == "" {
		return fmt.Errorf("user data directory not set")
	}

	// Extensions are stored in the "Extensions" directory in the user data directory
	extensionDir := filepath.Join(em.userDataDir, "Extensions", extensionID)
	if _, err := os.Stat(extensionDir); os.IsNotExist(err) {
		return fmt.Errorf("extension not found: %s", extensionID)
	}

	// Remove the extension directory
	err := os.RemoveAll(extensionDir)
	if err != nil {
		return fmt.Errorf("failed to remove extension directory: %w", err)
	}

	// Update the preferences file to remove the extension settings
	preferencesPath := filepath.Join(em.userDataDir, "Default", "Preferences")
	if _, err := os.Stat(preferencesPath); os.IsNotExist(err) {
		// Preferences file doesn't exist, nothing to update
		return nil
	}

	// Read the preferences file
	preferencesData, err := os.ReadFile(preferencesPath)
	if err != nil {
		return fmt.Errorf("failed to read preferences file: %w", err)
	}

	// Parse the preferences
	var preferences map[string]interface{}
	err = json.Unmarshal(preferencesData, &preferences)
	if err != nil {
		return fmt.Errorf("failed to parse preferences file: %w", err)
	}

	// Get the extensions settings
	extensions, ok := preferences["extensions"].(map[string]interface{})
	if !ok {
		// No extensions settings, nothing to update
		return nil
	}

	settings, ok := extensions["settings"].(map[string]interface{})
	if !ok {
		// No settings, nothing to update
		return nil
	}

	// Remove the extension settings
	delete(settings, extensionID)

	// Write the preferences back to the file
	updatedPreferencesData, err := json.MarshalIndent(preferences, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal preferences: %w", err)
	}

	err = os.WriteFile(preferencesPath, updatedPreferencesData, 0644)
	if err != nil {
		return fmt.Errorf("failed to write preferences file: %w", err)
	}

	return nil
}

// InstallExtension installs an extension from a local file
func (em *ExtensionManager) InstallExtension(extensionPath string) error {
	if em.userDataDir == "" {
		return fmt.Errorf("user data directory not set")
	}

	// Check if the file exists
	if _, err := os.Stat(extensionPath); os.IsNotExist(err) {
		return fmt.Errorf("extension file does not exist: %s", extensionPath)
	}

	// Extract the extension if it's a CRX or ZIP file
	extractedDir, err := em.extractExtension(extensionPath)
	if err != nil {
		return fmt.Errorf("failed to extract extension: %w", err)
	}

	// Read the manifest.json file to get the extension ID
	manifestPath := filepath.Join(extractedDir, "manifest.json")
	manifestData, err := os.ReadFile(manifestPath)
	if err != nil {
		return fmt.Errorf("failed to read manifest: %w", err)
	}

	var manifest struct {
		Name string `json:"name"`
		Key  string `json:"key"`
	}

	err = json.Unmarshal(manifestData, &manifest)
	if err != nil {
		return fmt.Errorf("failed to parse manifest: %w", err)
	}

	// Generate the extension ID from the key if available
	// In a real implementation, we would compute the extension ID from the key
	// For simplicity, we'll use a placeholder ID
	extensionID := "placeholder_id"
	if manifest.Key != "" {
		// In a real implementation, we would compute the ID from the key
		extensionID = manifest.Key[:32]
	}

	// Create the extension directory
	extensionsDir := filepath.Join(em.userDataDir, "Extensions")
	err = os.MkdirAll(extensionsDir, 0755)
	if err != nil {
		return fmt.Errorf("failed to create extensions directory: %w", err)
	}

	extensionDir := filepath.Join(extensionsDir, extensionID)
	err = os.MkdirAll(extensionDir, 0755)
	if err != nil {
		return fmt.Errorf("failed to create extension directory: %w", err)
	}

	// Create a version directory (use 1.0 as a placeholder)
	versionDir := filepath.Join(extensionDir, "1.0")
	err = os.MkdirAll(versionDir, 0755)
	if err != nil {
		return fmt.Errorf("failed to create version directory: %w", err)
	}

	// Copy the extension files to the version directory
	err = copyDir(extractedDir, versionDir)
	if err != nil {
		return fmt.Errorf("failed to copy extension files: %w", err)
	}

	// Update the preferences file to enable the extension
	err = em.setExtensionState(extensionID, true)
	if err != nil {
		em.logger.Printf("Failed to enable extension: %v", err)
	}

	return nil
}

// extractExtension extracts a CRX or ZIP file to a temporary directory
func (em *ExtensionManager) extractExtension(extensionPath string) (string, error) {
	// Create a temporary directory
	tempDir, err := os.MkdirTemp("", "rod-extension-*")
	if err != nil {
		return "", fmt.Errorf("failed to create temp directory: %w", err)
	}

	// Check the file extension
	ext := strings.ToLower(filepath.Ext(extensionPath))
	switch ext {
	case ".crx":
		// Extract CRX file
		// In a real implementation, we would handle the CRX format properly
		// For simplicity, we'll just assume it's a ZIP file with a different header
		return tempDir, fmt.Errorf("CRX extraction not implemented")
	case ".zip":
		// Extract ZIP file
		// In a real implementation, we would use the archive/zip package
		// For simplicity, we'll just return the temp directory
		return tempDir, fmt.Errorf("ZIP extraction not implemented")
	default:
		// Assume it's an unpacked extension directory
		// Copy the directory to the temp directory
		err = copyDir(extensionPath, tempDir)
		if err != nil {
			return "", fmt.Errorf("failed to copy extension directory: %w", err)
		}
		return tempDir, nil
	}
}

// copyDir copies a directory recursively
func copyDir(src, dst string) error {
	// Get file info
	info, err := os.Stat(src)
	if err != nil {
		return err
	}

	// Check if it's a directory
	if !info.IsDir() {
		// It's a file, copy it
		return copyFile(src, dst)
	}

	// Create the destination directory
	err = os.MkdirAll(dst, info.Mode())
	if err != nil {
		return err
	}

	// Read the source directory
	entries, err := os.ReadDir(src)
	if err != nil {
		return err
	}

	// Copy each entry
	for _, entry := range entries {
		srcPath := filepath.Join(src, entry.Name())
		dstPath := filepath.Join(dst, entry.Name())

		if entry.IsDir() {
			// Recursively copy the directory
			err = copyDir(srcPath, dstPath)
			if err != nil {
				return err
			}
		} else {
			// Copy the file
			err = copyFile(srcPath, dstPath)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

// copyFile copies a file
func copyFile(src, dst string) error {
	// Open the source file
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	// Get file info
	info, err := srcFile.Stat()
	if err != nil {
		return err
	}

	// Create the destination file
	dstFile, err := os.OpenFile(dst, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, info.Mode())
	if err != nil {
		return err
	}
	defer dstFile.Close()

	// Copy the file
	_, err = io.Copy(dstFile, srcFile)
	return err
}
