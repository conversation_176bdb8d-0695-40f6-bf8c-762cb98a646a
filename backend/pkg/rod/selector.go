package rod

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-rod/rod"
	"github.com/go-rod/rod/lib/proto"
)

// ElementSelector handles different types of selectors
type ElementSelector struct {
	page *rod.Page
}

// NewElementSelector creates a new element selector
func NewElementSelector(page *rod.Page) *ElementSelector {
	return &ElementSelector{
		page: page,
	}
}

// FindElement finds an element using the specified selector type
func (es *ElementSelector) FindElement(selector string, selectorType SelectorType, timeout time.Duration) (*rod.Element, error) {
	switch selectorType {
	case SelectorQuery:
		return es.findByCSS(selector, timeout)
	case SelectorID:
		return es.findByID(selector, timeout)
	case SelectorXPath:
		return es.findByXPath(selector, timeout)
	case SelectorText:
		return es.findByText(selector, timeout)
	case SelectorJSPath:
		return es.findByJSPath(selector, timeout)
	default:
		return es.findByCSS(selector, timeout) // Default to CSS selector
	}
}

// FindElements finds multiple elements using the specified selector type
func (es *ElementSelector) FindElements(selector string, selectorType SelectorType, timeout time.Duration) (rod.Elements, error) {
	switch selectorType {
	case SelectorQueryAll:
		return es.findAllByCSS(selector, timeout)
	case SelectorXPath:
		return es.findAllByXPath(selector, timeout)
	case SelectorText:
		return es.findAllByText(selector, timeout)
	default:
		return es.findAllByCSS(selector, timeout) // Default to CSS selector
	}
}

// findByCSS finds an element by CSS selector
func (es *ElementSelector) findByCSS(selector string, timeout time.Duration) (*rod.Element, error) {
	return es.page.Timeout(timeout).Element(selector)
}

// findAllByCSS finds all elements matching a CSS selector
func (es *ElementSelector) findAllByCSS(selector string, timeout time.Duration) (rod.Elements, error) {
	return es.page.Timeout(timeout).Elements(selector)
}

// findByID finds an element by ID
func (es *ElementSelector) findByID(id string, timeout time.Duration) (*rod.Element, error) {
	// Remove the # prefix if present
	id = strings.TrimPrefix(id, "#")
	return es.page.Timeout(timeout).Element("#" + id)
}

// findByXPath finds an element by XPath
func (es *ElementSelector) findByXPath(xpath string, timeout time.Duration) (*rod.Element, error) {
	return es.page.Timeout(timeout).ElementX(xpath)
}

// findAllByXPath finds all elements matching an XPath
func (es *ElementSelector) findAllByXPath(xpath string, timeout time.Duration) (rod.Elements, error) {
	return es.page.Timeout(timeout).ElementsX(xpath)
}

// findByText finds an element containing the specified text
func (es *ElementSelector) findByText(text string, timeout time.Duration) (*rod.Element, error) {
	// Use XPath to find elements containing the text
	xpath := fmt.Sprintf("//*[contains(text(), '%s')]", text)
	return es.page.Timeout(timeout).ElementX(xpath)
}

// findAllByText finds all elements containing the specified text
func (es *ElementSelector) findAllByText(text string, timeout time.Duration) (rod.Elements, error) {
	// Use XPath to find elements containing the text
	xpath := fmt.Sprintf("//*[contains(text(), '%s')]", text)
	return es.page.Timeout(timeout).ElementsX(xpath)
}

// findByJSPath finds an element using a JavaScript path
func (es *ElementSelector) findByJSPath(jsPath string, timeout time.Duration) (*rod.Element, error) {
	// Execute JavaScript to find the element
	result, err := es.page.Timeout(timeout).Eval(jsPath)
	if err != nil {
		return nil, fmt.Errorf("failed to evaluate JavaScript path: %w", err)
	}

	// Check if the result is an element
	if result.Type != proto.RuntimeRemoteObjectTypeObject || result.Subtype != proto.RuntimeRemoteObjectSubtypeNode {
		return nil, fmt.Errorf("JavaScript path did not return an element")
	}

	// Get the element from the result
	return es.page.ElementFromObject(result)
}

// WaitForElement waits for an element to appear
func (es *ElementSelector) WaitForElement(selector string, selectorType SelectorType, timeout time.Duration) (*rod.Element, error) {
	// Create a context with timeout
	ctx := es.page.GetContext()
	var cancel context.CancelFunc
	if timeout > 0 {
		ctx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	}

	// Create a cache for the selector to avoid repeated string operations
	var cachedSelector string
	if selectorType == SelectorID {
		id := strings.TrimPrefix(selector, "#")
		cachedSelector = "#" + id
	} else if selectorType == SelectorText {
		cachedSelector = fmt.Sprintf("//*[contains(text(), '%s')]", selector)
	} else {
		cachedSelector = selector
	}

	// Use exponential backoff for polling
	backoff := 50 * time.Millisecond
	maxBackoff := 1 * time.Second
	backoffFactor := 1.5

	// Poll until the element is found or timeout
	var element *rod.Element
	var err error

	startTime := time.Now()
	for {
		select {
		case <-ctx.Done():
			return nil, fmt.Errorf("timeout waiting for element to appear: %w", ctx.Err())
		default:
			// Try to find the element based on selector type
			switch selectorType {
			case SelectorQuery:
				element, err = es.page.Element(cachedSelector)
			case SelectorID:
				element, err = es.page.Element(cachedSelector)
			case SelectorXPath:
				element, err = es.page.ElementX(cachedSelector)
			case SelectorText:
				element, err = es.page.ElementX(cachedSelector)
			case SelectorJSPath:
				// For JS path, we need to evaluate the JavaScript
				result, evalErr := es.page.Eval(selector)
				if evalErr != nil {
					err = evalErr
					break
				}

				// Check if the result is an element
				if result.Type != proto.RuntimeRemoteObjectTypeObject || result.Subtype != proto.RuntimeRemoteObjectSubtypeNode {
					err = fmt.Errorf("JavaScript path did not return an element")
					break
				}

				// Get the element from the result
				element, err = es.page.ElementFromObject(result)
			default:
				element, err = es.page.Element(cachedSelector)
			}

			// If element found, return it
			if err == nil && element != nil {
				return element, nil
			}

			// If we've been trying for too long, give up
			if time.Since(startTime) > timeout {
				if err != nil {
					return nil, fmt.Errorf("failed to find element: %w", err)
				}
				return nil, fmt.Errorf("timeout waiting for element to appear")
			}

			// Wait with exponential backoff before trying again
			time.Sleep(backoff)
			backoff = time.Duration(float64(backoff) * backoffFactor)
			if backoff > maxBackoff {
				backoff = maxBackoff
			}
		}
	}
}

// WaitForElementVisible waits for an element to become visible
func (es *ElementSelector) WaitForElementVisible(selector string, selectorType SelectorType, timeout time.Duration) (*rod.Element, error) {
	el, err := es.FindElement(selector, selectorType, timeout)
	if err != nil {
		return nil, fmt.Errorf("failed to find element: %w", err)
	}

	err = el.WaitVisible()
	if err != nil {
		return nil, fmt.Errorf("element did not become visible: %w", err)
	}

	return el, nil
}

// WaitForElementNotVisible waits for an element to become invisible
func (es *ElementSelector) WaitForElementNotVisible(selector string, selectorType SelectorType, timeout time.Duration) error {
	el, err := es.FindElement(selector, selectorType, timeout)
	if err != nil {
		// If element not found, it's not visible
		return nil
	}

	err = el.WaitInvisible()
	if err != nil {
		return fmt.Errorf("element did not become invisible: %w", err)
	}

	return nil
}

// WaitForElementPresent waits for an element to be present in the DOM
func (es *ElementSelector) WaitForElementPresent(selector string, selectorType SelectorType, timeout time.Duration) (*rod.Element, error) {
	return es.FindElement(selector, selectorType, timeout)
}

// WaitForElementNotPresent waits for an element to not be present in the DOM
func (es *ElementSelector) WaitForElementNotPresent(selector string, selectorType SelectorType, timeout time.Duration) error {
	// Create a context with timeout
	ctx := es.page.GetContext()
	var cancel context.CancelFunc
	if timeout > 0 {
		ctx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	}

	// Poll until the element is not present or timeout
	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("timeout waiting for element to not be present")
		default:
			// Check if the element exists
			el, err := es.FindElement(selector, selectorType, 100*time.Millisecond)
			if err != nil || el == nil {
				// Element not found, which is what we want
				return nil
			}
			// Element still exists, wait a bit and try again
			time.Sleep(100 * time.Millisecond)
		}
	}
}

// WaitForElementEnabled waits for an element to be enabled
func (es *ElementSelector) WaitForElementEnabled(selector string, selectorType SelectorType, timeout time.Duration) (*rod.Element, error) {
	el, err := es.FindElement(selector, selectorType, timeout)
	if err != nil {
		return nil, fmt.Errorf("failed to find element: %w", err)
	}

	// Create a context with timeout
	ctx := es.page.GetContext()
	var cancel context.CancelFunc
	if timeout > 0 {
		ctx, cancel = context.WithTimeout(ctx, timeout)
		defer cancel()
	}

	// Poll until the element is enabled or timeout
	for {
		select {
		case <-ctx.Done():
			return nil, fmt.Errorf("timeout waiting for element to be enabled")
		default:
			// Check if the element is disabled
			disabled, err := el.Property("disabled")
			if err != nil {
				return nil, fmt.Errorf("failed to check if element is disabled: %w", err)
			}

			// If the element is not disabled, it's enabled
			if !disabled.Bool() {
				return el, nil
			}

			// Element is still disabled, wait a bit and try again
			time.Sleep(100 * time.Millisecond)
		}
	}
}
