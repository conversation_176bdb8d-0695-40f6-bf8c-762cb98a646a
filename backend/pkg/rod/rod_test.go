package rod

import (
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestNewBrowserAutomation tests the creation of a new browser automation instance
func TestNewBrowserAutomation(t *testing.T) {
	// Create a new browser automation instance
	ba := NewBrowserAutomation()
	assert.NotNil(t, ba, "Browser automation instance should not be nil")
	assert.NotNil(t, ba.parser, "Parser should not be nil")
	assert.NotNil(t, ba.executor, "Executor should not be nil")
	assert.NotNil(t, ba.logger, "Logger should not be nil")

	// Test with user data directory
	tempDir, err := os.MkdirTemp("", "rod-test-*")
	require.NoError(t, err, "Failed to create temp directory")
	defer os.RemoveAll(tempDir)

	baWithUserDir := NewBrowserAutomation(tempDir)
	assert.NotNil(t, baWithUserDir, "Browser automation instance with user dir should not be nil")
	assert.Equal(t, tempDir, baWithUserDir.executor.settings.UserDataDir, "User data directory should be set")
}

// TestCreateNewPlan tests the creation of a new task plan
func TestCreateNewPlan(t *testing.T) {
	ba := NewBrowserAutomation()
	plan := ba.CreateNewPlan("Test Plan", "A test plan")

	assert.NotNil(t, plan, "Task plan should not be nil")
	assert.Equal(t, "Test Plan", plan.Name, "Plan name should be set")
	assert.Equal(t, "A test plan", plan.Description, "Plan description should be set")
	assert.NotNil(t, plan.Tasks, "Tasks slice should be initialized")
	assert.NotNil(t, plan.Variables, "Variables map should be initialized")
	assert.NotNil(t, plan.Settings, "Settings should be initialized")
}

// TestTaskAddition tests adding various tasks to a plan
func TestTaskAddition(t *testing.T) {
	ba := NewBrowserAutomation()
	plan := ba.CreateNewPlan("Test Plan", "A test plan")

	// Test adding navigate task
	ba.AddNavigateTask(plan, "https://example.com", "Navigate to example")
	assert.Len(t, plan.Tasks, 1, "Plan should have 1 task")
	assert.Equal(t, TaskNavigate, plan.Tasks[0].Type, "Task type should be navigate")
	assert.Equal(t, "https://example.com", plan.Tasks[0].URL, "URL should be set")

	// Test adding click task
	ba.AddClickTask(plan, "#button", SelectorQuery, "Click button")
	assert.Len(t, plan.Tasks, 2, "Plan should have 2 tasks")
	assert.Equal(t, TaskClick, plan.Tasks[1].Type, "Task type should be click")
	assert.Equal(t, "#button", plan.Tasks[1].Selector, "Selector should be set")
	assert.Equal(t, SelectorQuery, plan.Tasks[1].SelectorType, "Selector type should be set")

	// Test adding type task
	ba.AddTypeTask(plan, "input[name='q']", SelectorQuery, "search term", "Type search")
	assert.Len(t, plan.Tasks, 3, "Plan should have 3 tasks")
	assert.Equal(t, TaskTypeText, plan.Tasks[2].Type, "Task type should be type")
	assert.Equal(t, "search term", plan.Tasks[2].Value, "Value should be set")

	// Test adding wait task
	ba.AddWaitVisibleTask(plan, ".result", SelectorQuery, "5s", "Wait for results")
	assert.Len(t, plan.Tasks, 4, "Plan should have 4 tasks")
	assert.Equal(t, TaskWaitVisible, plan.Tasks[3].Type, "Task type should be wait_visible")
	assert.Equal(t, "5s", plan.Tasks[3].Timeout, "Timeout should be set")

	// Test adding screenshot task
	ba.AddScreenshotTask(plan, "body", SelectorQuery, "screenshot.png", "Take screenshot")
	assert.Len(t, plan.Tasks, 5, "Plan should have 5 tasks")
	assert.Equal(t, TaskScreenshot, plan.Tasks[4].Type, "Task type should be screenshot")
	assert.Equal(t, "screenshot.png", plan.Tasks[4].FileName, "Filename should be set")
}

// TestParseTimeout tests the ParseTimeout function
func TestParseTimeout(t *testing.T) {
	// Test valid durations
	duration, err := ParseTimeout("5s")
	assert.NoError(t, err, "Should parse 5s without error")
	assert.Equal(t, 5*time.Second, duration, "Duration should be 5 seconds")

	duration, err = ParseTimeout("1m")
	assert.NoError(t, err, "Should parse 1m without error")
	assert.Equal(t, 1*time.Minute, duration, "Duration should be 1 minute")

	duration, err = ParseTimeout("500ms")
	assert.NoError(t, err, "Should parse 500ms without error")
	assert.Equal(t, 500*time.Millisecond, duration, "Duration should be 500 milliseconds")

	// Test invalid duration
	_, err = ParseTimeout("invalid")
	assert.Error(t, err, "Should return error for invalid duration")

	// Test empty duration (should use default)
	duration, err = ParseTimeout("")
	assert.NoError(t, err, "Should not return error for empty duration")
	assert.Equal(t, 30*time.Second, duration, "Duration should be default (30 seconds)")
}

// TestRandomUserAgent tests the RandomUserAgent function
func TestRandomUserAgent(t *testing.T) {
	ua := RandomUserAgent()
	assert.NotEmpty(t, ua, "User agent should not be empty")
}

// TestYAMLParsing tests parsing YAML task plans
func TestYAMLParsing(t *testing.T) {
	yamlContent := `
name: Test YAML Plan
description: A test plan defined in YAML
settings:
  headless: true
  viewport_width: 1366
  viewport_height: 768
  anti_detection: true
tasks:
  - type: navigate
    url: https://example.com
    description: Navigate to example
  - type: wait_visible
    selector: h1
    selector_type: query
    timeout: 5s
    description: Wait for heading
`

	ba := NewBrowserAutomation()
	plan, err := ba.parser.ParseBytes([]byte(yamlContent))

	assert.NoError(t, err, "Should parse YAML without error")
	assert.NotNil(t, plan, "Plan should not be nil")
	assert.Equal(t, "Test YAML Plan", plan.Name, "Plan name should be parsed correctly")
	assert.Equal(t, "A test plan defined in YAML", plan.Description, "Plan description should be parsed correctly")
	assert.True(t, plan.Settings.Headless, "Headless setting should be parsed correctly")
	assert.Equal(t, 1366, plan.Settings.ViewportWidth, "Viewport width should be parsed correctly")
	assert.Equal(t, 768, plan.Settings.ViewportHeight, "Viewport height should be parsed correctly")
	assert.True(t, plan.Settings.AntiDetection, "Anti-detection setting should be parsed correctly")
	assert.Len(t, plan.Tasks, 2, "Plan should have 2 tasks")
	assert.Equal(t, TaskNavigate, plan.Tasks[0].Type, "First task type should be navigate")
	assert.Equal(t, "https://example.com", plan.Tasks[0].URL, "First task URL should be parsed correctly")
	assert.Equal(t, TaskWaitVisible, plan.Tasks[1].Type, "Second task type should be wait_visible")
	assert.Equal(t, "h1", plan.Tasks[1].Selector, "Second task selector should be parsed correctly")
}
