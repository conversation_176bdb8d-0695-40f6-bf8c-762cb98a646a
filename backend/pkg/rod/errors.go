package rod

import (
	"errors"
	"fmt"
	"time"
)

// Error codes for standardized error handling
const (
	// Browser related errors
	ErrCodeBrowserInit    = "BROWSER_INIT_FAILED"
	ErrCodeBrowserClosed  = "BROWSER_CLOSED"
	ErrCodePageCreate     = "PAGE_CREATE_FAILED"
	ErrCodePageNavigation = "PAGE_NAVIGATION_FAILED"

	// Element related errors
	ErrCodeElementNotFound    = "ELEMENT_NOT_FOUND"
	ErrCodeElementNotVisible  = "ELEMENT_NOT_VISIBLE"
	ErrCodeElementNotEnabled  = "ELEMENT_NOT_ENABLED"
	ErrCodeElementInteraction = "ELEMENT_INTERACTION_FAILED"
	ErrCodeSelectorInvalid    = "SELECTOR_INVALID"

	// Task related errors
	ErrCodeTaskExecution  = "TASK_EXECUTION_FAILED"
	ErrCodeTaskTimeout    = "TASK_TIMEOUT"
	ErrCodeTaskValidation = "TASK_VALIDATION_FAILED"
	ErrCodeTaskParsing    = "TASK_PARSING_FAILED"

	// Challenge related errors
	ErrCodeChallengeDetection = "CHALLENGE_DETECTION_FAILED"
	ErrCodeChallengeHandling  = "CHALLENGE_HANDLING_FAILED"
	ErrCodeCaptchaSolving     = "CAPTCHA_SOLVING_FAILED"

	// Extension related errors
	ErrCodeExtensionInstall = "EXTENSION_INSTALL_FAILED"
	ErrCodeExtensionManage  = "EXTENSION_MANAGE_FAILED"

	// Configuration related errors
	ErrCodeConfigInvalid    = "CONFIG_INVALID"
	ErrCodeConfigParsing    = "CONFIG_PARSING_FAILED"
	ErrCodeConfigValidation = "CONFIG_VALIDATION_FAILED"

	// Resource related errors
	ErrCodeResourceExhausted = "RESOURCE_EXHAUSTED"
	ErrCodeResourceLeak      = "RESOURCE_LEAK"
	ErrCodeResourceCleanup   = "RESOURCE_CLEANUP_FAILED"

	// Network related errors
	ErrCodeNetworkTimeout = "NETWORK_TIMEOUT"
	ErrCodeNetworkError   = "NETWORK_ERROR"
	ErrCodeProxyError     = "PROXY_ERROR"
)

// BrowserAutomationError represents a standardized error in the browser automation system
type BrowserAutomationError struct {
	Code      string                 `json:"code"`
	Message   string                 `json:"message"`
	Cause     error                  `json:"-"`
	Context   map[string]interface{} `json:"context,omitempty"`
	Timestamp int64                  `json:"timestamp"`
}

// Error implements the error interface
func (e *BrowserAutomationError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("[%s] %s: %v", e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// Unwrap returns the underlying error
func (e *BrowserAutomationError) Unwrap() error {
	return e.Cause
}

// Is checks if the error matches a target error
func (e *BrowserAutomationError) Is(target error) bool {
	var targetErr *BrowserAutomationError
	if errors.As(target, &targetErr) {
		return e.Code == targetErr.Code
	}
	return false
}

// WithContext adds context information to the error
func (e *BrowserAutomationError) WithContext(key string, value interface{}) *BrowserAutomationError {
	if e.Context == nil {
		e.Context = make(map[string]interface{})
	}
	e.Context[key] = value
	return e
}

// NewError creates a new BrowserAutomationError
func NewError(code, message string, cause error) *BrowserAutomationError {
	return &BrowserAutomationError{
		Code:      code,
		Message:   message,
		Cause:     cause,
		Timestamp: getCurrentTimestamp(),
	}
}

// NewErrorWithContext creates a new error with context
func NewErrorWithContext(code, message string, cause error, context map[string]interface{}) *BrowserAutomationError {
	return &BrowserAutomationError{
		Code:      code,
		Message:   message,
		Cause:     cause,
		Context:   context,
		Timestamp: getCurrentTimestamp(),
	}
}

// Predefined error constructors for common cases

// NewBrowserInitError creates a browser initialization error
func NewBrowserInitError(cause error) *BrowserAutomationError {
	return NewError(ErrCodeBrowserInit, "Failed to initialize browser", cause)
}

// NewElementNotFoundError creates an element not found error
func NewElementNotFoundError(selector string, selectorType SelectorType, cause error) *BrowserAutomationError {
	return NewErrorWithContext(
		ErrCodeElementNotFound,
		"Element not found",
		cause,
		map[string]interface{}{
			"selector":      selector,
			"selector_type": string(selectorType),
		},
	)
}

// NewTaskExecutionError creates a task execution error
func NewTaskExecutionError(taskType TaskType, description string, cause error) *BrowserAutomationError {
	return NewErrorWithContext(
		ErrCodeTaskExecution,
		"Task execution failed",
		cause,
		map[string]interface{}{
			"task_type":   string(taskType),
			"description": description,
		},
	)
}

// NewChallengeHandlingError creates a challenge handling error
func NewChallengeHandlingError(challengeType ChallengeType, cause error) *BrowserAutomationError {
	return NewErrorWithContext(
		ErrCodeChallengeHandling,
		"Challenge handling failed",
		cause,
		map[string]interface{}{
			"challenge_type": string(challengeType),
		},
	)
}

// NewConfigValidationError creates a configuration validation error
func NewConfigValidationError(message string, errors []string) *BrowserAutomationError {
	return NewErrorWithContext(
		ErrCodeConfigValidation,
		message,
		nil,
		map[string]interface{}{
			"validation_errors": errors,
		},
	)
}

// Error checking helpers

// IsBrowserError checks if the error is browser-related
func IsBrowserError(err error) bool {
	var baErr *BrowserAutomationError
	if errors.As(err, &baErr) {
		return baErr.Code == ErrCodeBrowserInit ||
			baErr.Code == ErrCodeBrowserClosed ||
			baErr.Code == ErrCodePageCreate ||
			baErr.Code == ErrCodePageNavigation
	}
	return false
}

// IsElementError checks if the error is element-related
func IsElementError(err error) bool {
	var baErr *BrowserAutomationError
	if errors.As(err, &baErr) {
		return baErr.Code == ErrCodeElementNotFound ||
			baErr.Code == ErrCodeElementNotVisible ||
			baErr.Code == ErrCodeElementNotEnabled ||
			baErr.Code == ErrCodeElementInteraction ||
			baErr.Code == ErrCodeSelectorInvalid
	}
	return false
}

// IsTimeoutError checks if the error is timeout-related
func IsTimeoutError(err error) bool {
	var baErr *BrowserAutomationError
	if errors.As(err, &baErr) {
		return baErr.Code == ErrCodeTaskTimeout || baErr.Code == ErrCodeNetworkTimeout
	}
	return false
}

// IsRetryableError checks if the error is retryable
func IsRetryableError(err error) bool {
	var baErr *BrowserAutomationError
	if errors.As(err, &baErr) {
		retryableCodes := []string{
			ErrCodeNetworkTimeout,
			ErrCodeNetworkError,
			ErrCodeElementNotVisible,
			ErrCodeChallengeDetection,
		}
		for _, code := range retryableCodes {
			if baErr.Code == code {
				return true
			}
		}
	}
	return false
}

// Error aggregation for multiple errors

// ErrorGroup represents a collection of errors
type ErrorGroup struct {
	Errors []error
}

// NewErrorGroup creates a new error group
func NewErrorGroup() *ErrorGroup {
	return &ErrorGroup{
		Errors: make([]error, 0),
	}
}

// Add adds an error to the group
func (eg *ErrorGroup) Add(err error) {
	if err != nil {
		eg.Errors = append(eg.Errors, err)
	}
}

// HasErrors returns true if there are any errors
func (eg *ErrorGroup) HasErrors() bool {
	return len(eg.Errors) > 0
}

// Error implements the error interface
func (eg *ErrorGroup) Error() string {
	if len(eg.Errors) == 0 {
		return "no errors"
	}
	if len(eg.Errors) == 1 {
		return eg.Errors[0].Error()
	}

	message := fmt.Sprintf("multiple errors (%d):", len(eg.Errors))
	for i, err := range eg.Errors {
		message += fmt.Sprintf("\n  %d. %s", i+1, err.Error())
	}
	return message
}

// FirstError returns the first error in the group
func (eg *ErrorGroup) FirstError() error {
	if len(eg.Errors) == 0 {
		return nil
	}
	return eg.Errors[0]
}

// getCurrentTimestamp returns the current timestamp in Unix milliseconds
func getCurrentTimestamp() int64 {
	return getCurrentTime().UnixNano() / 1000000
}

// getCurrentTime returns the current time (can be mocked in tests)
var getCurrentTime = func() *time.Time {
	t := time.Now()
	return &t
}
