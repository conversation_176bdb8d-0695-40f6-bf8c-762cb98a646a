# Rod-based Browser Automation Module

This module provides a high-level API for browser automation using the [go-rod/rod](https://github.com/go-rod/rod) library. It is designed to be a drop-in replacement for the chromedp-based browser automation module with advanced error handling and resource management capabilities.

## ✨ Features

- **YAML-based task description language** - Declarative automation scripts
- **Standardized error handling** - Comprehensive error classification and context
- **Advanced resource management** - Efficient browser and page lifecycle management
- **Anti-detection capabilities** - Stealth mode for undetected automation
- **Challenge handling** - Support for Cloudflare, reCAPTCHA, hCaptcha, etc.
- **Resource pooling** - Browser instance reuse and automatic cleanup
- **User data directory support** - Persistent browser profiles
- **Browser extension support** - Dynamic extension installation
- **Screenshot capture** - Element and full-page screenshots
- **Element interaction** - Comprehensive UI interaction capabilities
- **JavaScript evaluation** - Execute custom JavaScript code
- **Flow control** - Conditional execution, loops, and iterations
- **Variable substitution** - Dynamic value insertion

## 🚀 New Features (v2.0)

### Standardized Error Handling

- **Structured Error Types**: Categorized error codes for browser, element, task, and network issues
- **Error Context**: Rich context information including selectors, timeouts, and stack traces
- **Error Classification**: Automatic detection of retryable vs non-retryable errors
- **Error Aggregation**: Multiple error collection and batch processing
- **Detailed Debugging**: Comprehensive error information for troubleshooting

### Advanced Resource Management

- **Browser Pooling**: Efficient reuse of browser instances
- **Automatic Cleanup**: Memory leak prevention with idle resource cleanup
- **Resource Limits**: Configurable limits for browsers and pages per browser
- **Context Control**: Precise timeout and cancellation management
- **Graceful Shutdown**: Clean resource release on system shutdown

## 📦 Installation

```bash
go get github.com/A8Tools/desktop/backend/pkg/rod
```

## 🔧 Quick Start

### Basic Example with Error Handling

```go
package main

import (
	"fmt"
	"log"

	"github.com/A8Tools/desktop/backend/pkg/rod"
)

func main() {
	// Create a new browser automation instance
	ba := rod.NewBrowserAutomation()
	defer ba.Shutdown() // Ensure graceful cleanup

	// Create a new task plan
	plan := ba.CreateNewPlan("Example Plan", "A simple example with error handling")

	// Configure settings for optimal resource usage
	plan.Settings.Headless = true
	plan.Settings.ViewportWidth = 1366
	plan.Settings.ViewportHeight = 768
	plan.Settings.AntiDetection = true
	plan.Settings.KeepOpen = false // Auto cleanup

	// Add tasks with improved error handling
	if err := ba.AddNavigateTask(plan, "https://www.google.com", "Navigate to Google"); err != nil {
		log.Fatalf("Failed to add navigate task: %v", err)
	}

	ba.AddWaitVisibleTask(plan, "input[name='q']", rod.SelectorQuery, "5s", "Wait for search box")
	ba.AddTypeTask(plan, "input[name='q']", rod.SelectorQuery, "go-rod browser automation", "Type search query")

	if err := ba.AddClickTask(plan, "input[name='btnK']", rod.SelectorQuery, "Click search button"); err != nil {
		log.Fatalf("Failed to add click task: %v", err)
	}

	ba.AddWaitVisibleTask(plan, "#search", rod.SelectorQuery, "5s", "Wait for search results")
	ba.AddScreenshotTask(plan, "#search", rod.SelectorQuery, "search_results.png", "Take screenshot")

	// Execute the plan with comprehensive error handling
	err := ba.ExecutePlan(plan)
	if err != nil {
		handleAutomationError(err)
		return
	}

	fmt.Println("Plan executed successfully!")

	// Get execution results
	results := ba.GetTaskResults()
	variables := ba.GetVariables()
	fmt.Printf("Results: %d, Variables: %d\n", len(results), len(variables))
}

func handleAutomationError(err error) {
	fmt.Printf("Automation failed: %s\n", err.Error())

	// Check error type for specific handling
	if rod.IsBrowserError(err) {
		fmt.Println("Browser-related error - may need to restart browser")
	} else if rod.IsElementError(err) {
		fmt.Println("Element-related error - check selectors")
	} else if rod.IsTimeoutError(err) {
		fmt.Println("Timeout error - consider increasing timeouts")
	}

	// Check if error is retryable
	if rod.IsRetryableError(err) {
		fmt.Println("This error can be retried")
	}

	// Get detailed error information
	if baErr, ok := err.(*rod.BrowserAutomationError); ok {
		fmt.Printf("Error Code: %s\n", baErr.Code)
		if len(baErr.Context) > 0 {
			fmt.Println("Context:")
			for key, value := range baErr.Context {
				fmt.Printf("  %s: %v\n", key, value)
			}
		}
	}
}
```

### Production Example with Retry Logic

```go
package main

import (
	"log"
	"time"

	"github.com/A8Tools/desktop/backend/pkg/rod"
)

func main() {
	ba := rod.NewBrowserAutomation()
	defer ba.Shutdown()

	plan := ba.CreateNewPlan("Production Plan", "Production-ready automation")
	plan.Settings.Headless = true
	plan.Settings.Timeout = "30s"
	plan.Settings.KeepOpen = false

	// Build plan
	if err := ba.AddNavigateTask(plan, "https://example.com", "Navigate to target"); err != nil {
		log.Fatalf("Failed to build plan: %v", err)
	}

	ba.AddWaitVisibleTask(plan, "body", rod.SelectorQuery, "10s", "Wait for page load")
	ba.AddGetTextTask(plan, "title", rod.SelectorQuery, "page_title", "Extract title")

	// Execute with retry logic
	const maxRetries = 3
	for attempt := 1; attempt <= maxRetries; attempt++ {
		err := ba.ExecutePlan(plan)
		if err == nil {
			log.Println("Plan executed successfully")
			break
		}

		log.Printf("Attempt %d failed: %v", attempt, err)

		// Don't retry non-retryable errors
		if !rod.IsRetryableError(err) {
			log.Println("Error is not retryable, stopping")
			break
		}

		if attempt < maxRetries {
			waitTime := time.Duration(attempt) * time.Second
			log.Printf("Retrying in %v...", waitTime)
			time.Sleep(waitTime)
		}
	}
}
```

### Advanced YAML Example

```yaml
name: Advanced YAML Plan
description: Production automation with error handling
settings:
  headless: true
  viewport_width: 1366
  viewport_height: 768
  anti_detection: true
  timeout: "30s"
  keep_open: false
  user_data_dir: "./browser_profile"

variables:
  search_term: "browser automation"
  timeout_duration: "10s"

tasks:
  - type: navigate
    url: https://www.google.com
    description: Navigate to Google
    timeout: "${timeout_duration}"

  - type: wait_visible
    selector: input[name='q']
    selector_type: query
    timeout: "${timeout_duration}"
    description: Wait for search box

  - type: type
    selector: input[name='q']
    selector_type: query
    value: "${search_term}"
    description: Type search query

  - type: if
    condition: 'document.querySelector(''input[name="btnK"]'') !== null'
    description: Check if search button exists
    tasks:
      - type: click
        selector: input[name='btnK']
        selector_type: query
        description: Click search button

  - type: wait_visible
    selector: "#search"
    selector_type: query
    timeout: "${timeout_duration}"
    description: Wait for search results

  - type: get_text
    selector: "#search .g:first-child h3"
    selector_type: query
    variable: first_result_title
    description: Get first result title

  - type: screenshot
    selector: "#search"
    selector_type: query
    filename: "search_results_${search_term}.png"
    description: Take screenshot of results
```

## 🔧 Configuration

### Resource Management Options

```go
// Configure resource management
resourceOpts := &rod.ResourceManagerOptions{
    MaxBrowsers:         5,     // Maximum concurrent browsers
    MaxPagesPerBrowser:  10,    // Maximum pages per browser
    CleanupInterval:     5 * time.Minute,  // Cleanup frequency
    MaxIdleTime:         10 * time.Minute, // Resource idle timeout
    BrowserPoolSize:     3,     // Browser pool size
}

ba := rod.NewBrowserAutomation().WithResourceManager(resourceOpts)
```

### Error Handling Configuration

```go
// Custom error handling
plan.Settings.HandleChallenges = true
plan.Settings.MonitorChallenges = true
plan.Settings.ChallengeTimeout = "60s"
plan.Settings.ChallengeRetries = 3
plan.Settings.AutoSolveCaptchas = true
```

## 📊 Error Codes Reference

### Browser Errors

- `BROWSER_INIT_FAILED` - Browser initialization failed
- `BROWSER_CLOSED` - Browser was closed unexpectedly
- `PAGE_CREATE_FAILED` - Page creation failed
- `PAGE_NAVIGATION_FAILED` - Navigation failed

### Element Errors

- `ELEMENT_NOT_FOUND` - Element not found with given selector
- `ELEMENT_NOT_VISIBLE` - Element exists but not visible
- `ELEMENT_NOT_ENABLED` - Element exists but not enabled
- `ELEMENT_INTERACTION_FAILED` - Failed to interact with element
- `SELECTOR_INVALID` - Invalid selector provided

### Task Errors

- `TASK_EXECUTION_FAILED` - General task execution failure
- `TASK_TIMEOUT` - Task execution timed out
- `TASK_VALIDATION_FAILED` - Task validation failed

### Resource Errors

- `RESOURCE_EXHAUSTED` - Resource limits exceeded
- `RESOURCE_LEAK` - Resource leak detected
- `RESOURCE_CLEANUP_FAILED` - Resource cleanup failed

### Network Errors

- `NETWORK_TIMEOUT` - Network operation timed out
- `NETWORK_ERROR` - General network error
- `PROXY_ERROR` - Proxy configuration error

## 🔄 Task Types

### Navigation

- `navigate`: Navigate to a URL
- `navigate_back`: Navigate back in history
- `navigate_forward`: Navigate forward in history
- `reload`: Reload the current page

### Element Interaction

- `click`: Click on an element
- `double_click`: Double-click on an element
- `type`: Type text into an element
- `select`: Select an option in a select element
- `clear`: Clear an input element
- `submit`: Submit a form
- `focus`: Focus an element
- `blur`: Remove focus from an element
- `scroll_into_view`: Scroll an element into view

### Wait Operations

- `wait_visible`: Wait for an element to become visible
- `wait_not_visible`: Wait for an element to become invisible
- `wait_present`: Wait for an element to be present in the DOM
- `wait_not_present`: Wait for an element to not be present in the DOM
- `wait_enabled`: Wait for an element to be enabled
- `sleep`: Wait for a specified duration

### Data Extraction

- `get_text`: Get the text content of an element
- `get_html`: Get the inner HTML of an element
- `get_outer_html`: Get the outer HTML of an element
- `get_attribute`: Get an attribute of an element
- `get_value`: Get the value of an input element
- `screenshot`: Take a screenshot of an element
- `full_screenshot`: Take a full page screenshot

### JavaScript Execution

- `evaluate`: Evaluate JavaScript code
- `poll`: Poll until a JavaScript expression returns true

### Flow Control

- `if`: Execute tasks conditionally
- `loop`: Execute tasks in a loop
- `for_each`: Execute tasks for each item in an array

### Challenge Handling

- `detect_challenge`: Detect if there's a challenge on the current page
- `handle_challenge`: Attempt to handle the detected challenge
- `solve_recaptcha`: Attempt to solve a reCAPTCHA challenge
- `solve_cloudflare`: Attempt to solve a Cloudflare challenge
- `monitor_challenges`: Continuously monitor for challenges

### Extension Management

- `install_extension`: Install a Chrome extension

## ⚙️ Settings Reference

### Basic Settings

- `headless`: Whether to run browser in headless mode
- `timeout`: Default timeout for tasks
- `user_agent`: Custom user agent string
- `viewport_width`: Browser viewport width
- `viewport_height`: Browser viewport height
- `proxy`: Proxy server configuration
- `anti_detection`: Enable stealth mode features

### Resource Management

- `keep_open`: Keep browser open after execution
- `user_data_dir`: Browser profile directory
- `device_scale_factor`: Display scale factor

### Challenge Handling

- `handle_challenges`: Enable automatic challenge detection
- `monitor_challenges`: Continuously monitor for challenges
- `challenge_timeout`: Timeout for challenge resolution
- `challenge_retries`: Number of retry attempts
- `challenge_retry_delay`: Delay between retries
- `auto_solve_captchas`: Enable automatic captcha solving

### Extension Support

- `extensions`: List of extension paths or URLs
- `extension_load_type`: Extension loading method ("local", "store", "auto")
- `extension_ids`: Chrome Web Store extension IDs

### Advanced Configuration

- `headers`: Custom HTTP headers
- `cookies`: Custom cookies to set
- `default_wait`: Default wait time for operations

## 🎯 Selector Types

- `query`: CSS selector (default)
- `query_all`: CSS selector for multiple elements
- `id`: Element ID selector
- `xpath`: XPath expression
- `js_path`: JavaScript path selector
- `text`: Text content selector

## 🧪 Testing

Run the test suite:

```bash
go test ./...
```

Run tests with verbose output:

```bash
go test -v ./...
```

Run specific test categories:

```bash
# Error handling tests
go test -v -run TestBrowserAutomationError

# Resource management tests
go test -v -run TestResourceManager
```

## 📝 Migration Guide

### From v1.x to v2.0

1. **Error Handling**: Update error handling code to use new error types
2. **Resource Management**: Leverage new resource pooling for better performance
3. **API Changes**: Some methods now return errors for better validation

```go
// Old way (v1.x)
ba.AddNavigateTask(plan, url, description)

// New way (v2.0)
if err := ba.AddNavigateTask(plan, url, description); err != nil {
    // Handle validation error
}
```

## 🤝 Contributing

Contributions are welcome! Please ensure:

1. All tests pass
2. Code follows Go best practices
3. Error handling uses standardized error types
4. Resource management is properly implemented
5. Documentation is updated accordingly

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🔗 Related Projects

- [go-rod/rod](https://github.com/go-rod/rod) - Core browser automation library
- [go-rod/stealth](https://github.com/go-rod/stealth) - Anti-detection capabilities
