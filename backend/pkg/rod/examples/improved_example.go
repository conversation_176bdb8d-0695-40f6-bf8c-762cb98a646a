package examples

import (
	"fmt"
	"time"

	"a8.tools/backend/pkg/rod"
	// Import the parent rod package
)

// DemonstrateImprovedFeatures shows the new error handling and resource management
func DemonstrateImprovedFeatures() {
	// Create a new browser automation instance
	ba := rod.NewBrowserAutomation()

	fmt.Println("=== Demonstrating Improved Error Handling and Resource Management ===")

	// Create a new task plan
	plan := ba.CreateNewPlan("Improved Example", "Demonstrates new error handling and resource management")

	// Configure settings with resource management in mind
	plan.Settings.Headless = true
	plan.Settings.ViewportWidth = 1366
	plan.Settings.ViewportHeight = 768
	plan.Settings.AntiDetection = true
	plan.Settings.KeepOpen = false // Ensure resources are cleaned up

	// Add tasks with improved error handling
	if err := ba.AddNavigateTask(plan, "https://example.com", "Navigate to example.com"); err != nil {
		handleBrowserAutomationError("Adding navigate task", err)
		return
	}

	ba.AddWaitVisibleTask(plan, "h1", rod.SelectorQuery, "5s", "Wait for heading")

	ba.AddGetTextTask(plan, "h1", rod.SelectorQuery, "page_title", "Get page title")

	// Execute the plan with improved error handling
	fmt.Println("Executing plan...")
	err := ba.ExecutePlan(plan)
	if err != nil {
		handleBrowserAutomationError("Executing plan", err)

		// Demonstrate different error types
		if rod.IsBrowserError(err) {
			fmt.Println("  → Browser-related error detected")
		} else if rod.IsElementError(err) {
			fmt.Println("  → Element-related error detected")
		} else if rod.IsTimeoutError(err) {
			fmt.Println("  → Timeout error detected")
		}

		if rod.IsRetryableError(err) {
			fmt.Println("  → This error is retryable")
		} else {
			fmt.Println("  → This error is not retryable")
		}
		return
	}

	// Get results
	results := ba.GetTaskResults()
	variables := ba.GetVariables()

	fmt.Printf("Execution completed successfully!\n")
	fmt.Printf("Results: %d items\n", len(results))
	fmt.Printf("Variables: %d items\n", len(variables))

	if title, exists := variables["page_title"]; exists {
		fmt.Printf("Page title: %s\n", title)
	}

	// Demonstrate graceful shutdown
	demonstrateGracefulShutdown(ba)
}

func demonstrateGracefulShutdown(ba *rod.BrowserAutomation) {
	fmt.Println("\n=== Demonstrating Graceful Shutdown ===")

	// Shutdown the browser automation system gracefully
	fmt.Println("Shutting down browser automation system...")

	start := time.Now()
	err := ba.Shutdown()
	duration := time.Since(start)

	if err != nil {
		handleBrowserAutomationError("Shutting down", err)
		return
	}

	fmt.Printf("Shutdown completed successfully in %v\n", duration)
}

func handleBrowserAutomationError(operation string, err error) {
	fmt.Printf("Error during %s: %s\n", operation, err.Error())

	// If it's our custom error type, show additional context
	if baErr, ok := err.(*rod.BrowserAutomationError); ok {
		fmt.Printf("  Error Code: %s\n", baErr.Code)
		fmt.Printf("  Message: %s\n", baErr.Message)
		fmt.Printf("  Timestamp: %d\n", baErr.Timestamp)

		if len(baErr.Context) > 0 {
			fmt.Println("  Context:")
			for key, value := range baErr.Context {
				fmt.Printf("    %s: %v\n", key, value)
			}
		}

		if baErr.Cause != nil {
			fmt.Printf("  Root Cause: %s\n", baErr.Cause.Error())
		}
	}

	// If it's an error group, show all errors
	if errorGroup, ok := err.(*rod.ErrorGroup); ok {
		fmt.Printf("  Multiple errors occurred (%d):\n", len(errorGroup.Errors))
		for i, subErr := range errorGroup.Errors {
			fmt.Printf("    %d. %s\n", i+1, subErr.Error())
		}
	}
}
