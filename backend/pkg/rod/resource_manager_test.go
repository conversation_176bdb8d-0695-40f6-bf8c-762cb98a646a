package rod

import (
	"context"
	"log"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestResourceManager(t *testing.T) {
	// Skip in CI environments where browser may not be available
	if os.Getenv("CI") != "" {
		t.Skip("Skipping resource manager test in CI environment")
	}

	logger := log.New(os.Stdout, "[Test] ", log.LstdFlags)

	opts := &ResourceManagerOptions{
		MaxBrowsers:        2,
		MaxPagesPerBrowser: 3,
		CleanupInterval:    1 * time.Minute,
		MaxIdleTime:        2 * time.Minute,
		BrowserPoolSize:    1,
	}

	rm := NewResourceManager(logger, opts)
	defer func() {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		rm.Shutdown(ctx)
	}()

	// Test getting a browser
	ctx := context.Background()
	settings := TaskSettings{
		Headless:       true,
		ViewportWidth:  1024,
		ViewportHeight: 768,
		AntiDetection:  false,
	}

	browser, err := rm.GetBrowser(ctx, settings)
	require.NoError(t, err)
	assert.NotNil(t, browser)
	assert.NotEmpty(t, browser.ID)
	assert.NotNil(t, browser.Browser)

	// Test getting a page
	page, err := rm.GetPage(ctx, browser.ID)
	require.NoError(t, err)
	assert.NotNil(t, page)
	assert.NotEmpty(t, page.ID)
	assert.NotNil(t, page.Page)
	assert.Equal(t, browser.ID, page.BrowserID)

	// Test page limit
	for i := 1; i < opts.MaxPagesPerBrowser; i++ {
		_, err := rm.GetPage(ctx, browser.ID)
		require.NoError(t, err)
	}

	// This should fail due to page limit
	_, err = rm.GetPage(ctx, browser.ID)
	assert.Error(t, err)
	assert.True(t, IsRetryableError(err) == false) // Resource exhausted is not retryable

	// Test releasing resources
	err = rm.ReleasePage(page.ID)
	assert.NoError(t, err)

	err = rm.ReleaseBrowser(browser.ID)
	assert.NoError(t, err)
}

func TestResourceManagerLimits(t *testing.T) {
	// Skip in CI environments
	if os.Getenv("CI") != "" {
		t.Skip("Skipping resource manager test in CI environment")
	}

	logger := log.New(os.Stdout, "[Test] ", log.LstdFlags)

	opts := &ResourceManagerOptions{
		MaxBrowsers:        1,
		MaxPagesPerBrowser: 1,
		CleanupInterval:    1 * time.Minute,
		MaxIdleTime:        2 * time.Minute,
		BrowserPoolSize:    0, // No pooling
	}

	rm := NewResourceManager(logger, opts)
	defer func() {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		rm.Shutdown(ctx)
	}()

	ctx := context.Background()
	settings := TaskSettings{
		Headless:       true,
		ViewportWidth:  1024,
		ViewportHeight: 768,
	}

	// Get the first browser
	browser1, err := rm.GetBrowser(ctx, settings)
	require.NoError(t, err)

	// Try to get a second browser - should fail
	_, err = rm.GetBrowser(ctx, settings)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "Maximum number of browsers")

	// Release the first browser
	err = rm.ReleaseBrowser(browser1.ID)
	assert.NoError(t, err)

	// Now getting a browser should work again
	_, err = rm.GetBrowser(ctx, settings)
	assert.NoError(t, err)
}

func TestManagedBrowserMethods(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	browser := &ManagedBrowser{
		ID:         "test-browser",
		CreatedAt:  time.Now().Add(-5 * time.Minute),
		LastUsedAt: time.Now().Add(-3 * time.Minute),
		PageCount:  0,
		ctx:        ctx,
		cancel:     cancel,
	}

	// Test IsIdle
	assert.True(t, browser.IsIdle(2*time.Minute))
	assert.False(t, browser.IsIdle(5*time.Minute))

	// Test updateLastUsed
	oldTime := browser.LastUsedAt
	time.Sleep(10 * time.Millisecond) // Ensure time difference
	browser.updateLastUsed()
	assert.True(t, browser.LastUsedAt.After(oldTime))
}

func TestManagedPageMethods(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	page := &ManagedPage{
		ID:         "test-page",
		BrowserID:  "test-browser",
		CreatedAt:  time.Now().Add(-5 * time.Minute),
		LastUsedAt: time.Now().Add(-3 * time.Minute),
		ctx:        ctx,
		cancel:     cancel,
	}

	// Test IsIdle
	assert.True(t, page.IsIdle(2*time.Minute))
	assert.False(t, page.IsIdle(5*time.Minute))

	// Test updateLastUsed
	oldTime := page.LastUsedAt
	time.Sleep(10 * time.Millisecond) // Ensure time difference
	page.updateLastUsed()
	assert.True(t, page.LastUsedAt.After(oldTime))
}

func TestBrowserPool(t *testing.T) {
	pool := &BrowserPool{
		browsers: make([]*ManagedBrowser, 0, 2),
		maxSize:  2,
	}

	// Test empty pool
	browser := pool.Get()
	assert.Nil(t, browser)

	// Add browsers to pool
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	browser1 := &ManagedBrowser{
		ID:        "browser1",
		PageCount: 0,
		ctx:       ctx,
		cancel:    cancel,
	}

	browser2 := &ManagedBrowser{
		ID:        "browser2",
		PageCount: 0,
		ctx:       ctx,
		cancel:    cancel,
	}

	// Test Put
	assert.True(t, pool.Put(browser1))
	assert.True(t, pool.Put(browser2))

	// Pool should be full now
	browser3 := &ManagedBrowser{
		ID: "browser3",
	}
	assert.False(t, pool.Put(browser3))

	// Test Get (LIFO)
	retrieved := pool.Get()
	assert.Equal(t, browser2.ID, retrieved.ID)

	retrieved = pool.Get()
	assert.Equal(t, browser1.ID, retrieved.ID)

	// Pool should be empty now
	retrieved = pool.Get()
	assert.Nil(t, retrieved)
}

func TestResourceManagerShutdown(t *testing.T) {
	// Skip in CI environments
	if os.Getenv("CI") != "" {
		t.Skip("Skipping resource manager test in CI environment")
	}

	logger := log.New(os.Stdout, "[Test] ", log.LstdFlags)
	rm := NewResourceManager(logger, nil)

	// Create some resources
	ctx := context.Background()
	settings := TaskSettings{
		Headless:       true,
		ViewportWidth:  1024,
		ViewportHeight: 768,
	}

	browser, err := rm.GetBrowser(ctx, settings)
	require.NoError(t, err)

	page, err := rm.GetPage(ctx, browser.ID)
	require.NoError(t, err)

	// Test graceful shutdown
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	err = rm.Shutdown(shutdownCtx)
	assert.NoError(t, err)

	// Verify resources are cleaned up
	assert.False(t, browser.IsActive)
	assert.False(t, page.IsActive)
}
