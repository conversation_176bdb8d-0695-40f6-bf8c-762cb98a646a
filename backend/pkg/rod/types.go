package rod

import (
	"time"
)

// TaskType represents the type of task to be executed
type TaskType string

const (
	// Navigation tasks
	TaskNavigate      TaskType = "navigate"
	TaskNavigateBack  TaskType = "navigate_back"
	TaskNavigateForwd TaskType = "navigate_forward"
	TaskReload        TaskType = "reload"

	// Query tasks
	TaskClick       TaskType = "click"
	TaskDoubleClick TaskType = "double_click"
	TaskTypeText    TaskType = "type"
	TaskSelect      TaskType = "select"
	TaskClear       TaskType = "clear"
	TaskSubmit      TaskType = "submit"
	TaskFocus       TaskType = "focus"
	TaskBlur        TaskType = "blur"
	TaskScrollInto  TaskType = "scroll_into_view"

	// Wait tasks
	TaskWaitVisible    TaskType = "wait_visible"
	TaskWaitNotVisible TaskType = "wait_not_visible"
	TaskWaitPresent    TaskType = "wait_present"
	TaskWaitNotPresent TaskType = "wait_not_present"
	TaskWaitEnabled    TaskType = "wait_enabled"
	TaskWaitSelected   TaskType = "wait_selected"
	TaskSleep          TaskType = "sleep"

	// Extract tasks
	TaskGetText        TaskType = "get_text"
	TaskGetHTML        TaskType = "get_html"
	TaskGetOuterHTML   TaskType = "get_outer_html"
	TaskGetAttribute   TaskType = "get_attribute"
	TaskGetValue       TaskType = "get_value"
	TaskScreenshot     TaskType = "screenshot"
	TaskFullScreenshot TaskType = "full_screenshot"

	// JavaScript tasks
	TaskEvaluate TaskType = "evaluate"
	TaskPoll     TaskType = "poll"

	// Flow control
	TaskIf       TaskType = "if"
	TaskLoop     TaskType = "loop"
	TaskForEach  TaskType = "for_each"
	TaskBreak    TaskType = "break"
	TaskContinue TaskType = "continue"

	// Challenge handling
	TaskDetectChallenge   TaskType = "detect_challenge"
	TaskHandleChallenge   TaskType = "handle_challenge"
	TaskSolveReCaptcha    TaskType = "solve_recaptcha"
	TaskSolveCloudflare   TaskType = "solve_cloudflare"
	TaskSolveHCaptcha     TaskType = "solve_hcaptcha"
	TaskMonitorChallenges TaskType = "monitor_challenges"

	// Extension handling
	TaskInstallExtension TaskType = "install_extension"
)

// SelectorType represents the type of selector to use
type SelectorType string

const (
	SelectorQuery    SelectorType = "query"     // CSS selector
	SelectorQueryAll SelectorType = "query_all" // CSS selector for multiple elements
	SelectorID       SelectorType = "id"        // Element ID
	SelectorXPath    SelectorType = "xpath"     // XPath selector
	SelectorJSPath   SelectorType = "js_path"   // JavaScript path
	SelectorText     SelectorType = "text"      // Text content
	SelectorNodeID   SelectorType = "node_id"   // Node ID
)

// Task represents a single automation task
type Task struct {
	Type         TaskType     `yaml:"type"`
	Description  string       `yaml:"description,omitempty"`
	Selector     string       `yaml:"selector,omitempty"`
	SelectorType SelectorType `yaml:"selector_type,omitempty"`
	URL          string       `yaml:"url,omitempty"`
	Value        string       `yaml:"value,omitempty"`
	Timeout      string       `yaml:"timeout,omitempty"`
	WaitTime     string       `yaml:"wait,omitempty"`
	Attribute    string       `yaml:"attribute,omitempty"`
	JavaScript   string       `yaml:"javascript,omitempty"`
	FileName     string       `yaml:"filename,omitempty"`
	Quality      int          `yaml:"quality,omitempty"`
	Variable     string       `yaml:"variable,omitempty"`
	Condition    string       `yaml:"condition,omitempty"`
	Tasks        []Task       `yaml:"tasks,omitempty"`
	RetryCount   int          `yaml:"retry_count,omitempty"`
	RetryDelay   string       `yaml:"retry_delay,omitempty"`
}

// TaskPlan represents a complete automation plan
type TaskPlan struct {
	Name        string                 `json:"name" yaml:"name"`
	Description string                 `json:"description,omitempty" yaml:"description,omitempty"`
	Variables   map[string]interface{} `json:"variables,omitempty" yaml:"variables,omitempty"`
	Settings    TaskSettings           `json:"settings,omitempty" yaml:"settings,omitempty"`
	Tasks       []Task                 `json:"tasks" yaml:"tasks"`
}

// TaskSettings represents global settings for the task plan
type TaskSettings struct {
	Headless       bool              `yaml:"headless"`
	Timeout        string            `yaml:"timeout,omitempty"`
	UserAgent      string            `yaml:"user_agent,omitempty"`
	ViewportWidth  int               `yaml:"viewport_width,omitempty"`
	ViewportHeight int               `yaml:"viewport_height,omitempty"`
	Proxy          string            `yaml:"proxy,omitempty"`
	AntiDetection  bool              `yaml:"anti_detection,omitempty"`
	DefaultWait    string            `yaml:"default_wait,omitempty"`
	Headers        map[string]string `yaml:"headers,omitempty"`
	Cookies        []Cookie          `yaml:"cookies,omitempty"`
	KeepOpen       bool              `yaml:"keep_open,omitempty"`     // Keep browser window open after tasks complete
	UserDataDir    string            `yaml:"user_data_dir,omitempty"` // User data directory for browser profile

	// Extension settings
	Extensions        []string `yaml:"extensions,omitempty"`          // List of extension paths or URLs to install
	ExtensionIDs      []string `yaml:"extension_ids,omitempty"`       // List of extension IDs to load
	ExtensionLoadType string   `yaml:"extension_load_type,omitempty"` // "local" or "store" or "auto"

	// Challenge handling settings
	HandleChallenges      bool    `yaml:"handle_challenges,omitempty"`       // Enable automatic challenge handling
	MonitorChallenges     bool    `yaml:"monitor_challenges,omitempty"`      // Continuously monitor for challenges
	ChallengeTimeout      string  `yaml:"challenge_timeout,omitempty"`       // Timeout for challenge handling
	ChallengeRetries      int     `yaml:"challenge_retries,omitempty"`       // Number of retries for challenge handling
	ChallengeRetryDelay   string  `yaml:"challenge_retry_delay,omitempty"`   // Delay between retries
	AutoSolveCaptchas     bool    `yaml:"auto_solve_captchas,omitempty"`     // Try to automatically solve captchas
	CaptchaSolvingService string  `yaml:"captcha_solving_service,omitempty"` // External captcha solving service to use
	CaptchaAPIKey         string  `yaml:"captcha_api_key,omitempty"`         // API key for captcha solving service
	DeviceScaleFactor     float64 `yaml:"device_scale_factor,omitempty"`     // Device scale factor for viewport
}

// Cookie represents a browser cookie
type Cookie struct {
	Name     string `yaml:"name"`
	Value    string `yaml:"value"`
	Domain   string `yaml:"domain,omitempty"`
	Path     string `yaml:"path,omitempty"`
	Secure   bool   `yaml:"secure,omitempty"`
	HTTPOnly bool   `yaml:"http_only,omitempty"`
}

// TaskResult represents the result of a task execution
type TaskResult struct {
	Success     bool
	Value       interface{}
	Screenshot  []byte
	Error       error
	ElapsedTime time.Duration
}

// TaskContext holds the context for task execution
type TaskContext struct {
	Variables map[string]interface{}
	Results   map[string]TaskResult
}
