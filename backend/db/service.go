package db

import (
	"errors"
	"fmt"
	"reflect"
	"strings"

	"gorm.io/gorm"
)

// Model定义了所有模型需要实现的基本接口
type Model interface {
	TableName() string
	GetID() uint
	SetID(id uint)
}

// Pageable定义了分页查询的参数
type Pageable struct {
	Page     int    `json:"page" form:"page"`
	PageSize int    `json:"pageSize" form:"pageSize"`
	Sort     string `json:"sort" form:"sort"`
	Order    string `json:"order" form:"order"`
}

// PageResult定义了分页查询的结果
type PageResult[T any] struct {
	Total     int64 `json:"total"`     // 总记录数
	TotalPage int   `json:"totalPage"` // 总页数
	Page      int   `json:"page"`      // 当前页码
	PageSize  int   `json:"pageSize"`  // 每页大小
	Data      []T   `json:"data"`      // 当前页数据
}

// BaseService提供通用的CRUD操作
// T可以是Model接口的指针实现
type BaseService[T any] struct {
	db *gorm.DB
}

// DB 返回 BaseService 内部的 gorm.DB 实例，主要用于测试
func (s *BaseService[T]) DB() *gorm.DB {
	return s.db
}

// NewBaseService创建一个新的BaseService实例
func NewBaseService[T any]() *BaseService[T] {
	return &BaseService[T]{
		db: DB,
	}
}

// Create创建一个新的记录
func (s *BaseService[T]) Create(model *T) error {
	return s.db.Create(model).Error
}

// BatchCreate批量创建记录
func (s *BaseService[T]) BatchCreate(models []T) error {
	return s.db.Create(&models).Error
}

// GetByID根据ID获取记录
func (s *BaseService[T]) GetByID(id uint) (*T, error) {
	var model T
	err := s.db.Where("id = ?", id).First(&model).Error
	return &model, err
}

// Update更新记录
func (s *BaseService[T]) Update(model *T) error {
	return s.db.Save(model).Error
}

// Delete删除记录
func (s *BaseService[T]) Delete(model *T) error {
	return s.db.Delete(model).Error
}

// DeleteByID根据ID删除记录
func (s *BaseService[T]) DeleteByID(id uint) error {
	return s.db.Delete(new(T), "id = ?", id).Error
}

// BatchDelete批量删除记录
func (s *BaseService[T]) BatchDelete(ids []uint) error {
	if len(ids) == 0 {
		return errors.New("ID列表不能为空")
	}

	return s.db.Delete(new(T), "id IN ?", ids).Error
}

// List获取所有记录
func (s *BaseService[T]) List() ([]T, error) {
	var models []T
	err := s.db.Find(&models).Error
	return models, err
}

// escapeKeyword 将SQL关键字用反引号包裹
func escapeKeyword(key string) string {
	// 移除可能已存在的反引号，防止重复添加
	key = strings.Trim(key, "`")

	// 检查是否包含条件操作符
	if strings.Contains(key, " ") {
		parts := strings.SplitN(key, " ", 2)
		fieldName := parts[0]
		operator := parts[1]
		return "`" + fieldName + "` " + operator
	}

	return "`" + key + "`"
}

// ListWithConditions获取符合条件的所有记录
func (s *BaseService[T]) ListWithConditions(conditions map[string]interface{}) ([]T, error) {
	var models []T
	query := s.db

	for key, value := range conditions {
		// 处理SQL关键字
		escapedKey := escapeKeyword(key)
		query = query.Where(escapedKey+" = ?", value)
	}

	err := query.Find(&models).Error
	return models, err
}

// Count计算符合条件的记录数
func (s *BaseService[T]) Count(conditions map[string]interface{}) (int64, error) {
	var count int64
	var model T
	query := s.db.Model(&model)

	for key, value := range conditions {
		if value != nil && value != "" {
			// 处理SQL关键字
			escapedKey := escapeKeyword(key)
			query = query.Where(escapedKey+" = ?", value)
		}
	}

	return count, query.Count(&count).Error
}

// QueryCondition 查询条件结构
type QueryCondition struct {
	Field    string      // 字段名
	Operator string      // 操作符: =, !=, >, <, >=, <=, LIKE, IN, NOT IN, BETWEEN
	Value    interface{} // 值
	Logic    string      // 逻辑关系: AND, OR (默认AND)
}

// QueryBuilder 查询构建器
type QueryBuilder struct {
	Conditions []QueryCondition
	Groups     []QueryBuilder // 支持嵌套的条件组
	Logic      string         // 组内的逻辑关系: AND, OR
}

// Page 分页查询 - 新版本
func (s *BaseService[T]) Page(pageable Pageable, queryBuilder *QueryBuilder) (*PageResult[T], error) {
	// 校正分页参数
	if pageable.Page <= 0 {
		pageable.Page = 1
	}
	if pageable.PageSize <= 0 {
		pageable.PageSize = 10
	}

	var model T
	query := s.db.Model(&model)

	// 应用查询条件
	if queryBuilder != nil {
		query = s.applyQueryBuilder(query, queryBuilder)
	}

	// 计算总记录数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 添加排序
	if pageable.Sort != "" {
		sortField := escapeKeyword(pageable.Sort)
		orderStr := sortField
		if pageable.Order != "" {
			orderStr = orderStr + " " + pageable.Order
		} else {
			orderStr = orderStr + " DESC"
		}
		// 如果是对 id 字段排序，强制转换为数值排序
		if strings.ToLower(pageable.Sort) == "id" {
			order := "DESC"
			if pageable.Order != "" {
				order = strings.ToUpper(pageable.Order)
			}
			orderStr = "CAST(`id` AS UNSIGNED) " + order
		}
		query = query.Order(orderStr)
	} else {
		query = query.Order("CAST(`id` AS UNSIGNED) DESC")
	}

	// 执行分页查询
	var models []T
	err := query.Offset((pageable.Page - 1) * pageable.PageSize).
		Limit(pageable.PageSize).
		Find(&models).Error

	if err != nil {
		return nil, err
	}

	// 计算总页数
	totalPage := int(total) / pageable.PageSize
	if int(total)%pageable.PageSize > 0 {
		totalPage++
	}

	return &PageResult[T]{
		Total:     total,
		TotalPage: totalPage,
		Page:      pageable.Page,
		PageSize:  pageable.PageSize,
		Data:      models,
	}, nil
}

// applyQueryBuilder 应用查询构建器
func (s *BaseService[T]) applyQueryBuilder(query *gorm.DB, builder *QueryBuilder) *gorm.DB {
	if builder == nil {
		return query
	}

	// 处理嵌套的条件组
	if len(builder.Groups) > 0 {
		if builder.Logic == "OR" {
			query = query.Where(func(db *gorm.DB) *gorm.DB {
				for i, group := range builder.Groups {
					subQuery := s.buildConditions(db, group.Conditions)
					if i == 0 {
						db = subQuery
					} else {
						db = db.Or(subQuery)
					}
				}
				return db
			})
		} else { // 默认 AND
			for _, group := range builder.Groups {
				query = query.Where(func(db *gorm.DB) *gorm.DB {
					return s.applyQueryBuilder(db, &group)
				})
			}
		}
	}

	// 处理当前层级的条件
	if len(builder.Conditions) > 0 {
		query = s.buildConditions(query, builder.Conditions)
	}

	return query
}

// buildConditions 构建条件
func (s *BaseService[T]) buildConditions(query *gorm.DB, conditions []QueryCondition) *gorm.DB {
	for i, cond := range conditions {
		field := escapeKeyword(cond.Field)

		// 构建条件语句
		var whereClause string
		var args []interface{}

		switch strings.ToUpper(cond.Operator) {
		case "LIKE":
			whereClause = field + " LIKE ?"
			args = append(args, "%"+fmt.Sprint(cond.Value)+"%")
		case "IN":
			whereClause = field + " IN ?"
			args = append(args, cond.Value)
		case "NOT IN":
			whereClause = field + " NOT IN ?"
			args = append(args, cond.Value)
		case "BETWEEN":
			if values, ok := cond.Value.([]interface{}); ok && len(values) == 2 {
				whereClause = field + " BETWEEN ? AND ?"
				args = append(args, values[0], values[1])
			}
		case "IS NULL":
			whereClause = field + " IS NULL"
		case "IS NOT NULL":
			whereClause = field + " IS NOT NULL"
		default:
			whereClause = field + " " + cond.Operator + " ?"
			args = append(args, cond.Value)
		}

		// 应用条件
		if i == 0 || cond.Logic != "OR" {
			query = query.Where(whereClause, args...)
		} else {
			query = query.Or(whereClause, args...)
		}
	}

	return query
}

// 便捷方法：创建简单的AND条件
func NewAndConditions(conditions map[string]interface{}) *QueryBuilder {
	builder := &QueryBuilder{Logic: "AND"}
	for field, value := range conditions {
		operator := "="
		if reflect.TypeOf(value).Kind() == reflect.String {
			operator = "LIKE"
		}
		builder.Conditions = append(builder.Conditions, QueryCondition{
			Field:    field,
			Operator: operator,
			Value:    value,
			Logic:    "AND",
		})
	}
	return builder
}

// 便捷方法：创建OR条件组
func NewOrGroup(conditions ...QueryCondition) QueryBuilder {
	return QueryBuilder{
		Conditions: conditions,
		Logic:      "OR",
	}
}

func (s *BaseService[T]) PageList(page, pageSize int) (*PageResult[T], error) {
	pageable := Pageable{
		Page:     page,
		PageSize: pageSize,
	}
	return s.Page(pageable, nil)
}

func (s *BaseService[T]) SearchPageList(page, pageSize int, key string, fileds []string) (*PageResult[T], error) {
	pageable := Pageable{
		Page:     page,
		PageSize: pageSize,
	}

	var conditions []QueryCondition
	if len(fileds) > 0 && key != "" { // 仅当字段列表和搜索键都不为空时创建条件
		for _, field := range fileds {
			conditions = append(conditions, QueryCondition{
				Field:    field,
				Operator: "LIKE", // 假设所有字段都使用 LIKE 操作符
				Value:    key,    // 假设所有字段都使用相同的搜索键
				Logic:    "OR",   // 假设字段间的逻辑是 OR
			})
		}
	}

	builder := &QueryBuilder{
		Conditions: conditions,
	}
	return s.Page(pageable, builder)
}
