package db

import (
	"io"
	"log"
	"os"
	"sync"
	"time"

	"a8.tools/backend/config"
	"a8.tools/backend/models"
	"a8.tools/backend/models/account"
	"a8.tools/backend/utils/file"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	DB     *gorm.DB
	dbOnce sync.Once
)

// getGormLogger 根据构建标签返回适当的GORM日志配置
func getGormLogger() logger.Interface {
	if config.IsDebugBuild {
		// 调试模式：启用详细日志，便于开发调试
		return logger.New(
			log.New(os.Stdout, "\r\n", log.LstdFlags), // 输出到标准输出
			logger.Config{
				SlowThreshold:             time.Second, // 慢SQL阈值，超过1秒的查询会被标记为慢查询
				LogLevel:                  logger.Info, // 日志级别设为Info，会显示所有SQL语句
				IgnoreRecordNotFoundError: true,        // 忽略记录未找到的错误
				Colorful:                  true,        // 启用彩色打印，使日志更易读
			},
		)
	} else {
		// 生产模式：完全禁用日志，确保安全性
		return logger.New(
			log.New(io.Discard, "", 0), // 丢弃所有日志输出
			logger.Config{
				SlowThreshold:             0,             // 禁用慢查询检测
				LogLevel:                  logger.Silent, // 静默模式，不输出任何日志
				IgnoreRecordNotFoundError: true,          // 忽略记录未找到的错误
				Colorful:                  false,         // 禁用彩色打印
			},
		)
	}
}

func InitDB() error {
	var initErr error
	dbOnce.Do(func() {
		dbFile, err := file.GetDatabaseFile("app.db")
		if err != nil {
			initErr = err
			return
		}

		// 根据构建标签获取安全的日志配置
		gormLogger := getGormLogger()

		// 记录数据库初始化信息（仅在调试模式下详细记录）
		if config.IsDebugBuild {
			log.Printf("Debug mode: Initializing database with detailed logging enabled")
			log.Printf("Database file: %s", dbFile)
		} else {
			log.Printf("Production mode: Initializing database with secure logging")
		}

		// 基础 SQLite 连接参数（优化后的配置将通过 SQLiteOptimizer 应用）
		dsn := dbFile + "?_busy_timeout=5000"

		// Open the database connection
		db, err := gorm.Open(sqlite.Open(dsn), &gorm.Config{
			Logger: gormLogger, // 使用安全的日志配置
		})
		if err != nil {
			initErr = err
			return
		}

		// 获取通用数据库对象，设置连接池
		sqlDB, err := db.DB()
		if err != nil {
			initErr = err
			return
		}

		// 设置连接池参数
		// SetMaxIdleConns: 设置空闲连接池中连接的最大数量
		sqlDB.SetMaxIdleConns(10)
		// SetMaxOpenConns: 设置打开数据库连接的最大数量
		sqlDB.SetMaxOpenConns(100)
		// SetConnMaxLifetime: 设置连接可复用的最大时间
		sqlDB.SetConnMaxLifetime(time.Hour)
		// SetConnMaxIdleTime: 设置连接在连接池中最大空闲时间
		sqlDB.SetConnMaxIdleTime(time.Minute * 30)

		// 应用 SQLite 优化配置
		optimizer := NewSQLiteOptimizer(db)
		if err := optimizer.OptimizeSettings(); err != nil {
			log.Printf("警告: SQLite 优化配置失败: %v", err)
		}

		// 根据构建模式应用特定优化
		if !config.IsDebugBuild {
			if err := optimizer.OptimizeForProduction(); err != nil {
				log.Printf("警告: 生产环境优化失败: %v", err)
			}
		}

		// 执行基础模型迁移
		err = db.AutoMigrate(
			&account.Email{},
			&account.Discord{},
			&account.Telegram{},
			&account.Proxy{},
			&account.X{},
			&models.Wallet{},
			&models.Browser{},
			&models.Setting{},
		)

		if err != nil {
			initErr = err
			return
		}

		DB = db

		// 应用安全性改进
		securityConfig := DefaultSecurityConfig()
		if err := ApplySecurityImprovements(securityConfig); err != nil {
			log.Printf("警告: 应用安全性改进失败: %v", err)
		}
	})
	return initErr
}
