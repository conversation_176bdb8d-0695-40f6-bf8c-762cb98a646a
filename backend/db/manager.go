package db

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"gorm.io/gorm"
)

// DatabaseManager 数据库管理器
type DatabaseManager struct {
	db        *gorm.DB
	optimizer *SQLiteOptimizer
}

// NewDatabaseManager 创建数据库管理器
func NewDatabaseManager(db *gorm.DB) *DatabaseManager {
	return &DatabaseManager{
		db:        db,
		optimizer: NewSQLiteOptimizer(db),
	}
}

// GetDefaultManager 获取默认的数据库管理器（使用全局 DB）
func GetDefaultManager() *DatabaseManager {
	if DB == nil {
		log.Fatal("数据库未初始化，请先调用 InitDB()")
	}
	return NewDatabaseManager(DB)
}

// Status 显示数据库状态
func (dm *DatabaseManager) Status() (*DatabaseStatus, error) {
	status := &DatabaseStatus{
		Timestamp: time.Now(),
	}

	// 获取数据库统计信息
	stats, err := dm.optimizer.GetDatabaseStats()
	if err != nil {
		return nil, fmt.Errorf("获取数据库统计信息失败: %w", err)
	}
	status.Stats = stats

	// 检查数据库连接状态
	sqlDB, err := dm.db.DB()
	if err != nil {
		status.ConnectionStatus = "error"
		status.ConnectionError = err.Error()
	} else {
		if err := sqlDB.Ping(); err != nil {
			status.ConnectionStatus = "error"
			status.ConnectionError = err.Error()
		} else {
			status.ConnectionStatus = "connected"
		}
	}

	// 获取连接池统计
	if sqlDB != nil {
		dbStats := sqlDB.Stats()
		status.ConnectionPool = &ConnectionPoolStats{
			MaxOpenConnections: dbStats.MaxOpenConnections,
			OpenConnections:    dbStats.OpenConnections,
			InUse:              dbStats.InUse,
			Idle:               dbStats.Idle,
		}
	}

	return status, nil
}

// DatabaseStatus 数据库状态信息
type DatabaseStatus struct {
	Timestamp        time.Time            `json:"timestamp"`
	ConnectionStatus string               `json:"connection_status"`
	ConnectionError  string               `json:"connection_error,omitempty"`
	ConnectionPool   *ConnectionPoolStats `json:"connection_pool,omitempty"`
	Stats            *DatabaseStats       `json:"stats,omitempty"`
}

// ConnectionPoolStats 连接池统计信息
type ConnectionPoolStats struct {
	MaxOpenConnections int `json:"max_open_connections"`
	OpenConnections    int `json:"open_connections"`
	InUse              int `json:"in_use"`
	Idle               int `json:"idle"`
}

// Backup 备份数据库
func (dm *DatabaseManager) Backup(backupPath string) error {
	log.Printf("开始备份数据库到: %s", backupPath)

	// 执行检查点，确保 WAL 文件数据写入主数据库文件
	if err := dm.db.Exec("PRAGMA wal_checkpoint(TRUNCATE)").Error; err != nil {
		log.Printf("警告: WAL 检查点失败: %v", err)
	}

	// 使用 SQLite 的 VACUUM INTO 命令进行备份
	sql := fmt.Sprintf("VACUUM INTO '%s'", backupPath)
	if err := dm.db.Exec(sql).Error; err != nil {
		return fmt.Errorf("备份数据库失败: %w", err)
	}

	log.Printf("数据库备份完成: %s", backupPath)
	return nil
}

// Restore 恢复数据库（注意：这会替换当前数据库）
func (dm *DatabaseManager) Restore(backupPath string) error {
	log.Printf("警告: 准备从备份恢复数据库: %s", backupPath)
	log.Println("这将替换当前数据库的所有数据！")

	// 关闭当前连接
	sqlDB, err := dm.db.DB()
	if err != nil {
		return fmt.Errorf("获取数据库连接失败: %w", err)
	}

	if err := sqlDB.Close(); err != nil {
		return fmt.Errorf("关闭数据库连接失败: %w", err)
	}

	// 这里需要文件系统操作来替换数据库文件
	// 实际实现中需要根据具体的文件操作需求来完成
	log.Printf("数据库恢复功能需要在应用层实现文件替换逻辑")

	return fmt.Errorf("恢复功能需要应用层支持")
}

// Optimize 全面优化数据库
func (dm *DatabaseManager) Optimize() error {
	log.Println("开始全面优化数据库...")

	// 1. 优化 SQLite 设置
	if err := dm.optimizer.OptimizeSettings(); err != nil {
		return fmt.Errorf("优化 SQLite 设置失败: %w", err)
	}

	// 2. 索引优化已移除，用户自行维护

	// 3. 执行维护操作
	if err := dm.optimizer.PerformMaintenance(); err != nil {
		return fmt.Errorf("执行维护操作失败: %w", err)
	}

	log.Println("数据库全面优化完成")
	return nil
}

// 索引管理方法已移除，用户自行维护索引

// PrintStatus 打印数据库状态（格式化输出）
func (dm *DatabaseManager) PrintStatus() error {
	status, err := dm.Status()
	if err != nil {
		return err
	}

	fmt.Println("=== 数据库状态报告 ===")
	fmt.Printf("时间: %s\n", status.Timestamp.Format("2006-01-02 15:04:05"))
	fmt.Printf("连接状态: %s\n", status.ConnectionStatus)

	if status.ConnectionError != "" {
		fmt.Printf("连接错误: %s\n", status.ConnectionError)
	}

	if status.ConnectionPool != nil {
		fmt.Println("\n--- 连接池信息 ---")
		fmt.Printf("最大连接数: %d\n", status.ConnectionPool.MaxOpenConnections)
		fmt.Printf("当前连接数: %d\n", status.ConnectionPool.OpenConnections)
		fmt.Printf("使用中: %d\n", status.ConnectionPool.InUse)
		fmt.Printf("空闲: %d\n", status.ConnectionPool.Idle)
	}

	if status.Stats != nil {
		fmt.Println("\n--- 数据库统计 ---")
		fmt.Printf("页面数量: %d\n", status.Stats.PageCount)
		fmt.Printf("页面大小: %d bytes\n", status.Stats.PageSize)
		fmt.Printf("数据库大小: %.2f MB\n", float64(status.Stats.DatabaseSize)/(1024*1024))
		fmt.Printf("空闲页面: %d\n", status.Stats.FreePages)
		fmt.Printf("WAL 大小: %.2f KB\n", float64(status.Stats.WALSize)/1024)

		fmt.Println("\n--- 表统计 ---")
		for _, table := range status.Stats.Tables {
			fmt.Printf("表 %s: %d 条记录\n", table.Name, table.RecordCount)
		}
	}

	// 索引报告已移除，用户自行维护索引

	return nil
}

// ExportStatus 导出状态为 JSON
func (dm *DatabaseManager) ExportStatus(filePath string) error {
	status, err := dm.Status()
	if err != nil {
		return err
	}

	data, err := json.MarshalIndent(status, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化状态信息失败: %w", err)
	}

	// 这里需要文件写入操作
	log.Printf("状态信息已准备好，需要写入到: %s", filePath)
	log.Printf("JSON 数据长度: %d bytes", len(data))

	// 实际的文件写入需要在调用方实现
	return fmt.Errorf("文件写入功能需要应用层支持")
}

// Health 健康检查
func (dm *DatabaseManager) Health() error {
	// 检查数据库连接
	sqlDB, err := dm.db.DB()
	if err != nil {
		return fmt.Errorf("数据库连接不可用: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("数据库 ping 失败: %w", err)
	}

	// 执行简单查询测试
	var result int
	if err := dm.db.Raw("SELECT 1").Scan(&result).Error; err != nil {
		return fmt.Errorf("数据库查询测试失败: %w", err)
	}

	if result != 1 {
		return fmt.Errorf("数据库查询结果异常: 期望 1，得到 %d", result)
	}

	return nil
}
