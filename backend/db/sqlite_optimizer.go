package db

import (
	"fmt"
	"log"
	"time"

	"gorm.io/gorm"
)

// SQLiteOptimizer SQLite 优化器
type SQLiteOptimizer struct {
	db *gorm.DB
}

// NewSQLiteOptimizer 创建 SQLite 优化器
func NewSQLiteOptimizer(db *gorm.DB) *SQLiteOptimizer {
	return &SQLiteOptimizer{db: db}
}

// OptimizeSettings 优化 SQLite 设置
func (o *SQLiteOptimizer) OptimizeSettings() error {
	log.Println("开始优化 SQLite 配置...")

	// 验证数据库连接可用性
	if _, err := o.db.DB(); err != nil {
		return fmt.Errorf("获取数据库连接失败: %w", err)
	}

	// 执行 PRAGMA 优化设置
	pragmas := []struct {
		name        string
		value       string
		description string
	}{
		// 启用 WAL 模式，提高并发性能
		{"journal_mode", "WAL", "启用 Write-Ahead Logging 模式"},

		// 设置同步模式为 NORMAL，平衡性能和安全性
		{"synchronous", "NORMAL", "设置同步模式为 NORMAL"},

		// 设置缓存大小（负数表示 KB，这里设置为 64MB）
		{"cache_size", "-65536", "设置缓存大小为 64MB"},

		// 设置临时存储为内存模式
		{"temp_store", "MEMORY", "设置临时存储为内存模式"},

		// 设置 mmap 大小（256MB）
		{"mmap_size", "268435456", "设置内存映射大小为 256MB"},

		// 启用外键约束
		{"foreign_keys", "ON", "启用外键约束"},

		// 优化页面大小（4KB，适合大多数现代系统）
		{"page_size", "4096", "设置页面大小为 4KB"},

		// 设置自动 vacuum 模式为增量模式
		{"auto_vacuum", "INCREMENTAL", "启用增量自动清理"},

		// 设置检查点自动触发阈值
		{"wal_autocheckpoint", "1000", "设置 WAL 自动检查点阈值"},

		// 启用查询规划器稳定性
		{"query_only", "OFF", "禁用只读模式"},

		// 设置忙等待超时
		{"busy_timeout", "5000", "设置忙等待超时为 5 秒"},
	}

	for _, pragma := range pragmas {
		sql := fmt.Sprintf("PRAGMA %s = %s", pragma.name, pragma.value)

		if err := o.db.Exec(sql).Error; err != nil {
			log.Printf("警告: 设置 PRAGMA %s 失败: %v", pragma.name, err)
			continue
		}

		log.Printf("✓ %s: %s", pragma.description, pragma.value)
	}

	// 验证关键设置
	if err := o.verifySettings(); err != nil {
		log.Printf("警告: 验证设置时出现问题: %v", err)
	}

	log.Println("SQLite 配置优化完成")
	return nil
}

// verifySettings 验证关键设置是否生效
func (o *SQLiteOptimizer) verifySettings() error {
	// 验证 WAL 模式
	var journalMode string
	if err := o.db.Raw("PRAGMA journal_mode").Scan(&journalMode).Error; err != nil {
		return fmt.Errorf("获取 journal_mode 失败: %w", err)
	}
	log.Printf("当前 journal_mode: %s", journalMode)

	// 验证外键约束
	var foreignKeys int
	if err := o.db.Raw("PRAGMA foreign_keys").Scan(&foreignKeys).Error; err != nil {
		return fmt.Errorf("获取 foreign_keys 失败: %w", err)
	}
	log.Printf("外键约束状态: %d", foreignKeys)

	// 验证缓存大小
	var cacheSize int
	if err := o.db.Raw("PRAGMA cache_size").Scan(&cacheSize).Error; err != nil {
		return fmt.Errorf("获取 cache_size 失败: %w", err)
	}
	log.Printf("当前缓存大小: %d", cacheSize)

	return nil
}

// PerformMaintenance 执行数据库维护操作
func (o *SQLiteOptimizer) PerformMaintenance() error {
	log.Println("开始执行数据库维护...")

	// 执行 ANALYZE 优化查询规划器
	if err := o.db.Exec("ANALYZE").Error; err != nil {
		log.Printf("警告: ANALYZE 执行失败: %v", err)
	} else {
		log.Println("✓ ANALYZE 执行完成，查询规划器已优化")
	}

	// 执行增量 VACUUM
	if err := o.db.Exec("PRAGMA incremental_vacuum").Error; err != nil {
		log.Printf("警告: 增量 VACUUM 执行失败: %v", err)
	} else {
		log.Println("✓ 增量 VACUUM 执行完成")
	}

	// WAL 检查点
	if err := o.db.Exec("PRAGMA wal_checkpoint(TRUNCATE)").Error; err != nil {
		log.Printf("警告: WAL 检查点执行失败: %v", err)
	} else {
		log.Println("✓ WAL 检查点执行完成")
	}

	log.Println("数据库维护完成")
	return nil
}

// GetDatabaseStats 获取数据库统计信息
func (o *SQLiteOptimizer) GetDatabaseStats() (*DatabaseStats, error) {
	stats := &DatabaseStats{}

	// 获取页面数量和页面大小
	if err := o.db.Raw("PRAGMA page_count").Scan(&stats.PageCount).Error; err != nil {
		return nil, fmt.Errorf("获取页面数量失败: %w", err)
	}

	if err := o.db.Raw("PRAGMA page_size").Scan(&stats.PageSize).Error; err != nil {
		return nil, fmt.Errorf("获取页面大小失败: %w", err)
	}

	// 计算数据库大小
	stats.DatabaseSize = int64(stats.PageCount * stats.PageSize)

	// 获取空闲页面数量
	if err := o.db.Raw("PRAGMA freelist_count").Scan(&stats.FreePages).Error; err != nil {
		return nil, fmt.Errorf("获取空闲页面数量失败: %w", err)
	}

	// 获取 WAL 文件大小
	var walSize int64
	if err := o.db.Raw("PRAGMA wal_size").Scan(&walSize).Error; err != nil {
		log.Printf("警告: 获取 WAL 大小失败: %v", err)
		walSize = 0
	}
	stats.WALSize = walSize

	// 获取表信息
	tables, err := o.getTableStats()
	if err != nil {
		return nil, fmt.Errorf("获取表统计信息失败: %w", err)
	}
	stats.Tables = tables

	return stats, nil
}

// getTableStats 获取表统计信息
func (o *SQLiteOptimizer) getTableStats() ([]TableStats, error) {
	var tableNames []string
	if err := o.db.Raw("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'").Scan(&tableNames).Error; err != nil {
		return nil, err
	}

	var tables []TableStats
	for _, tableName := range tableNames {
		var count int64
		if err := o.db.Table(tableName).Count(&count).Error; err != nil {
			log.Printf("警告: 获取表 %s 记录数失败: %v", tableName, err)
			continue
		}

		tables = append(tables, TableStats{
			Name:        tableName,
			RecordCount: count,
		})
	}

	return tables, nil
}

// DatabaseStats 数据库统计信息
type DatabaseStats struct {
	PageCount    int          `json:"page_count"`
	PageSize     int          `json:"page_size"`
	DatabaseSize int64        `json:"database_size"`
	FreePages    int          `json:"free_pages"`
	WALSize      int64        `json:"wal_size"`
	Tables       []TableStats `json:"tables"`
}

// TableStats 表统计信息
type TableStats struct {
	Name        string `json:"name"`
	RecordCount int64  `json:"record_count"`
}

// ScheduledMaintenance 定期维护任务
func (o *SQLiteOptimizer) ScheduledMaintenance() {
	ticker := time.NewTicker(24 * time.Hour) // 每24小时执行一次
	defer ticker.Stop()

	for range ticker.C {
		log.Println("执行定期数据库维护...")
		if err := o.PerformMaintenance(); err != nil {
			log.Printf("定期维护失败: %v", err)
		}
	}
}

// OptimizeForProduction 生产环境优化
func (o *SQLiteOptimizer) OptimizeForProduction() error {
	log.Println("应用生产环境优化配置...")

	// 生产环境专用设置
	productionPragmas := []struct {
		name  string
		value string
	}{
		// 更保守的同步设置，确保数据安全
		{"synchronous", "FULL"},

		// 增加缓存大小以提高性能
		{"cache_size", "-131072"}, // 128MB

		// 减少 WAL 检查点频率，提高写入性能
		{"wal_autocheckpoint", "2000"},

		// 启用优化器
		{"optimize", "0x10002"},
	}

	for _, pragma := range productionPragmas {
		sql := fmt.Sprintf("PRAGMA %s = %s", pragma.name, pragma.value)
		if err := o.db.Exec(sql).Error; err != nil {
			log.Printf("警告: 设置生产环境 PRAGMA %s 失败: %v", pragma.name, err)
		}
	}

	log.Println("生产环境优化配置完成")
	return nil
}
