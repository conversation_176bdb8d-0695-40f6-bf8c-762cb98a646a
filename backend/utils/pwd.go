package utils

import (
	"golang.org/x/crypto/bcrypt"
)

// HashPassword 创建密码的安全哈希
func HashPassword(password string) (string, error) {
	// 使用bcrypt算法哈希密码
	// 成本参数10是一个平衡安全性和性能的合理值
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}

	return string(hashedBytes), nil
}

// VerifyPassword 验证密码是否匹配存储的哈希
func VerifyPassword(password, hashedPassword string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	return err == nil
}
