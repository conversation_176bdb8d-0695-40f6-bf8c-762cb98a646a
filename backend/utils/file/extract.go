package file

import (
	"archive/tar"
	"archive/zip"
	"compress/gzip"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"github.com/ulikunitz/xz"
)

// ExtractFile 通用文件解压函数，自动检测压缩格式
func ExtractFile(src, dest string) error {
	if strings.HasSuffix(strings.ToLower(src), ".zip") {
		_, err := ExtractZip(src, dest)
		return err
	} else if strings.HasSuffix(strings.ToLower(src), ".tar.gz") || strings.HasSuffix(strings.ToLower(src), ".tgz") {
		_, err := ExtractTarGz(src, dest)
		return err
	} else if strings.HasSuffix(strings.ToLower(src), ".tar.xz") {
		_, err := ExtractTarXz(src, dest)
		return err
	} else if strings.HasSuffix(strings.ToLower(src), ".tar") {
		_, err := ExtractTar(src, dest)
		return err
	} else if strings.HasSuffix(strings.ToLower(src), ".gz") {
		_, err := ExtractGz(src, dest)
		return err
	}
	return fmt.Errorf("不支持的压缩格式: %s", src)
}

// ExtractZip 解压 ZIP 文件，返回解压后的目录路径
func ExtractZip(src, dest string) (string, error) {
	r, err := zip.OpenReader(src)
	if err != nil {
		return "", err
	}
	defer r.Close()

	// 创建目标目录
	extractDir := filepath.Join(dest, strings.TrimSuffix(filepath.Base(src), filepath.Ext(src)))
	if err := os.MkdirAll(extractDir, 0755); err != nil {
		return "", err
	}

	// 解压文件
	for _, f := range r.File {
		rc, err := f.Open()
		if err != nil {
			return "", err
		}

		path := filepath.Join(extractDir, f.Name)

		// 检查路径安全性（防止zip slip漏洞）
		if !strings.HasPrefix(path, filepath.Clean(extractDir)+string(os.PathSeparator)) {
			rc.Close()
			return "", fmt.Errorf("非法的文件路径: %s", f.Name)
		}

		if f.FileInfo().IsDir() {
			os.MkdirAll(path, f.FileInfo().Mode())
			rc.Close()
			continue
		}

		// 创建文件的目录
		if err := os.MkdirAll(filepath.Dir(path), 0755); err != nil {
			rc.Close()
			return "", err
		}

		// 创建文件
		outFile, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.FileInfo().Mode())
		if err != nil {
			rc.Close()
			return "", err
		}

		_, err = io.Copy(outFile, rc)
		outFile.Close()
		rc.Close()

		if err != nil {
			return "", err
		}
	}

	return extractDir, nil
}

// ExtractTarGz 解压 tar.gz 文件，返回解压后的目录路径
func ExtractTarGz(src, dest string) (string, error) {
	file, err := os.Open(src)
	if err != nil {
		return "", err
	}
	defer file.Close()

	gr, err := gzip.NewReader(file)
	if err != nil {
		return "", err
	}
	defer gr.Close()

	return extractTar(gr, src, dest)
}

// ExtractTar 解压 tar 文件，返回解压后的目录路径
func ExtractTar(src, dest string) (string, error) {
	file, err := os.Open(src)
	if err != nil {
		return "", err
	}
	defer file.Close()

	return extractTar(file, src, dest)
}

// ExtractGz 解压 .gz 文件，返回解压后的文件路径
func ExtractGz(src, dest string) (string, error) {
	file, err := os.Open(src)
	if err != nil {
		return "", err
	}
	defer file.Close()

	gr, err := gzip.NewReader(file)
	if err != nil {
		return "", err
	}
	defer gr.Close()

	// 创建输出文件
	baseName := filepath.Base(src)
	if strings.HasSuffix(strings.ToLower(baseName), ".gz") {
		baseName = baseName[:len(baseName)-3]
	}

	outputPath := filepath.Join(dest, baseName)
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return "", err
	}
	defer outputFile.Close()

	_, err = io.Copy(outputFile, gr)
	if err != nil {
		return "", err
	}

	return outputPath, nil
}

// ExtractTarXz 解压 tar.xz 文件，返回解压后的目录路径
func ExtractTarXz(src, dest string) (string, error) {
	file, err := os.Open(src)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 创建xz解压器
	xzr, err := xz.NewReader(file)
	if err != nil {
		return "", err
	}

	return extractTar(xzr, src, dest)
}

// extractTar 通用 tar 解压函数
func extractTar(reader io.Reader, src, dest string) (string, error) {
	tr := tar.NewReader(reader)

	// 创建目标目录
	extractDir := filepath.Join(dest, strings.TrimSuffix(filepath.Base(src), filepath.Ext(src)))
	if err := os.MkdirAll(extractDir, 0755); err != nil {
		return "", err
	}

	for {
		header, err := tr.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return "", err
		}

		path := filepath.Join(extractDir, header.Name)

		// 检查路径安全性（防止tar slip漏洞）
		if !strings.HasPrefix(path, filepath.Clean(extractDir)+string(os.PathSeparator)) {
			return "", fmt.Errorf("非法的文件路径: %s", header.Name)
		}

		switch header.Typeflag {
		case tar.TypeDir:
			if err := os.MkdirAll(path, os.FileMode(header.Mode)); err != nil {
				return "", err
			}
		case tar.TypeReg:
			// 创建文件的目录
			if err := os.MkdirAll(filepath.Dir(path), 0755); err != nil {
				return "", err
			}

			// 创建文件
			outFile, err := os.Create(path)
			if err != nil {
				return "", err
			}

			if _, err := io.Copy(outFile, tr); err != nil {
				outFile.Close()
				return "", err
			}

			outFile.Close()

			// 设置文件权限
			if err := os.Chmod(path, os.FileMode(header.Mode)); err != nil {
				return "", err
			}
		}
	}

	return extractDir, nil
}
