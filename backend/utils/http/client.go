package http

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// Client 是HTTP客户端的封装
type Client struct {
	client  *http.Client
	baseURL string
	headers map[string]string
	proxy   string
}

// ProgressWriter 是一个支持进度显示的Writer
type ProgressWriter struct {
	Total      int64
	Current    int64
	Writer     io.Writer
	OnProgress func(current, total int64)
}

// Write 实现io.Writer接口
func (pw *ProgressWriter) Write(p []byte) (n int, err error) {
	n, err = pw.Writer.Write(p)
	pw.Current += int64(n)
	if pw.OnProgress != nil {
		pw.OnProgress(pw.Current, pw.Total)
	}
	return
}

// NewClient 创建一个新的HTTP客户端，使用默认配置
func NewClient() *Client {
	client := &Client{
		headers: make(map[string]string),
	}

	// 创建默认transport和client
	transport := &http.Transport{}

	client.client = &http.Client{
		Timeout:   30 * time.Second,
		Transport: transport,
	}

	// 默认设置常用请求头
	client.setDefaultHeaders()

	return client
}

// WithBaseURL 设置基础URL
func (c *Client) WithBaseURL(baseURL string) *Client {
	c.baseURL = baseURL
	return c
}

// WithTimeout 设置超时时间
func (c *Client) WithTimeout(timeout time.Duration) *Client {
	c.client.Timeout = timeout
	return c
}

// WithProxy 设置代理
func (c *Client) WithProxy(proxyURL string) *Client {
	c.proxy = proxyURL

	// 创建新的Transport
	transport := &http.Transport{}

	// 设置代理
	if proxyURL != "" {
		parsedURL, err := url.Parse(proxyURL)
		if err == nil {
			transport.Proxy = http.ProxyURL(parsedURL)
		}
	}

	// 更新客户端的Transport
	c.client.Transport = transport
	return c
}

// WithHeader 设置单个请求头
func (c *Client) WithHeader(key, value string) *Client {
	c.headers[key] = value
	return c
}

// WithHeaders 批量设置请求头
func (c *Client) WithHeaders(headers map[string]string) *Client {
	for key, value := range headers {
		c.headers[key] = value
	}
	return c
}

// setDefaultHeaders 设置常用的默认请求头
func (c *Client) setDefaultHeaders() *Client {
	c.headers["User-Agent"] = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
	c.headers["Accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
	c.headers["Accept-Language"] = "en-US,en;q=0.9"
	c.headers["Connection"] = "keep-alive"
	c.headers["Cache-Control"] = "max-age=0"
	return c
}

// applyHeaders 将请求头应用到请求上
func (c *Client) applyHeaders(req *http.Request) {
	for key, value := range c.headers {
		req.Header.Set(key, value)
	}
}

// buildURL 构建完整的URL
func (c *Client) buildURL(path string) string {
	// 如果path已经是完整URL（以http://或https://开头），则直接返回
	if strings.HasPrefix(path, "http://") || strings.HasPrefix(path, "https://") {
		return path
	}

	// 否则拼接baseURL
	if c.baseURL == "" {
		return path
	}
	return c.baseURL + path
}

// Get 发送GET请求（简化版无需传入context）
func (c *Client) Get(path string) (*http.Response, error) {
	return c.GetWithContext(context.Background(), path)
}

// GetWithContext 发送GET请求（带context）
func (c *Client) GetWithContext(ctx context.Context, path string) (*http.Response, error) {
	url := c.buildURL(path)
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}
	c.applyHeaders(req)
	return c.client.Do(req)
}

// Post 发送POST请求（简化版无需传入context）
func (c *Client) Post(path string, contentType string, body io.Reader) (*http.Response, error) {
	return c.PostWithContext(context.Background(), path, contentType, body)
}

// PostWithContext 发送POST请求（带context）
func (c *Client) PostWithContext(ctx context.Context, path string, contentType string, body io.Reader) (*http.Response, error) {
	url := c.buildURL(path)
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, body)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", contentType)
	c.applyHeaders(req)
	return c.client.Do(req)
}

// PostJSON 发送JSON格式的POST请求（简化版无需传入context）
func (c *Client) PostJSON(path string, data interface{}) (*http.Response, error) {
	return c.PostJSONWithContext(context.Background(), path, data)
}

// PostJSONWithContext 发送JSON格式的POST请求（带context）
func (c *Client) PostJSONWithContext(ctx context.Context, path string, data interface{}) (*http.Response, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	return c.PostWithContext(ctx, path, "application/json", bytes.NewBuffer(jsonData))
}

// Put 发送PUT请求（简化版无需传入context）
func (c *Client) Put(path string, contentType string, body io.Reader) (*http.Response, error) {
	return c.PutWithContext(context.Background(), path, contentType, body)
}

// PutWithContext 发送PUT请求（带context）
func (c *Client) PutWithContext(ctx context.Context, path string, contentType string, body io.Reader) (*http.Response, error) {
	url := c.buildURL(path)
	req, err := http.NewRequestWithContext(ctx, http.MethodPut, url, body)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", contentType)
	c.applyHeaders(req)
	return c.client.Do(req)
}

// Delete 发送DELETE请求（简化版无需传入context）
func (c *Client) Delete(path string) (*http.Response, error) {
	return c.DeleteWithContext(context.Background(), path)
}

// DeleteWithContext 发送DELETE请求（带context）
func (c *Client) DeleteWithContext(ctx context.Context, path string) (*http.Response, error) {
	url := c.buildURL(path)
	req, err := http.NewRequestWithContext(ctx, http.MethodDelete, url, nil)
	if err != nil {
		return nil, err
	}
	c.applyHeaders(req)
	return c.client.Do(req)
}

// GetString 获取响应内容为字符串（简化版无需传入context）
func (c *Client) GetBytes(path string) ([]byte, error) {
	return c.GetStringWithContext(context.Background(), path)
}

// GetStringWithContext 获取响应内容为字符串（带context）
func (c *Client) GetStringWithContext(ctx context.Context, path string) ([]byte, error) {
	resp, err := c.GetWithContext(ctx, path)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP请求失败，状态码: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	return body, nil
}

// GetJSON 获取响应内容为JSON并解析（简化版无需传入context）
func (c *Client) GetJSON(path string, v interface{}) error {
	return c.GetJSONWithContext(context.Background(), path, v)
}

// GetJSONWithContext 获取响应内容为JSON并解析（带context）
func (c *Client) GetJSONWithContext(ctx context.Context, path string, v interface{}) error {
	resp, err := c.GetWithContext(ctx, path)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP请求失败，状态码: %d", resp.StatusCode)
	}

	return json.NewDecoder(resp.Body).Decode(v)
}

// DownloadFile 下载文件（简化版无需传入context）
func (c *Client) DownloadFile(url, destPath string, onProgress func(current, total int64)) error {
	return c.DownloadFileWithContext(context.Background(), url, destPath, onProgress)
}

// DownloadFileWithContext 下载文件（带context）
func (c *Client) DownloadFileWithContext(ctx context.Context, url, destPath string, onProgress func(current, total int64)) error {
	// 创建目录
	if err := os.MkdirAll(filepath.Dir(destPath), 0755); err != nil {
		return err
	}

	// 创建临时文件
	tmpFile := destPath + ".download"
	file, err := os.Create(tmpFile)
	if err != nil {
		return err
	}
	defer file.Close()

	// 发送请求
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return err
	}
	c.applyHeaders(req)
	resp, err := c.client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载文件失败，状态码: %d", resp.StatusCode)
	}

	// 支持进度显示
	var reader io.Reader = resp.Body
	if onProgress != nil {
		progressWriter := &ProgressWriter{
			Total:      resp.ContentLength,
			Writer:     file,
			OnProgress: onProgress,
		}
		_, err = io.Copy(progressWriter, reader)
	} else {
		_, err = io.Copy(file, reader)
	}

	if err != nil {
		return err
	}

	// 关闭文件
	if err = file.Close(); err != nil {
		return err
	}

	// 重命名文件
	return os.Rename(tmpFile, destPath)
}

// GetWithRetry 带重试的GET请求（简化版无需传入context）
func (c *Client) GetWithRetry(path string, retries int, retryDelay time.Duration) (*http.Response, error) {
	return c.GetWithRetryContext(context.Background(), path, retries, retryDelay)
}

// GetWithRetryContext 带重试的GET请求（带context）
func (c *Client) GetWithRetryContext(ctx context.Context, path string, retries int, retryDelay time.Duration) (*http.Response, error) {
	var resp *http.Response
	var err error

	for i := 0; i <= retries; i++ {
		resp, err = c.GetWithContext(ctx, path)
		if err == nil && resp.StatusCode < 500 {
			return resp, nil
		}

		if resp != nil {
			resp.Body.Close()
		}

		if i < retries {
			select {
			case <-time.After(retryDelay):
			case <-ctx.Done():
				return nil, ctx.Err()
			}
		}
	}

	return nil, fmt.Errorf("在%d次重试后请求失败: %v", retries, err)
}
