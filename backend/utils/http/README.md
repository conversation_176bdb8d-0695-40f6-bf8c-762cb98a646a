# HTTP 工具类

这个包提供了 HTTP 请求的封装，简化了常见的 HTTP 操作，包括基本请求、JSON 处理和文件下载等功能。

## 功能特点

- 支持常见的 HTTP 方法（GET, POST, PUT, DELETE 等）
- 支持 JSON 请求和响应的处理
- 支持文件下载（带进度显示）
- 支持请求重试机制
- 支持 HTTP 代理配置
- 链式调用 API
- 简化的 API（无需传入 Context）

## 基本用法

### 创建 HTTP 客户端

```go
import (
    "time"
    httputil "a8.tools/backend/utils/http"
)

// 创建默认客户端
client := httputil.NewClient()

// 使用链式调用设置参数
client := httputil.NewClient().
    WithBaseURL("https://api.example.com").
    WithTimeout(30 * time.Second).
    WithProxy("http://localhost:7890").
    WithHeader("Authorization", "Bearer token123")

// 批量设置请求头
headers := map[string]string{
    "X-API-Key": "your-api-key",
    "X-Client-ID": "client-123",
}
client.WithHeaders(headers)

// 兼容旧版的创建方式（已废弃）
client := httputil.NewDefaultClient("https://api.example.com", 30*time.Second)
```

### 发送 GET 请求

```go
// 基本GET请求（无需传入Context）
resp, err := client.Get("/users")
if err != nil {
    // 处理错误
}
defer resp.Body.Close()

// 获取响应内容为字符串
content, err := client.GetString("/users")
if err != nil {
    // 处理错误
}

// 将响应解析为JSON
var users []User
err := client.GetJSON("/users", &users)
if err != nil {
    // 处理错误
}

// 使用完整URL发送请求（当创建client时没有指定baseURL或需要请求其他域名时）
content, err := client.GetString("https://api.another.com/api/data")

// 需要自定义Context的高级用法
ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
defer cancel()
resp, err := client.GetWithContext(ctx, "/users")
```

### 发送 POST 请求

```go
// 普通POST请求
resp, err := client.Post("/users", "application/x-www-form-urlencoded", strings.NewReader("name=test&age=25"))

// 发送JSON数据
data := map[string]interface{}{
    "name": "test",
    "age": 25,
}
resp, err := client.PostJSON("/users", data)
```

### 文件下载

```go
// 基本下载
err := client.DownloadFile("https://example.com/file.zip", "/path/to/save/file.zip", nil)

// 带进度显示的下载
err := client.DownloadFile("https://example.com/file.zip", "/path/to/save/file.zip",
    func(current, total int64) {
        progress := float64(current) / float64(total) * 100
        fmt.Printf("下载进度: %.2f%%\n", progress)
    })
```

### 带重试的请求

```go
// 最多重试3次，每次重试间隔2秒
resp, err := client.GetWithRetry("/users", 3, 2*time.Second)
```

## 代理支持

客户端支持以下代理协议：

- HTTP 代理: `http://host:port`
- HTTPS 代理: `https://host:port`
- SOCKS5 代理: `socks5://host:port`

## 完整示例

```go
package main

import (
    "fmt"
    "time"
    httputil "a8.tools/backend/utils/http"
)

func main() {
    // 创建客户端并配置参数
    client := httputil.NewClient().
        WithBaseURL("https://api.example.com").
        WithTimeout(30 * time.Second).
        WithHeader("X-API-Key", "your-api-key")

    // 发送GET请求
    content, err := client.GetString("/api/data")
    if err != nil {
        fmt.Printf("请求失败: %v\n", err)
        return
    }

    fmt.Println("响应内容:", content)

    // 下载文件
    err = client.DownloadFile("https://example.com/file.zip", "./downloads/file.zip",
        func(current, total int64) {
            if total > 0 {
                progress := float64(current) / float64(total) * 100
                fmt.Printf("\r下载进度: %.2f%%", progress)
            } else {
                fmt.Printf("\r已下载: %d 字节", current)
            }
        })

    if err != nil {
        fmt.Printf("\n下载失败: %v\n", err)
        return
    }

    fmt.Println("\n下载完成!")
}
```
