.idea/
.task
bin
frontend/dist
frontend/node_modules
frontend/bindings/*
build/linux/appimage/build
build/windows/nsis/MicrosoftEdgeWebview2Setup.exe

# Added by Task Master AI
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Dependency directories
node_modules/

# Environment variables
.env

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store

.specstory/
.claude/

.taskmaster/

# Task files
tasks.json
tasks/ 
