# A8 Tools

## 📖 项目概述

A8 Tools 是一个使用 Wails v3 和 React 构建的跨平台桌面应用，旨在为加密货币空投猎人提供一个高效、集成的管理工具集。它整合了加密货币钱包管理、社交账户管理（如邮箱、X/Twitter、Discord）、代理服务以及强大的浏览器自动化功能，以简化和自动化空投任务流程。

## ✨ 主要功能

*   **多账户管理**: 集中管理您的空投所需账户：
    *   📧 **邮箱账户**
    *   🐦 **X (Twitter) 账户**
    *   👾 **Discord 账户**
    *   🌐 **Telegram 账户**
*   **加密钱包管理**:
    *   安全地创建、导入和管理多种区块链钱包。
    *   支持 Bitcoin, Ethereum, Solana, Aptos, Sui 等多种主流公链。
*   **安全密码存储**:
    *   利用系统级的密钥环 (Keyring) 安全地存储和管理您的敏感密码和私钥。
*   **代理服务**:
    *   内置代理管理功能，方便您在执行任务时切换网络环境。
*   **浏览器自动化**:
    *   基于 `go-rod` 实现，可编写和执行自动化脚本，完成复杂的 Web 交互任务。

## 🛠️ 技术栈

本应用采用现代化的技术栈，以确保高性能、安全性和良好的开发体验。

### **后端 (Backend)**

*   **框架**: [Go](https://golang.org/) + [Wails v3](https://wails.io/)
*   **浏览器自动化**: [go-rod](https://github.com/go-rod/rod)
*   **数据库 ORM**: [GORM](https://gorm.io/) (with MySQL driver)
*   **安全存储**: [go-keyring](https://github.com/zalando/go-keyring)
*   **依赖注入**: [Uber Fx](https://github.com/uber-go/fx)

### **前端 (Frontend)**

*   **框架**: [React](https://reactjs.org/)
*   **构建工具**: [Vite](https://vitejs.dev/)
*   **UI 组件库**: [Material-UI (MUI)](https://mui.com/)
*   **图表**: [ApexCharts](https://apexcharts.com/)
*   **富文本编辑**: [Tiptap](https://tiptap.dev/)
*   **Web3/加密**: [@okxweb3/coin-*](https://github.com/okx/js-wallet)
*   **状态管理/API 通信**: Wails Runtime Bridge

## 📂 项目结构

```
.
├── backend/         # Go 后端代码
├── frontend/        # React 前端代码
│   ├── src/         # 前端源码
│   └── package.json # 前端依赖
├── main.go          # 应用主入口
├── go.mod           # Go 模块依赖
└── README.md        # 就是你现在看到的这个文件
```

## 🚀 开发入门

### **环境要求**

1.  **Go**: 版本 1.23+
2.  **Node.js**: 版本 20+
3.  **Wails CLI**: [安装 Wails CLI](https://wails.io/docs/getting-started/installation)

### **安装与运行**

1.  **克隆仓库**:
    ```bash
    git clone <your-repo-url>
    cd desktop
    ```

2.  **安装前端依赖**:
    ```bash
    cd frontend
    yarn install
    cd ..
    ```

3.  **运行应用**:
    在项目根目录下运行以下命令，Wails 会自动编译和启动应用：
    ```bash
    wails3 dev
    ```

4.  **构建应用**:
    ```bash
    wails3 build
    ```
    构建产物将位于 `build/bin` 目录下。
