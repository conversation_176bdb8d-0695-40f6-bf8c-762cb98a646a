import i18next from 'i18next';
import { getStorage } from 'minimal-shared/utils';
import resourcesToBackend from 'i18next-resources-to-backend';
import LanguageDetector from 'i18next-browser-languagedetector';
import { initReactI18next, I18nextProvider as Provider } from 'react-i18next';

import {
  i18nOptions,
  fallbackLng,
  detectSystemLanguage,
  detectSystemLanguageSync,
} from './locales-config';

// ----------------------------------------------------------------------

/**
 * 获取项目中可用的命名空间
 * 使用手动维护的列表以确保兼容性
 */
function getAvailableNamespaces() {
  return ['common', 'proxy', 'wallet', 'account', 'navbar', 'settings', 'init'];
}

/**
 * 获取初始语言设置（同步版本）
 * 优先级：localStorage 中的设置 > 同步检测的浏览器语言 > 默认语言
 */
function getInitialLanguageSync() {
  // 检查 localStorage 中是否有用户手动设置的语言
  const storedLang = getStorage('i18nextLng');

  if (storedLang && storedLang !== fallbackLng) {
    return storedLang;
  }

  // 如果没有存储的语言设置，则同步检测浏览器语言
  const detectedLang = detectSystemLanguageSync();

  // 如果检测到的语言不是默认语言，则保存到 localStorage
  if (detectedLang !== fallbackLng) {
    try {
      localStorage.setItem('i18nextLng', detectedLang);
    } catch (error) {
      console.warn('⚠️ Failed to save language preference to localStorage:', error);
    }
  }

  return detectedLang;
}

/**
 * 异步完善语言设置
 * 在 Wails 环境中尝试获取更准确的系统语言设置
 */
async function enhanceLanguageDetection() {
  try {
    const storedLang = getStorage('i18nextLng');

    // 如果用户已经手动设置过语言，则不进行异步检测
    if (storedLang && storedLang !== fallbackLng) {
      return;
    }

    // 异步检测系统语言（包括 Wails API）
    const detectedLang = await detectSystemLanguage();
    const currentLang = i18next.language;

    // 如果检测到的语言与当前语言不同，则更新
    if (detectedLang !== currentLang) {
      // 更新 i18next 语言设置
      await i18next.changeLanguage(detectedLang);

      // 保存到 localStorage
      try {
        localStorage.setItem('i18nextLng', detectedLang);
      } catch (error) {
        console.warn('⚠️ Failed to update language preference in localStorage:', error);
      }
    }
  } catch (error) {
    console.warn('⚠️ Failed to enhance language detection:', error);
  }
}

// 获取初始语言（同步）
const lng = getInitialLanguageSync();

// 获取可用的命名空间
const availableNamespaces = getAvailableNamespaces();

// 初始化 i18next
i18next
  .use(LanguageDetector)
  .use(initReactI18next)
  .use(
    resourcesToBackend((lang, ns) =>
      import(`./langs/${lang}/${ns}.json`).catch((error) => {
        console.error(`Failed to load i18n resource: ${lang}/${ns}.json`, error);
        return {};
      })
    )
  )
  .init({
    ...i18nOptions(lng),
    detection: {
      // 配置语言检测选项
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
      excludeCacheFor: ['cimode'], // 开发模式下不缓存
    },
    // 使用配置的命名空间
    preload: ['en', 'cn'],
    ns: availableNamespaces,
    defaultNS: 'common',
    fallbackNS: 'common',
    debug: process.env.NODE_ENV === 'development',
    interpolation: {
      escapeValue: false, // React 已经处理了 XSS
    },
  })
  .then(() => {
    // i18next 初始化完成后，尝试异步完善语言检测
    enhanceLanguageDetection();
  })
  .catch((error) => {
    console.error('❌ Failed to initialize i18next:', error);
  });

// ----------------------------------------------------------------------

export function I18nProvider({ children }) {
  return <Provider i18n={i18next}>{children}</Provider>;
}
