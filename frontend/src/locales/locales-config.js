// ----------------------------------------------------------------------

export const fallbackLng = 'en';
export const languages = ['en', 'cn'];
export const defaultNS = 'common';

// 浏览器语言到应用语言的映射
const languageMapping = {
  zh: 'cn',
  'zh-CN': 'cn',
  'zh-cn': 'cn',
  'zh-Hans': 'cn',
  'zh-Hans-CN': 'cn',
  'zh-TW': 'cn',
  'zh-tw': 'cn',
  'zh-Hant': 'cn',
  'zh-Hant-TW': 'cn',
  'zh-HK': 'cn',
  'zh-hk': 'cn',
  'zh-MO': 'cn',
  'zh-mo': 'cn',
  en: 'en',
  'en-US': 'en',
  'en-us': 'en',
  'en-GB': 'en',
  'en-gb': 'en',
  'en-CA': 'en',
  'en-ca': 'en',
  'en-AU': 'en',
  'en-au': 'en',
};

/**
 * 检查是否在 Wails 环境中运行
 * @returns {boolean}
 */
function isWailsEnvironment() {
  return (
    typeof window !== 'undefined' &&
    typeof window._wails !== 'undefined' &&
    window._wails.environment
  );
}

/**
 * 从 Wails 系统环境获取语言信息
 * @returns {Promise<string|null>}
 */
async function getWailsSystemLanguage() {
  try {
    if (!isWailsEnvironment()) {
      return null;
    }

    // 尝试从 Wails 环境信息获取系统语言
    const environment = window._wails.environment;

    // 检查是否有系统语言信息
    if (environment && environment.Locale) {
      return environment.Locale;
    }

    // 尝试使用 Wails System API
    if (typeof window.runtime !== 'undefined' && window.runtime.System) {
      const systemInfo = await window.runtime.System.Environment();
      if (systemInfo && systemInfo.Locale) {
        return systemInfo.Locale;
      }
    }

    return null;
  } catch (error) {
    console.warn('⚠️ Failed to get Wails system language:', error);
    return null;
  }
}

/**
 * 从浏览器获取语言设置
 * @returns {string[]}
 */
function getBrowserLanguages() {
  const detectedLanguages = [];

  try {
    // 获取浏览器语言设置
    if (navigator.language) {
      detectedLanguages.push(navigator.language);
    }

    if (navigator.languages && Array.isArray(navigator.languages)) {
      detectedLanguages.push(...navigator.languages);
    }

    // 兼容旧版浏览器
    if (navigator.userLanguage) {
      detectedLanguages.push(navigator.userLanguage);
    }

    if (navigator.browserLanguage) {
      detectedLanguages.push(navigator.browserLanguage);
    }

    if (navigator.systemLanguage) {
      detectedLanguages.push(navigator.systemLanguage);
    }
  } catch (error) {
    console.warn('⚠️ Error getting browser languages:', error);
  }

  return [...new Set(detectedLanguages)]; // 去重
}

/**
 * 将检测到的语言映射到应用支持的语言
 * @param {string} detectedLang - 检测到的语言代码
 * @returns {string|null}
 */
function mapToSupportedLanguage(detectedLang) {
  if (!detectedLang) return null;

  // 直接匹配
  if (languageMapping[detectedLang]) {
    return languageMapping[detectedLang];
  }

  // 尝试匹配语言代码的前缀（如 'zh-CN' -> 'zh'）
  const langPrefix = detectedLang.split('-')[0];
  if (languageMapping[langPrefix]) {
    return languageMapping[langPrefix];
  }

  return null;
}

/**
 * 自动检测系统语言并映射到应用支持的语言
 * @returns {Promise<string>} 检测到的语言代码，如果不支持则返回 fallback 语言
 */
export async function detectSystemLanguage() {
  try {
    // 1. 首先尝试从 Wails 系统环境获取语言
    const wailsLang = await getWailsSystemLanguage();
    if (wailsLang) {
      const mappedLang = mapToSupportedLanguage(wailsLang);
      if (mappedLang) {
        return mappedLang;
      }
    }

    // 2. 从浏览器语言设置获取
    const browserLanguages = getBrowserLanguages();

    // 3. 尝试匹配浏览器语言到应用支持的语言
    for (const browserLang of browserLanguages) {
      if (!browserLang) continue;

      const mappedLang = mapToSupportedLanguage(browserLang);
      if (mappedLang) {
        return mappedLang;
      }
    }

    return fallbackLng;
  } catch (error) {
    console.error('❌ Error detecting system language:', error);
    return fallbackLng;
  }
}

/**
 * 同步版本的语言检测（仅浏览器）
 * 用于不支持异步的场景
 * @returns {string}
 */
export function detectSystemLanguageSync() {
  try {
    const browserLanguages = getBrowserLanguages();
    // 尝试匹配浏览器语言到应用支持的语言
    for (const browserLang of browserLanguages) {
      if (!browserLang) continue;

      const mappedLang = mapToSupportedLanguage(browserLang);
      if (mappedLang) {
        return mappedLang;
      }
    }

    return fallbackLng;
  } catch (error) {
    console.error('❌ Error detecting system language (sync):', error);
    return fallbackLng;
  }
}

// ----------------------------------------------------------------------

export function i18nOptions(lng = fallbackLng, ns = defaultNS) {
  return {
    // debug: true,
    lng,
    fallbackLng,
    ns,
    defaultNS,
    fallbackNS: defaultNS,
    supportedLngs: languages,
  };
}

// ----------------------------------------------------------------------

export const changeLangMessages = {
  en: {
    success: 'Language has been changed!',
    error: 'Error changing language!',
    loading: 'Loading...',
  },
  cn: {
    success: '语言已更改！',
    error: '更改语言时出错！',
    loading: '加载中...',
  },
};
