# 国际化 (i18n) 配置说明

## 项目结构

```
frontend/src/locales/
├── langs/                    # 语言文件目录
│   ├── en/                  # 英文翻译
│   │   ├── common.json      # 通用翻译
│   │   ├── wallet.json      # 钱包相关翻译
│   │   ├── account.json     # 账户相关翻译
│   │   └── ...
│   └── cn/                  # 中文翻译
│       ├── common.json      # 通用翻译
│       ├── wallet.json      # 钱包相关翻译
│       ├── account.json     # 账户相关翻译
│       └── ...
├── i18n-provider.jsx        # i18n 提供者组件
├── locales-config.js        # 本地化配置
├── use-locales.js          # 本地化 Hook
└── README.md               # 说明文档
```

## 自动语言检测

项目支持根据系统语言自动设置默认语言，检测优先级如下：

1. **用户手动设置的语言** (localStorage)
2. **Wails 系统语言** (仅在桌面应用中)
3. **浏览器语言设置** (navigator.language 等)
4. **默认语言** (English)

### 支持的语言映射

- 中文系列：`zh`, `zh-CN`, `zh-TW`, `zh-<PERSON>`, `zh-Hant` 等 → `cn`
- 英文系列：`en`, `en-US`, `en-GB`, `en-CA` 等 → `en`

### 语言检测功能

```jsx
import { useTranslate } from 'src/locales';

function MyComponent() {
  const { t, onChangeLang, onResetToSystemLang } = useTranslate();
  
  const handleResetLanguage = async () => {
    try {
      const systemLang = await onResetToSystemLang();
      console.log('Language reset to:', systemLang);
    } catch (error) {
      console.error('Failed to reset language:', error);
    }
  };
  
  return (
    <div>
      <button onClick={() => onChangeLang('en')}>
        {t('English')}
      </button>
      <button onClick={() => onChangeLang('cn')}>
        {t('中文')}
      </button>
      <button onClick={handleResetLanguage}>
        {t('Reset to System Language')}
      </button>
    </div>
  );
}
```

## 使用方法

### 1. 基本使用

```jsx
import { useTranslate } from 'src/locales';

function MyComponent() {
  const { t } = useTranslate();
  
  return (
    <div>
      {/* 使用默认命名空间 (common) */}
      <button>{t('action.save')}</button>
      
      {/* 使用指定命名空间 */}
      <h1>{t('wallet:title')}</h1>
      
      {/* 跨命名空间引用 */}
      <span>{t('common:action.edit')}</span>
    </div>
  );
}
```

### 2. 支持的命名空间

- `common` - 通用翻译（默认）
- `wallet` - 钱包相关
- `account` - 账户相关
- `team` - 团队相关
- `proxy` - 代理相关
- `navbar` - 导航栏相关
- `settings` - 设置相关
- `init` - 初始化相关

### 3. 语言切换

```jsx
import { useTranslate } from 'src/locales';

function LanguageSwitcher() {
  const { currentLang, onChangeLang, onResetToSystemLang } = useTranslate();
  
  return (
    <div>
      <p>Current: {currentLang.label}</p>
      
      {/* 手动切换语言 */}
      <button onClick={() => onChangeLang('en')}>English</button>
      <button onClick={() => onChangeLang('cn')}>中文</button>
      
      {/* 重置到系统语言 */}
      <button onClick={onResetToSystemLang}>
        Reset to System Language
      </button>
    </div>
  );
}
```

### 4. i18n Ally 插件

项目已配置 i18n Ally 插件支持：

- **自动检测**: 自动识别代码中的翻译键
- **内联显示**: 在代码中显示翻译内容
- **快速编辑**: 直接在编辑器中编辑翻译
- **缺失检测**: 检测缺失的翻译键
- **使用统计**: 显示翻译键的使用情况

### 5. 配置文件

- `.i18nrc.json` - i18n Ally 主配置文件
- `.vscode/settings.json` - VSCode 工作区设置
- `.vscode/extensions.json` - 推荐扩展列表

## 高级功能

### 自定义语言检测

```jsx
import { detectSystemLanguage, detectSystemLanguageSync } from 'src/locales/locales-config';

// 异步检测（推荐，支持 Wails API）
const systemLang = await detectSystemLanguage();

// 同步检测（仅浏览器 API）
const browserLang = detectSystemLanguageSync();
```

### Wails 桌面应用支持

在 Wails 桌面应用中，语言检测会尝试：

1. 从 `window._wails.environment.Locale` 获取系统语言
2. 调用 `window.runtime.System.Environment()` API
3. 回退到浏览器语言检测

## 添加新翻译

1. 在对应语言目录下的 JSON 文件中添加翻译键
2. 使用嵌套结构组织翻译键
3. 确保所有语言文件保持结构一致

## 注意事项

- 翻译键使用 camelCase 命名风格
- 支持嵌套结构，使用点号分隔
- 跨命名空间引用使用冒号分隔：`namespace:key`
- 建议保持所有语言文件的键结构一致
- 系统语言检测在应用启动时自动进行
- 用户手动设置的语言优先级最高 