// ----------------------------------------------------------------------

const ROOTS = {
  AUTH: '/auth',
  DASHBOARD: '/dashboard',
  WALLET: '/wallet',
  ACCOUNT: '/account',
  TASK: '/task',
  TEAM: '/team',
  TOOL: '/tool',
  LOCK: '/lock',
  PROXY: '/proxy',
  PROFILE: '/profile',
  SETTINGS: '/settings',
};

// ----------------------------------------------------------------------

export const paths = {
  page403: '/error/403',
  page404: '/error/404',
  page500: '/error/500',
  lock: ROOTS.LOCK,

  // AUTH
  auth: {
    jwt: {
      signIn: `${ROOTS.AUTH}/jwt/sign-in`,
      signUp: `${ROOTS.AUTH}/jwt/sign-up`,
    },
  },

  // DASHBOARD
  dashboard: {
    root: ROOTS.DASHBOARD,
  },
  wallet: {
    root: ROOTS.WALLET,
    add: `${ROOTS.WALLET}/add`,
    import: `${ROOTS.WALLET}/import`,
  },
  account: {
    root: ROOTS.ACCOUNT,
    mail: {
      root: `${ROOTS.ACCOUNT}/email`,
      edit: (id) => `${ROOTS.ACCOUNT}/email/${id}/edit`,
      details: (id) => `${ROOTS.ACCOUNT}/email/${id}`,
    },
    x: {
      root: `${ROOTS.ACCOUNT}/x`,
      edit: (id) => `${ROOTS.ACCOUNT}/x/${id}/edit`,
      details: (id) => `${ROOTS.ACCOUNT}/x/${id}`,
    },
    discord: {
      root: `${ROOTS.ACCOUNT}/discord`,
      edit: (id) => `${ROOTS.ACCOUNT}/discord/${id}/edit`,
      details: (id) => `${ROOTS.ACCOUNT}/discord/${id}`,
    },
    telegram: {
      root: `${ROOTS.ACCOUNT}/telegram`,
      edit: (id) => `${ROOTS.ACCOUNT}/telegram/${id}/edit`,
      details: (id) => `${ROOTS.ACCOUNT}/telegram/${id}`,
    },
  },
  proxy: {
    root: ROOTS.PROXY,
    add: `${ROOTS.PROXY}/add`,
  },
  task: {
    root: ROOTS.TASK,
  },
  tool: {
    root: ROOTS.TOOL,
  },
  profile: {
    root: ROOTS.PROFILE,
  },
  settings: {
    root: ROOTS.SETTINGS,
  },
};
