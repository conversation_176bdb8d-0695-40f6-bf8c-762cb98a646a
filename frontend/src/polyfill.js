// src/polyfill.js

import util from 'util';
import buffer from 'buffer';
import events from 'events';
import assert from 'assert';
import process from 'process';
import path from 'path-browserify';
import stream from 'stream-browserify';
import os from 'os-browserify/browser';

if (typeof window !== 'undefined') {
  window.Buffer = buffer.Buffer;
  window.process = process;
  window.global ||= window;
  window.stream = stream;
  window.util = util;
  window.events = events;
  window.path = path;
  window.os = os;
  window.assert = assert;
}
