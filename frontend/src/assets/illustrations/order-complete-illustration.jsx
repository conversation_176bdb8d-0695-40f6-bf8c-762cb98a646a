import { memo } from 'react';

import SvgIcon from '@mui/material/SvgIcon';

import { CONFIG } from 'src/global-config';

import { BackgroundShape } from './background-shape';

// ----------------------------------------------------------------------

function OrderCompleteIllustration({ hideBackground, sx, ...other }) {
  const renderCharacterImage = () => (
    <image
      href={`${CONFIG.assetsDir}/assets/illustrations/characters/character-happy-jump.webp`}
      height="280"
      x="270"
      y="40"
    />
  );

  return (
    <SvgIcon
      viewBox="0 0 480 360"
      xmlns="http://www.w3.org/2000/svg"
      sx={[
        (theme) => ({
          '--primary-light': theme.vars.palette.primary.light,
          '--primary-main': theme.vars.palette.primary.main,
          '--primary-dark': theme.vars.palette.primary.dark,
          '--primary-darker': theme.vars.palette.primary.darker,
          width: 320,
          maxWidth: 1,
          flexShrink: 0,
          height: 'auto',
        }),
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
      {...other}
    >
      {!hideBackground && <BackgroundShape />}

      <path
        d="M237.579 162.7C236.483 162.702 235.417 162.347 234.54 161.689C233.663 161.032 233.024 160.107 232.718 159.055L217.685 105.728C216.781 102.343 214.803 99.3424 212.049 97.1774C209.294 95.0123 205.911 93.7993 202.409 93.7207H123.938C120.428 93.7862 117.034 94.9919 114.27 97.1557C111.505 99.3195 109.52 102.324 108.613 105.716L93.6283 159.006C93.217 160.238 92.3474 161.264 91.1997 161.872C90.0521 162.48 88.7144 162.623 87.4642 162.272C86.2139 161.92 85.1469 161.101 84.4845 159.984C83.822 158.866 83.6149 157.537 83.906 156.272L98.8905 102.981C100.401 97.4754 103.662 92.6104 108.18 89.1204C112.698 85.6303 118.229 83.7046 123.938 83.6338H202.409C208.11 83.7178 213.63 85.6508 218.138 89.1421C222.647 92.6334 225.899 97.4942 227.407 102.993L242.392 156.284C242.751 157.573 242.584 158.952 241.928 160.118C241.272 161.285 240.18 162.143 238.892 162.506C238.465 162.631 238.024 162.697 237.579 162.7Z"
        fill="#C4CDD5"
      />
      <path
        d="M228.501 272.368H97.8455C95.3334 272.368 92.9075 271.452 91.0218 269.792C89.1361 268.133 87.9198 265.843 87.6006 263.351L75.4478 167.635H250.85L238.697 263.351C238.384 265.836 237.176 268.123 235.3 269.782C233.423 271.442 231.006 272.361 228.501 272.368V272.368Z"
        fill="url(#paint1_linear_4588_195698)"
      />
      <path
        d="M252.151 155.117H74.1963C68.565 155.117 64 159.682 64 165.313V166.201C64 171.832 68.565 176.397 74.1963 176.397H252.151C257.782 176.397 262.347 171.832 262.347 166.201V165.313C262.347 159.682 257.782 155.117 252.151 155.117Z"
        fill="url(#paint2_linear_4588_195698)"
      />
      <path
        d="M112.66 184.017H112.648C108.298 184.017 104.772 187.542 104.772 191.892V250.323C104.772 254.672 108.298 258.198 112.648 258.198H112.66C117.009 258.198 120.535 254.672 120.535 250.323V191.892C120.535 187.542 117.009 184.017 112.66 184.017Z"
        fill="var(--primary-darker)"
      />
      <path
        d="M146.335 184.017H146.323C141.974 184.017 138.448 187.542 138.448 191.892V250.323C138.448 254.672 141.974 258.198 146.323 258.198H146.335C150.685 258.198 154.21 254.672 154.21 250.323V191.892C154.21 187.542 150.685 184.017 146.335 184.017Z"
        fill="var(--primary-darker)"
      />
      <path
        d="M180.023 184.017H180.011C175.662 184.017 172.136 187.542 172.136 191.892V250.323C172.136 254.672 175.662 258.198 180.011 258.198H180.023C184.373 258.198 187.898 254.672 187.898 250.323V191.892C187.898 187.542 184.373 184.017 180.023 184.017Z"
        fill="var(--primary-darker)"
      />
      <path
        d="M213.699 184.017H213.687C209.337 184.017 205.812 187.542 205.812 191.892V250.323C205.812 254.672 209.337 258.198 213.687 258.198H213.699C218.048 258.198 221.574 254.672 221.574 250.323V191.892C221.574 187.542 218.048 184.017 213.699 184.017Z"
        fill="var(--primary-darker)"
      />
      <path
        d="M186.792 80H139.554C134.715 80 130.792 83.923 130.792 88.7622V88.7744C130.792 93.6136 134.715 97.5366 139.554 97.5366H186.792C191.632 97.5366 195.555 93.6136 195.555 88.7744V88.7622C195.555 83.923 191.632 80 186.792 80Z"
        fill="url(#paint3_linear_4588_195698)"
      />
      <path
        d="M238.289 279.577C262.196 279.577 281.577 260.196 281.577 236.289C281.577 212.381 262.196 193 238.289 193C214.381 193 195 212.381 195 236.289C195 260.196 214.381 279.577 238.289 279.577Z"
        fill="white"
      />
      <path
        d="M227.999 256.002L212.816 239.255C212.256 238.638 211.964 237.823 212.004 236.989C212.044 236.156 212.413 235.373 213.03 234.812L217.249 230.979C217.867 230.418 218.682 230.126 219.515 230.166C220.348 230.206 221.131 230.576 221.692 231.193L230.837 241.258L256.021 217.84C256.632 217.273 257.443 216.972 258.276 217.002C259.109 217.032 259.896 217.391 260.464 218.001L264.341 222.177C264.623 222.479 264.842 222.834 264.987 223.221C265.131 223.608 265.198 224.019 265.183 224.432C265.168 224.845 265.072 225.251 264.9 225.626C264.728 226.002 264.483 226.34 264.18 226.621L232.421 256.152C232.122 256.434 231.77 256.654 231.385 256.799C231 256.945 230.59 257.012 230.179 256.998C229.768 256.984 229.364 256.889 228.99 256.718C228.615 256.547 228.279 256.304 227.999 256.002V256.002Z"
        fill="url(#paint4_linear_4588_195698)"
      />

      <defs>
        <linearGradient
          id="paint1_linear_4588_195698"
          x1="251"
          y1="272"
          x2="64.5406"
          y2="247.455"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="var(--primary-main)" />
          <stop offset="1" stopColor="var(--primary-dark)" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_4588_195698"
          x1="64"
          y1="155.117"
          x2="64"
          y2="176.397"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="var(--primary-light)" />
          <stop offset="1" stopColor="var(--primary-dark)" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_4588_195698"
          x1="130.792"
          y1="80"
          x2="130.792"
          y2="97.5366"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="var(--primary-light)" />
          <stop offset="1" stopColor="var(--primary-dark)" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_4588_195698"
          x1="212"
          y1="217"
          x2="212"
          y2="257"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="var(--primary-light)" />
          <stop offset="1" stopColor="var(--primary-dark)" />
        </linearGradient>
      </defs>

      {renderCharacterImage()}
    </SvgIcon>
  );
}

export default memo(OrderCompleteIllustration);
