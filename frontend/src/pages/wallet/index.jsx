import { useBoolean } from 'minimal-shared/hooks';
import { useRef, useState, useEffect } from 'react';

import { Box, Button } from '@mui/material';
import Typography from '@mui/material/Typography';

import { useTranslate } from 'src/locales';
import { WalletService } from 'src/bindings';

import { Iconify } from 'src/components/iconify';

import { WalletList } from 'src/sections/wallet/wallet-list';
import { WalletEmpty } from 'src/sections/wallet/wallet-empty';
import { WalletNewDialog } from 'src/sections/wallet/wallet-new-dialog';
import { WalletImportDialog } from 'src/sections/wallet/wallet-import-dialog';

export default function WalletPage() {
  const { t } = useTranslate('wallet');
  const newDialog = useBoolean(false);
  const importDialog = useBoolean(false);
  const [wallets, setWallets] = useState([]);
  const [loading, setLoading] = useState(true);
  const walletListRef = useRef(null);

  const fetchWallets = async () => {
    try {
      const res = await WalletService.PageList(1, 10);
      setWallets(res.data || []);
    } catch (error) {
      console.error('获取钱包列表失败:', error);
      setWallets([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWallets();
  }, []);

  const handleDialogClose = () => {
    // 关闭对话框后刷新列表
    fetchWallets();
  };

  const handleNewWalletSuccess = () => {
    // 新建钱包成功后刷新表格和空状态检查
    fetchWallets(); // 更新空状态检查
    walletListRef.current?.refetch(); // 刷新表格数据
  };

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mb: 3,
        }}
      >
        <Typography variant="h5">{t('wallet:title')}</Typography>

        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="text"
            startIcon={<Iconify icon="mingcute:arrow-down-circle-fill" />}
            onClick={importDialog.onTrue}
          >
            {t('wallet:import')}
          </Button>

          <Button
            variant="text"
            startIcon={<Iconify icon="mingcute:add-circle-fill" />}
            onClick={newDialog.onTrue}
          >
            {t('wallet:create')}
          </Button>
        </Box>
      </Box>

      {/* 根据钱包数量显示不同内容 */}
      {!loading && wallets.length === 0 ? (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: 'calc(100vh - 500px)',
          }}
        >
          <WalletEmpty onCreateClick={newDialog.onTrue} onImportClick={importDialog.onTrue} />
        </Box>
      ) : (
        <WalletList ref={walletListRef} />
      )}

      {/* 对话框 */}
      {newDialog.value && (
        <WalletNewDialog
          open={newDialog.value}
          onClose={() => {
            newDialog.onFalse();
            handleDialogClose();
          }}
          onSuccess={handleNewWalletSuccess}
        />
      )}
      {importDialog.value && (
        <WalletImportDialog
          open={importDialog.value}
          onClose={() => {
            importDialog.onFalse();
            handleDialogClose();
          }}
        />
      )}
    </>
  );
}
