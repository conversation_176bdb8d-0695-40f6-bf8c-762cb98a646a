import React, { useState, useMemo } from 'react';
import { Box, Card, Typography, Button } from '@mui/material';
import { TableColumnManager } from 'src/components/table/table-column-manager';
import { useTableColumnSettings } from 'src/hooks/use-table-column-settings';

// 测试列定义
const testColumns = [
  { id: 'id', label: 'ID', width: 80 },
  { id: 'name', label: '名称', width: 200 },
  { id: 'email', label: '邮箱', width: 250 },
  { id: 'phone', label: '电话', width: 150 },
  { id: 'status', label: '状态', width: 100 },
  { id: 'created_at', label: '创建时间', width: 160 },
  { id: 'action', label: '操作', width: 120 },
];

export default function TestColumnManager() {
  const [columnMenuAnchor, setColumnMenuAnchor] = useState(null);
  const columnMenuOpen = Boolean(columnMenuAnchor);

  // 使用持久化Hook
  const columnSettings = useTableColumnSettings('test-table', testColumns);

  const handleColumnMenuOpen = (event) => {
    setColumnMenuAnchor(event.currentTarget);
  };

  const handleColumnMenuClose = () => {
    setColumnMenuAnchor(null);
  };

  // 显示当前状态
  const currentState = useMemo(() => {
    return {
      columnOrder: columnSettings.columnOrder,
      columnVisibility: columnSettings.columnVisibility,
      visibleColumns: columnSettings.visibleColumns?.map(col => col.id),
      orderedColumns: columnSettings.orderedColumns?.map(col => col.id),
    };
  }, [columnSettings]);

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3 }}>
        列管理器测试页面
      </Typography>

      <Card sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <Typography variant="h6">列管理器</Typography>
          <TableColumnManager
            tableId="test-table"
            columns={columnSettings.orderedColumns}
            anchorEl={columnMenuAnchor}
            open={columnMenuOpen}
            onClose={handleColumnMenuClose}
            onOpen={handleColumnMenuOpen}
          />
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          点击列设置按钮来测试列的显示/隐藏和拖拽排序功能
        </Typography>

        <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
          <Button
            variant="outlined"
            onClick={() => {
              console.log('=== 当前状态 ===');
              console.log('columnOrder:', columnSettings.columnOrder);
              console.log('columnVisibility:', columnSettings.columnVisibility);
              console.log('visibleColumns:', columnSettings.visibleColumns?.map(col => col.id));
              console.log('orderedColumns:', columnSettings.orderedColumns?.map(col => col.id));
              console.log('localStorage:', localStorage.getItem('table-column-settings'));
            }}
          >
            打印状态
          </Button>

          <Button
            variant="outlined"
            color="warning"
            onClick={() => {
              columnSettings.resetToDefault();
            }}
          >
            重置设置
          </Button>
        </Box>
      </Card>

      <Card sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          当前状态
        </Typography>
        <Box component="pre" sx={{ 
          fontSize: '0.875rem', 
          backgroundColor: 'grey.100', 
          p: 2, 
          borderRadius: 1,
          overflow: 'auto'
        }}>
          {JSON.stringify(currentState, null, 2)}
        </Box>
      </Card>
    </Box>
  );
}
