import { useRef, useState } from 'react';

import { Box, Typography } from '@mui/material';

import { useTranslate } from 'src/locales';

import { ImportExportAction } from 'src/components/action';

import { ListView } from 'src/sections/proxy/list';
import { ProxyNewDialog } from 'src/sections/proxy/proxy-new-dialog';

export default function Page() {
  const { t } = useTranslate();

  const listViewRef = useRef(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleImport = () => {
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
  };

  const handleExport = () => {
    console.log('export');
  };

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mb: 3,
        }}
      >
        <Typography variant="h5" sx={{ mb: 3 }}>
          {t('proxy:title')}
        </Typography>

        <Box sx={{ display: 'flex', gap: 2 }}>
          <ImportExportAction
            onImport={handleImport}
            onExport={handleExport}
            sx={{ xs: 1, md: 3 }}
          />
        </Box>
      </Box>

      <ListView ref={listViewRef} />

      {dialogOpen && <ProxyNewDialog open={dialogOpen} onClose={handleDialogClose} />}
    </>
  );
}
