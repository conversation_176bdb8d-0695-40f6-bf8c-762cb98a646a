import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router';

import { styled } from '@mui/material/styles';
import { Box, Container, TextField, IconButton, Typography, InputAdornment } from '@mui/material';

import { useAppSettings } from 'src/hooks/use-app-settings';

import { Iconify } from 'src/components/iconify';
import { unlockScreen } from 'src/components/auto-lock/lock-guard';

// 样式组件
const RootStyle = styled('div')(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'center',
  minHeight: '100vh',
  backgroundColor: theme.vars.palette.background.default,
  '&::before': {
    content: '""',
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: theme.vars.palette.background.default,
    zIndex: -1,
  },
}));

const LogoStyle = styled('div')(({ theme }) => ({
  width: 64,
  height: 64,
  borderRadius: '50%',
  backgroundColor: theme.palette.primary.main,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.spacing(3),
}));

export default function LockScreen() {
  const navigate = useNavigate();
  const location = useLocation();
  const { settings, verifyPassword } = useAppSettings();

  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [errorCount, setErrorCount] = useState(0);
  const [isLocked, setIsLocked] = useState(false);
  const [lockTimeRemaining, setLockTimeRemaining] = useState(0);
  const [returnPath, setReturnPath] = useState(location.state?.from || '/');

  // 错误次数限制和等待时间配置
  const MAX_ERROR_COUNT = 5; // 最大错误次数

  // 根据错误次数和设置中的autoLock值计算锁定时间
  const calculateLockTime = (currentErrorCount) => {
    const autoLockMinutes = settings.security.autoLock;

    // 如果autoLock设置为-1（从不）或0（总是），使用默认锁定时间
    if (autoLockMinutes <= 0) {
      return 30; // 默认30秒
    }

    // 根据错误次数递增锁定时间：
    // 第1次错误：autoLock分钟 * 0.5
    // 第2次错误：autoLock分钟 * 1
    // 第3次错误：autoLock分钟 * 1.5
    // 第4次错误：autoLock分钟 * 2
    // 第5次错误：autoLock分钟 * 3
    const multiplier =
      currentErrorCount <= 2
        ? currentErrorCount * 0.5
        : currentErrorCount === 3
          ? 1.5
          : currentErrorCount === 4
            ? 2
            : 3;

    return Math.round(autoLockMinutes * multiplier * 60); // 转换为秒
  };

  // 防止用户通过浏览器回退按钮绕过锁屏
  useEffect(() => {
    const handlePopState = () => {
      // 如果用户尝试回退，重新导航到锁屏页面
      navigate('/lock', {
        replace: true,
        state: location.state, // 保留原来的状态信息
      });
    };

    window.addEventListener('popstate', handlePopState);

    // 如果用户直接访问锁屏页面（而不是通过自动锁屏功能），
    // 并且没有from状态，则重定向到首页
    if (!location.state?.from && location.pathname === '/lock') {
      // 仅在页面加载时检查一次，使用setTimeout避免与其他导航操作冲突
      setTimeout(() => {
        navigate('/', { replace: true });
      }, 100);
    }

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [navigate, location]);

  const handlePasswordChange = (event) => {
    setPassword(event.target.value);
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // 在组件加载时保存返回路径，防止后续 location.state 变化
  useEffect(() => {
    if (location.state?.from) {
      setReturnPath(location.state.from);
    }
  }, [location.state?.from]);

  // 处理锁定时间倒计时
  useEffect(() => {
    let timer;
    if (isLocked && lockTimeRemaining > 0) {
      timer = setInterval(() => {
        setLockTimeRemaining((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            setIsLocked(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [isLocked, lockTimeRemaining]);

  const handleSubmit = async (event) => {
    event.preventDefault();

    // 如果账户已锁定，不处理提交
    if (isLocked) {
      return;
    }

    // 使用AppPasswordService验证密码
    const isValid = await verifyPassword(password);

    if (isValid) {
      // 密码正确，重置错误计数
      setErrorCount(0);

      unlockScreen();
      // 返回之前的页面
      navigate(returnPath, { replace: true });
    } else {
      // 密码错误，增加错误计数
      const newErrorCount = errorCount + 1;
      setErrorCount(newErrorCount);

      if (newErrorCount >= MAX_ERROR_COUNT) {
        // 错误次数达到上限，锁定账户
        const lockTime = calculateLockTime(newErrorCount);
        setIsLocked(true);
        setLockTimeRemaining(lockTime);
      }

      // 清空密码输入
      setPassword('');
    }
  };

  // 格式化锁定时间显示
  const formatLockTime = (seconds) => {
    if (seconds >= 60) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}分${remainingSeconds}秒`;
    }
    return `${seconds}秒`;
  };

  return (
    <RootStyle>
      <Container
        maxWidth="sm"
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          height: '100%',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <LogoStyle>
            <Iconify icon="eva:lock-outline" width={32} height={32} color="white" />
          </LogoStyle>
          <Typography component="h1" variant="h5" sx={{ mb: 3, color: 'text.primary' }}>
            欢迎回来
          </Typography>
          <Box
            component="form"
            onSubmit={handleSubmit}
            sx={{ mt: 1, width: '100%', maxWidth: 400 }}
          >
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="输入密码"
              type={showPassword ? 'text' : 'password'}
              id="password"
              autoComplete="current-password"
              value={password}
              onChange={handlePasswordChange}
              disabled={isLocked}
              error={errorCount > 0}
              helperText={
                isLocked
                  ? `账户已锁定，请等待${formatLockTime(lockTimeRemaining)}`
                  : errorCount > 0
                    ? `密码错误，还有${MAX_ERROR_COUNT - errorCount}次尝试机会`
                    : ''
              }
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleTogglePasswordVisibility}
                        edge="end"
                        disabled={isLocked}
                      >
                        {showPassword ? (
                          <Iconify icon="eva:eye-fill" />
                        ) : (
                          <Iconify icon="eva:eye-off-fill" />
                        )}
                      </IconButton>
                      {password && (
                        <>
                          <Box component="span" sx={{ width: 8 }} />
                          <IconButton
                            aria-label="submit password"
                            onClick={handleSubmit}
                            edge="end"
                          >
                            <Iconify icon="eva:arrow-forward-fill" />
                          </IconButton>
                        </>
                      )}
                    </InputAdornment>
                  ),
                },
                inputLabel: {
                  sx: {
                    color: 'text.secondary',
                    '&.Mui-focused': {
                      color: 'primary.main',
                    },
                    '&.Mui-error': {
                      color: 'error.main',
                    },
                  },
                },
              }}
              sx={{
                mb: 2,
                '& .MuiOutlinedInput-root': {
                  '& fieldset': {
                    borderColor: 'divider',
                  },
                  '&:hover fieldset': {
                    borderColor: 'text.primary',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: 'primary.main',
                  },
                  '&.Mui-error fieldset': {
                    borderColor: 'error.main',
                  },
                },
              }}
            />
          </Box>
        </Box>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            position: 'absolute',
            bottom: 40,
            left: 0,
            right: 0,
          }}
        >
          <Typography
            variant="body2"
            component="a"
            onClick={() => {
              // 可以导航到重置密码页面或显示提示
              alert('请联系管理员重置密码');
            }}
            sx={{
              cursor: 'pointer',
              color: 'text.secondary',
              '&:hover': {
                textDecoration: 'none',
                color: 'primary.main',
              },
            }}
          >
            忘记密码？
          </Typography>
        </Box>
      </Container>
    </RootStyle>
  );
}
