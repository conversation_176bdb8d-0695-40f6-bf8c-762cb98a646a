import { useRef, useState, useCallback } from 'react';

import {
  Box,
  Button,
  Dialog,
  Checkbox,
  TextField,
  IconButton,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
  FormControlLabel,
} from '@mui/material';

import { useAppSettings } from 'src/hooks/use-app-settings';

import { useTranslate } from 'src/locales/use-locales';

import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

const DEBOUNCE_DELAY = 800;
const MIN_PASSCODE_LENGTH = 6;

export function SetPasscodeDialog({ open, onClose, onConfirm, mode = 'set' }) {
  const { t } = useTranslate();
  const { verifyPassword } = useAppSettings();
  const [oldPasscode, setOldPasscode] = useState('');
  const [newPasscode, setNewPasscode] = useState('');
  const [confirmPasscode, setConfirmPasscode] = useState('');
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [oldPasswordVerified, setOldPasswordVerified] = useState(false);
  const [isVerifyingOldPassword, setIsVerifyingOldPassword] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  // 防抖定时器引用
  const verifyTimerRef = useRef(null);

  const handleClose = () => {
    // 清除防抖定时器
    if (verifyTimerRef.current) {
      clearTimeout(verifyTimerRef.current);
      verifyTimerRef.current = null;
    }

    setOldPasscode('');
    setNewPasscode('');
    setConfirmPasscode('');
    setShowOldPassword(false);
    setShowNewPassword(false);
    setShowConfirmPassword(false);
    setOldPasswordVerified(false);
    setIsVerifyingOldPassword(false);
    setAgreedToTerms(false);
    onClose();
  };

  // 防抖验证旧密码函数
  const debouncedVerifyPassword = useCallback(
    async (password) => {
      if (!password) {
        setOldPasswordVerified(false);
        setIsVerifyingOldPassword(false);
        return;
      }

      setIsVerifyingOldPassword(true);
      try {
        const isValid = await verifyPassword(password);
        setOldPasswordVerified(isValid);
      } catch (error) {
        console.error('验证旧密码失败:', error);
        setOldPasswordVerified(false);
      } finally {
        setIsVerifyingOldPassword(false);
      }
    },
    [verifyPassword]
  );

  // 验证旧密码 - 添加防抖机制
  const handleOldPasswordChange = useCallback(
    (event) => {
      const { value } = event.target;
      setOldPasscode(value);

      if (mode === 'change') {
        // 清除之前的定时器
        if (verifyTimerRef.current) {
          clearTimeout(verifyTimerRef.current);
        }

        if (!value) {
          // 如果输入为空，立即重置状态
          setOldPasswordVerified(false);
          setIsVerifyingOldPassword(false);
          return;
        }

        // 设置新的防抖定时器，DEBOUNCE_DELAY 后执行验证
        verifyTimerRef.current = setTimeout(() => {
          debouncedVerifyPassword(value);
        }, DEBOUNCE_DELAY);
      } else {
        setOldPasswordVerified(false);
      }
    },
    [mode, debouncedVerifyPassword]
  );

  const handleConfirm = () => {
    if (mode === 'change') {
      if (oldPasswordVerified && newPasscode && newPasscode === confirmPasscode) {
        onConfirm(newPasscode);
        handleClose();
      }
    } else {
      if (newPasscode && newPasscode === confirmPasscode) {
        onConfirm(newPasscode);
        handleClose();
      }
    }
  };

  const isValid =
    mode === 'change'
      ? oldPasswordVerified &&
        newPasscode &&
        newPasscode === confirmPasscode &&
        newPasscode.length >= MIN_PASSCODE_LENGTH &&
        agreedToTerms
      : newPasscode &&
        newPasscode === confirmPasscode &&
        newPasscode.length >= MIN_PASSCODE_LENGTH &&
        agreedToTerms;

  // 获取按钮文案和状态
  const getButtonState = () => {
    if (mode === 'change' && oldPasscode && !oldPasswordVerified && !isVerifyingOldPassword) {
      return { text: t('settings:security.oldPasswordIncorrect'), disabled: true };
    }

    if (isVerifyingOldPassword) {
      return { text: t('settings:security.verifyingPassword'), disabled: true };
    }

    if (!newPasscode && !confirmPasscode) {
      return { text: t('settings:security.confirm'), disabled: true };
    }

    if (newPasscode && newPasscode.length < 6) {
      return { text: t('settings:security.passwordTooShort'), disabled: true };
    }

    if (confirmPasscode && newPasscode !== confirmPasscode) {
      return { text: t('settings:security.passwordMismatch'), disabled: true };
    }

    if (
      newPasscode &&
      confirmPasscode &&
      newPasscode === confirmPasscode &&
      newPasscode.length >= MIN_PASSCODE_LENGTH &&
      !agreedToTerms
    ) {
      return { text: t('settings:security.pleaseAgreeToTerms'), disabled: true };
    }

    if (isValid) {
      return { text: t('settings:security.confirm'), disabled: false };
    }

    return { text: t('settings:security.confirm'), disabled: true };
  };

  const buttonState = getButtonState();

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      slotProps={{
        paper: {
          sx: {
            borderRadius: 3,
            p: 1,
          },
        },
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        {mode === 'change'
          ? t('settings:security.changePasscode')
          : t('settings:security.setPasscode')}
        <IconButton onClick={handleClose} size="small">
          <Iconify icon="mingcute:close-line" />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ px: 3, pb: 2 }}>
        <Box sx={{ mt: 2 }}>
          {/* 旧密码输入框 - 仅在修改密码模式下显示 */}
          {mode === 'change' && (
            <Box sx={{ mb: 3 }}>
              <Box sx={{ mb: 1, fontSize: '0.875rem', fontWeight: 500 }}>
                {t('settings:security.oldPasscode')}
              </Box>
              <TextField
                fullWidth
                type={showOldPassword ? 'text' : 'password'}
                value={oldPasscode}
                onChange={handleOldPasswordChange}
                placeholder={t('settings:security.enterOldPasscode')}
                variant="outlined"
                size="medium"
                error={oldPasscode && !oldPasswordVerified}
                helperText={
                  oldPasscode && !oldPasswordVerified
                    ? t('settings:security.oldPasswordIncorrect')
                    : ''
                }
                slotProps={{
                  input: {
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowOldPassword(!showOldPassword)}
                          edge="end"
                          size="small"
                          tabIndex={-1}
                        >
                          <Iconify
                            icon={showOldPassword ? 'solar:eye-closed-linear' : 'solar:eye-bold'}
                          />
                        </IconButton>
                      </InputAdornment>
                    ),
                  },
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  },
                }}
              />
            </Box>
          )}

          {/* 新密码输入框 */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ mb: 1, fontSize: '0.875rem', fontWeight: 500 }}>
              {t('settings:security.newPasscode')}
            </Box>
            <TextField
              fullWidth
              type={showNewPassword ? 'text' : 'password'}
              value={newPasscode}
              onChange={(e) => setNewPasscode(e.target.value)}
              placeholder={t('settings:security.createStrongPasscode')}
              variant="outlined"
              size="medium"
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        edge="end"
                        size="small"
                        tabIndex={-1}
                      >
                        <Iconify
                          icon={showNewPassword ? 'solar:eye-closed-linear' : 'solar:eye-bold'}
                        />
                      </IconButton>
                    </InputAdornment>
                  ),
                },
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                },
              }}
            />
          </Box>
          {/* 确认密码输入框 */}
          <Box sx={{ mb: 2 }}>
            <Box sx={{ mb: 1, fontSize: '0.875rem', fontWeight: 500 }}>
              {t('settings:security.confirmPasscode')}
            </Box>
            <TextField
              fullWidth
              type={showConfirmPassword ? 'text' : 'password'}
              value={confirmPasscode}
              onChange={(e) => setConfirmPasscode(e.target.value)}
              placeholder={t('settings:security.reenterPasscode')}
              variant="outlined"
              size="medium"
              error={confirmPasscode && newPasscode !== confirmPasscode}
              helperText={
                confirmPasscode && newPasscode !== confirmPasscode
                  ? t('settings:security.passwordMismatch')
                  : ''
              }
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        edge="end"
                        size="small"
                        tabIndex={-1}
                      >
                        <Iconify
                          icon={showConfirmPassword ? 'solar:eye-closed-linear' : 'solar:eye-bold'}
                        />
                      </IconButton>
                    </InputAdornment>
                  ),
                },
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                },
              }}
            />
          </Box>

          {/* 密码忘记提示确认 */}
          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={agreedToTerms}
                  onChange={(e) => setAgreedToTerms(e.target.checked)}
                  color="primary"
                />
              }
              label={
                <span
                  dangerouslySetInnerHTML={{ __html: t('settings:security.passwordForgetTip') }}
                />
              }
            />
          </Box>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button
          fullWidth
          variant="contained"
          onClick={handleConfirm}
          disabled={buttonState.disabled}
          sx={{
            borderRadius: 2,
            py: 1.5,
            fontSize: '1rem',
            fontWeight: 600,
          }}
        >
          {buttonState.text}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
