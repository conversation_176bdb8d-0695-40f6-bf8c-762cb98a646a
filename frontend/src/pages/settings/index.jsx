import { useRef, useMemo, useState, useEffect } from 'react';

import { useTheme } from '@mui/material/styles';
import {
  Box,
  List,
  Paper,
  Alert,
  Button,
  Select,
  Divider,
  MenuItem,
  Container,
  Typography,
  FormControl,
  ListItemIcon,
  ListItemText,
  ListItemButton,
} from '@mui/material';

import { useAppSettings } from 'src/hooks/use-app-settings';

import { useTranslate } from 'src/locales/use-locales';

import { Iconify } from 'src/components/iconify';

import { SetPasscodeDialog } from './set-passcode-dialog';

export default function SettingsPage() {
  const theme = useTheme();
  const { t } = useTranslate();
  const [selectedSection, setSelectedSection] = useState('general');
  const [passcodeDialogOpen, setPasscodeDialogOpen] = useState(false);

  // 使用专门的应用设置hook
  const { settings, updateSetting, hasPassword, setPassword } = useAppSettings();

  const contentRef = useRef(null);
  const sectionRefs = useRef({});

  const menuItems = useMemo(
    () => [
      {
        id: 'backup',
        label: t('settings:backup.title'),
        icon: <Iconify icon="material-symbols:backup-rounded" />,
      },
      {
        id: 'security',
        label: t('settings:security.title'),
        icon: <Iconify icon="material-symbols:security" />,
      },
      {
        id: 'about',
        label: t('settings:about.title'),
        icon: <Iconify icon="material-symbols:info-rounded" />,
      },
    ],
    [t]
  );

  const handleSettingChange = (key, value) => {
    updateSetting(key, value);
  };

  // 处理密码设置
  const handleOpenPasscodeDialog = () => {
    setPasscodeDialogOpen(true);
  };

  const handleClosePasscodeDialog = () => {
    setPasscodeDialogOpen(false);
  };

  const handleConfirmPasscode = async (newPasscode) => {
    try {
      const success = await setPassword(newPasscode);
      if (success) {
        console.log('密码设置成功');
      } else {
        console.error('密码设置失败');
      }
    } catch (error) {
      console.error('密码设置异常:', error);
    }
  };

  // 滚动到指定部分
  const scrollToSection = (sectionId) => {
    const sectionElement = sectionRefs.current[sectionId];
    if (sectionElement && contentRef.current) {
      const sectionTop = sectionElement.offsetTop;

      // 计算滚动位置，使目标部分位于容器顶部
      const scrollPosition = sectionTop - 24; // 24px 为顶部边距

      contentRef.current.scrollTo({
        top: scrollPosition,
        behavior: 'smooth',
      });
    }
    setSelectedSection(sectionId);
  };

  // 监听滚动事件，更新当前选中的部分
  useEffect(() => {
    const container = contentRef.current;
    if (!container) return undefined;

    const handleScroll = () => {
      const scrollTop = container.scrollTop;

      // 查找当前在视口中的部分
      for (const item of menuItems) {
        const element = sectionRefs.current[item.id];
        if (element) {
          const elementTop = element.offsetTop;
          const elementBottom = elementTop + element.clientHeight;

          // 如果元素的上边界在视口上半部分，则认为是当前活跃部分
          if (elementTop <= scrollTop + 100 && elementBottom > scrollTop + 100) {
            if (selectedSection !== item.id) {
              setSelectedSection(item.id);
            }
            break;
          }
        }
      }
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [selectedSection, menuItems]);

  // 渲染左侧菜单
  const renderSidebar = () => (
    <Paper
      elevation={0}
      sx={{
        width: 180,
        height: '100%',
        borderRight: `1px solid ${theme.palette.divider}`,
        overflow: 'hidden',
        borderRadius: 0,
      }}
    >
      <List sx={{ py: 0 }}>
        {menuItems.map((item) => (
          <ListItemButton
            key={item.id}
            selected={selectedSection === item.id}
            onClick={() => scrollToSection(item.id)}
            sx={{
              py: 1.5,
              px: 1,
              '&.Mui-selected': {
                backgroundColor: theme.palette.primary.main + '10',
                borderRight: `3px solid ${theme.palette.primary.main}`,
              },
            }}
          >
            <ListItemIcon sx={{ minWidth: 40 }}>{item.icon}</ListItemIcon>
            <ListItemText primary={item.label} />
          </ListItemButton>
        ))}
      </List>
    </Paper>
  );

  // 渲染设置内容区域
  const renderContent = () => (
    <Box
      ref={contentRef}
      sx={{
        flex: 1,
        height: '100%',
        overflowY: 'auto',
        px: 3,
        pt: 3,
        pb: 6,
      }}
    >
      {/* 备份设置 */}
      <Box ref={(el) => (sectionRefs.current.backup = el)} sx={{ mb: 6 }}>
        <Typography variant="h5" gutterBottom>
          {t('settings:backup.title')}
        </Typography>
        <Box sx={{ mt: 3 }}>
          <Alert severity="warning" sx={{ mb: 2 }}>
            <span dangerouslySetInnerHTML={{ __html: t('settings:backup.backupWarning') }} />
          </Alert>
        </Box>
      </Box>

      <Divider sx={{ mb: 3, mt: 3 }} />
      {/* 安全设置 */}
      <Box ref={(el) => (sectionRefs.current.security = el)}>
        <Typography variant="h5" gutterBottom>
          {t('settings:security.title')}
        </Typography>
        <Box sx={{ mt: 3 }}>
          {!hasPassword ? (
            <>
              <Button variant="outlined" sx={{ mb: 0.5 }} onClick={handleOpenPasscodeDialog}>
                {t('settings:security.setPasscode')}
              </Button>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                <span dangerouslySetInnerHTML={{ __html: t('settings:security.passwordTip') }} />
              </Typography>
            </>
          ) : (
            <Button variant="outlined" sx={{ mb: 2 }} onClick={handleOpenPasscodeDialog}>
              {t('settings:security.changePasscode')}
            </Button>
          )}

          <Typography variant="body1" sx={{ mb: 2 }}>
            {t('settings:security.autoLock')}:
          </Typography>
          <FormControl fullWidth sx={{ mr: 1, ml: 1, minWidth: 120 }}>
            <Select
              size="small"
              value={settings.security.autoLock}
              onChange={(e) => handleSettingChange('security.autoLock', e.target.value)}
            >
              <MenuItem value={-1}>{t('settings:security.never')}</MenuItem>
              <MenuItem value={1}>{t('settings:security.ifAwayFor1min')}</MenuItem>
              <MenuItem value={5}>{t('settings:security.ifAwayFor5mins')}</MenuItem>
              <MenuItem value={10}>{t('settings:security.ifAwayFor10mins')}</MenuItem>
              <MenuItem value={0}>{t('settings:security.always')}</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Box>

      <Divider sx={{ mb: 3, mt: 3 }} />

      {/* 关于 */}
      <Box ref={(el) => (sectionRefs.current.about = el)} sx={{ mb: 6 }}>
        <Typography variant="h5" gutterBottom>
          {t('settings:about.title')}
        </Typography>
        <Box sx={{ mt: 3 }}>
          <Typography variant="body1" sx={{ mb: 2 }}>
            {t('settings:about.version')}: {settings.about.version}
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            {t('settings:about.buildTime')}: 2024-01-01
          </Typography>
          <Typography variant="body1" sx={{ mb: 3 }}>
            {t('settings:about.author')}: A8Tools Team
          </Typography>
          <Button variant="contained" color="primary">
            {t('settings:about.checkUpdate')}
          </Button>
        </Box>
      </Box>
    </Box>
  );

  return (
    <Container maxWidth="xl" sx={{ height: '100vh' }}>
      <Box
        sx={{
          display: 'flex',
          gap: 0,
          height: '100%',
          maxHeight: 'calc(100vh - 88px)',
        }}
      >
        {renderSidebar()}
        {renderContent()}
      </Box>

      {/* 设置密码对话框 */}
      {passcodeDialogOpen && (
        <SetPasscodeDialog
          open={passcodeDialogOpen}
          onClose={handleClosePasscodeDialog}
          onConfirm={handleConfirmPasscode}
          mode={hasPassword ? 'change' : 'set'}
        />
      )}
    </Container>
  );
}
