import { useState, useEffect } from 'react';

import {
  Box,
  Stack,
  Dialog,
  Button,
  TextField,
  CardHeader,
  IconButton,
  DialogActions,
  DialogContent,
  InputAdornment,
} from '@mui/material';

import { useTranslate } from 'src/locales';
import { XService, MailService, DiscordService, TelegramService } from 'src/bindings';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

// 账号类型配置
const ACCOUNT_CONFIG = {
  discord: {
    service: DiscordService,
    nameKey: 'Discord',
  },
  telegram: {
    service: TelegramService,
    nameKey: 'Telegram',
  },
  x: {
    service: XService,
    nameKey: 'X',
  },
  mail: {
    service: MailService,
    nameKey: 'Email',
  },
};

export function AccountEditDialog({ open, onClose, account, accountType, onSuccess }) {
  const { t } = useTranslate();

  const [formData, setFormData] = useState({
    Account: '',
    Password: '',
    Group: '',
    Labels: '',
    Remark: '',
  });
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const config = ACCOUNT_CONFIG[accountType] || ACCOUNT_CONFIG.mail;

  // 初始化表单数据
  useEffect(() => {
    if (account) {
      setFormData({
        Account: account.Account || '',
        Password: account.Password || '',
        Group: account.Group || '',
        Labels: account.Labels || '',
        Remark: account.Remark || '',
      });
    } else {
      // 创建模式，重置表单
      setFormData({
        Account: '',
        Password: '',
        Group: '',
        Labels: '',
        Remark: '',
      });
    }
  }, [account, open]);

  // 处理表单输入
  const handleInputChange = (field) => (event) => {
    setFormData((prev) => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  // 处理提交
  const handleSubmit = async () => {
    // 验证必填项
    if (!formData.Account.trim()) {
      toast.error(t('account:validation.accountRequired'));
      return;
    }

    if (!formData.Password.trim()) {
      toast.error(t('account:validation.passwordRequired'));
      return;
    }

    setLoading(true);
    try {
      const accountData = {
        Account: formData.Account.trim(),
        Password: formData.Password.trim(),
        Group: formData.Group.trim(),
        Labels: formData.Labels.trim(),
        Remark: formData.Remark.trim(),
      };

      const isCreate = !account?.ID;

      if (isCreate) {
        // 创建模式
        await config.service.Create(accountData);
        toast.success(t('common:message.createSuccess'));
      } else {
        // 编辑模式
        const updatedAccount = {
          ...account,
          ...accountData,
        };
        await config.service.Update(updatedAccount);
        toast.success(t('common:message.updateSuccess'));
      }

      onSuccess && onSuccess();
      handleCancel();
    } catch (error) {
      const isCreate = !account?.ID;
      console.error(`${isCreate ? '创建' : '更新'}${config.nameKey}账号失败:`, error);
      toast.error(t(`common:message.${isCreate ? 'createFailed' : 'updateFailed'}`));
    } finally {
      setLoading(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setFormData({
      Account: '',
      Password: '',
      Group: '',
      Labels: '',
      Remark: '',
    });
    setShowPassword(false);
    onClose();
  };

  // 切换密码显示
  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  const isCreate = !account?.ID;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <CardHeader
        title={
          isCreate
            ? t('account:create', { type: config.nameKey })
            : t('account:edit', { type: config.nameKey })
        }
        action={
          <IconButton onClick={handleCancel}>
            <Iconify icon="solar:close-circle-bold" />
          </IconButton>
        }
      />

      <DialogContent>
        <Stack spacing={3} sx={{ pt: 1 }}>
          <TextField
            fullWidth
            label={t('account:account')}
            value={formData.Account}
            onChange={handleInputChange('Account')}
            placeholder={t('account:account')}
            required
            error={!formData.Account.trim() && formData.Account !== ''}
            helperText={
              !formData.Account.trim() && formData.Account !== ''
                ? t('account:validation.accountRequired')
                : ''
            }
          />

          <TextField
            fullWidth
            label={t('account:password')}
            type={showPassword ? 'text' : 'password'}
            value={formData.Password}
            onChange={handleInputChange('Password')}
            placeholder={t('account:password')}
            required
            error={!formData.Password.trim() && formData.Password !== ''}
            helperText={
              !formData.Password.trim() && formData.Password !== ''
                ? t('account:validation.passwordRequired')
                : ''
            }
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton onClick={togglePasswordVisibility} edge="end">
                      <Iconify icon={showPassword ? 'solar:eye-bold' : 'solar:eye-closed-bold'} />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
          />

          <TextField
            fullWidth
            label={t('account:group')}
            value={formData.Group}
            onChange={handleInputChange('Group')}
            placeholder={t('account:group')}
          />

          <TextField
            fullWidth
            label={t('account:label')}
            value={formData.Labels}
            onChange={handleInputChange('Labels')}
            placeholder={t('account:label')}
          />

          <TextField
            fullWidth
            label={t('account:remark')}
            value={formData.Remark}
            onChange={handleInputChange('Remark')}
            placeholder={t('account:remark')}
            multiline
            rows={3}
          />

          {/* 如果是编辑模式，显示一些只读信息 */}
          {!isCreate && (
            <Box
              sx={{
                p: 2,
                bgcolor: 'background.neutral',
                borderRadius: 1,
                border: '1px dashed',
                borderColor: 'divider',
              }}
            >
              <Stack spacing={1}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Box component="span" sx={{ color: 'text.secondary' }}>
                    ID:
                  </Box>
                  <Box component="span" sx={{ fontWeight: 500 }}>
                    {account.ID}
                  </Box>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Box component="span" sx={{ color: 'text.secondary' }}>
                    {t('common:createdAt')}:
                  </Box>
                  <Box component="span" sx={{ fontWeight: 500 }}>
                    {account.CreatedAt ? new Date(account.CreatedAt).toLocaleString() : '-'}
                  </Box>
                </Box>
              </Stack>
            </Box>
          )}
        </Stack>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={handleCancel} color="inherit" variant="outlined">
          {t('common:action.cancel')}
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading || !formData.Account.trim() || !formData.Password.trim()}
          loading={loading}
        >
          {isCreate ? t('common:action.create') : t('common:action.save')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
