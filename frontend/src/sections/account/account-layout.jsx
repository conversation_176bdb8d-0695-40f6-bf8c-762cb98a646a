import { removeLastSlash } from 'minimal-shared/utils';

import Tab from '@mui/material/Tab';
import { Box } from '@mui/material';
import Tabs from '@mui/material/Tabs';

import { paths } from 'src/routes/paths';
import { usePathname } from 'src/routes/hooks';
import { RouterLink } from 'src/routes/components';

import { useTranslate } from 'src/locales';
import { CONFIG } from 'src/global-config';

import { SvgColor } from 'src/components/svg-color';
import { ImportExportAction } from 'src/components/action/import-export';

// ----------------------------------------------------------------------

function getNavItems(t) {
  return [
    {
      label: t('account:email'),
      icon: <SvgColor width={24} src={`${CONFIG.assetsDir}/assets/icons/navbar/ic-mail.svg`} />,
      href: paths.account.root,
    },
    {
      label: t('account:x'),
      icon: <SvgColor width={24} src={`${CONFIG.assetsDir}/assets/icons/navbar/ic-twitter.svg`} />,
      href: paths.account.x.root,
    },
    {
      label: t('account:telegram'),
      icon: <SvgColor width={24} src={`${CONFIG.assetsDir}/assets/icons/navbar/ic-telegram.svg`} />,
      href: paths.account.telegram.root,
    },
    {
      label: t('account:discord'),
      icon: <SvgColor width={24} src={`${CONFIG.assetsDir}/assets/icons/navbar/ic-discord.svg`} />,
      href: paths.account.discord.root,
    },
  ];
}

// ----------------------------------------------------------------------

export function AccountLayout({ children, ...other }) {
  const pathname = usePathname();
  const { t } = useTranslate('account');
  const navItems = getNavItems(t);

  const handleAddIndividual = () => {
    console.log('Add individual');
  };

  const handleBatchAdd = () => {
    console.log('Batch add');
  };

  const handleExport = () => {
    console.log('export');
  };

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'flex-start',
          justifyContent: 'space-between',
          mb: { xs: 1, md: 3 },
        }}
      >
        <Tabs value={removeLastSlash(pathname)}>
          {navItems.map((tab) => (
            <Tab
              component={RouterLink}
              key={tab.href}
              label={tab.label}
              icon={tab.icon}
              value={tab.href}
              href={tab.href}
            />
          ))}
        </Tabs>

        <ImportExportAction
          onAddIndividual={handleAddIndividual}
          onBatchAdd={handleBatchAdd}
          onExport={handleExport}
          sx={{ xs: 1, md: 3 }}
        />
      </Box>
      {children}
    </>
  );
}
