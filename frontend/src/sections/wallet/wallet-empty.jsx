import { Box, Stack, Typography } from '@mui/material';

import { useTranslate } from 'src/locales';

import { Iconify } from 'src/components/iconify';

export function WalletEmpty({ onCreateClick, onImportClick }) {
  const { t } = useTranslate('wallet');
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        py: 5,
        textAlign: 'center',
      }}
    >
      <Stack spacing={2} alignItems="center">
        <Iconify
          icon="hugeicons:wallet-02"
          width={100}
          height={100}
          sx={{ color: 'text.secondary' }}
        />
        <Typography variant="h6" color="text.secondary">
          {t('wallet:empty')}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {t('wallet:please')}{' '}
          <Box
            component="span"
            sx={{
              color: 'primary.main',
              cursor: 'pointer',
              fontWeight: 'bold',
              '&:hover': { textDecoration: 'underline' },
            }}
            onClick={onCreateClick}
          >
            {t('wallet:create')}
          </Box>{' '}
          {t('wallet:or')}{' '}
          <Box
            component="span"
            sx={{
              color: 'primary.main',
              cursor: 'pointer',
              fontWeight: 'bold',
              '&:hover': { textDecoration: 'underline' },
            }}
            onClick={onImportClick}
          >
            {t('wallet:import')}
          </Box>{' '}
          {t('wallet:wallet')}
        </Typography>
      </Stack>
    </Box>
  );
}
