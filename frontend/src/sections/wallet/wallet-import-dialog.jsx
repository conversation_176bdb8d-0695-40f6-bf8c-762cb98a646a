import * as XLSX from 'xlsx';
import PropTypes from 'prop-types';
import React, { useState, useCallback } from 'react';

// @mui
import {
  Box,
  Tab,
  Tabs,
  <PERSON>ack,
  Di<PERSON>,
  <PERSON><PERSON>,
  Typo<PERSON>,
  Card<PERSON><PERSON>er,
  IconButton,
  DialogContent,
  CircularProgress,
} from '@mui/material';

import { useTranslate } from 'src/locales';

import { Upload } from 'src/components/upload';
import { Iconify } from 'src/components/iconify';
import { LineNumberedTextArea } from 'src/components/numbered-textarea/';

// ----------------------------------------------------------------------

// ----------------------------------------------------------------------

export function WalletImportDialog({ open, onClose, onSuccess }) {
  const { t } = useTranslate('wallet');
  const [tab, setTab] = useState(0);
  const [mnemonic, setMnemonic] = useState('');
  const [file, setFile] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState();

  const handleChangeTab = (event, newValue) => {
    setTab(newValue);
    setErrorMessage(''); // 切换标签时清除错误信息
    // 在切换到文件上传时重置文件
    if (newValue === 1) {
      setFile(null);
    }
  };

  const handleChangeMnemonic = (event) => {
    const newValue = typeof event === 'string' ? event : event.target.value;
    setMnemonic(newValue);
    if (errorMessage) setErrorMessage(''); // 用户输入时清除错误信息
  };

  // 文件大小限制（字节）
  const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

  // 处理文件拒绝事件
  const handleFileReject = useCallback((rejectedFiles) => {
    if (rejectedFiles?.length > 0) {
      const { file: rejectedFile, errors } = rejectedFiles[0];

      // 更新错误消息
      if (errors.some((error) => error.code === 'file-too-large')) {
        const fileSize = (rejectedFile.size / (1024 * 1024)).toFixed(2);
        setErrorMessage(`文件过大，请上传不超过10MB的文件（当前文件大小：${fileSize}MB）`);
      } else if (errors.some((error) => error.code === 'file-invalid-type')) {
        setErrorMessage('文件类型不支持，请上传 .csv, .xls, .xlsx 或 .txt 文件');
      } else {
        setErrorMessage('文件上传失败，请重试');
      }
    }
  }, []);

  const handleDropSingleFile = useCallback((acceptedFiles) => {
    const newFile = acceptedFiles[0];
    if (newFile) {
      setFile(newFile);
      setIsLoading(true); // 开始加载

      // 获取文件扩展名
      const fileExt = newFile.name.split('.').pop().toLowerCase();

      // 处理文件并解析内容
      const processFile = (content) => {
        if (!content || content.trim() === '') {
          setErrorMessage('文件内容为空，请上传包含助记词的有效文件');
          setIsLoading(false);
          return;
        }
        setTab(0);
        setMnemonic(content);
        setIsLoading(false);
        setErrorMessage(''); // 成功处理后清除错误信息
      };

      try {
        const reader = new FileReader();

        // 定义reader的onload事件处理函数
        reader.onload = (e) => {
          try {
            let content = '';

            if (['txt', 'csv'].includes(fileExt)) {
              // 按行读取文件内容并去除空行
              content = e.target.result
                .split('\n')
                .filter((line) => line.trim() !== '') // 去除空行
                .join('\n');
            } else if (['xls', 'xlsx'].includes(fileExt)) {
              // 处理Excel文件
              const data = new Uint8Array(e.target.result);
              const workbook = XLSX.read(data, { type: 'array' });
              const firstSheetName = workbook.SheetNames[0];
              const worksheet = workbook.Sheets[firstSheetName];
              const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

              // 将JSON数据转换为文本
              content = jsonData
                .filter((row) => row && row.length > 0) // 去除空行
                .map((row) => row[0]) // 取每行的第一列
                .join('\n'); // 用换行符连接
            }

            processFile(content);
          } catch (err) {
            console.error('文件解析错误:', err);
            setIsLoading(false);
            setErrorMessage(`文件解析失败: ${err.message || '请检查文件格式是否正确'}`);
          }
        };

        // 处理文件读取错误
        reader.onerror = () => {
          console.error('文件读取错误');
          setIsLoading(false);
          setErrorMessage('文件读取失败，请重试或选择其他文件');
        };

        // 根据文件类型选择不同的读取方式
        if (['txt', 'csv'].includes(fileExt)) {
          reader.readAsText(newFile);
        } else if (['xls', 'xlsx'].includes(fileExt)) {
          reader.readAsArrayBuffer(newFile);
        }
      } catch (err) {
        console.error('文件处理错误:', err);
        setIsLoading(false);
        setErrorMessage(`文件处理错误: ${err.message || '请重试或选择其他文件'}`);
      }
    }
  }, []);

  const handleImport = useCallback(() => {
    // 将多行mnemonic转换为数组
    const mnemonicArray = mnemonic
      .split('\n')
      .map((line) => line.trim())
      .filter((line) => line !== '');

    // 处理助记词
    console.log('mnemonicArray:', { mnemonicArray });

    // 假设导入成功，通知父组件
    const importedCount = mnemonicArray.length;
    if (onSuccess) {
      onSuccess(importedCount);
    }

    onClose();
  }, [mnemonic, onClose, onSuccess]);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <CardHeader
        title={t('wallet:import_mnemonic')}
        sx={{ mb: 1 }}
        action={
          <IconButton aria-label="关闭" onClick={onClose}>
            <Iconify icon="solar:close-circle-bold" />
          </IconButton>
        }
      />

      <DialogContent sx={{ pb: 0, px: { xs: 2, md: 4 }, maxHeight: '80vh', overflow: 'auto' }}>
        <Tabs
          value={tab}
          onChange={handleChangeTab}
          sx={{
            mb: 3,
            '& .MuiTabs-flexContainer': {
              justifyContent: 'center',
            },
          }}
        >
          <Tab label={t('wallet:manual_input')} />
          <Tab label={t('wallet:upload_file')} />
        </Tabs>

        {/* 固定高度的内容容器 */}
        <Box sx={{ minHeight: 300, position: 'relative' }}>
          {tab === 0 && (
            <Box sx={{ height: '100%' }}>
              <Typography variant="body2" sx={{ mb: 1 }}>
                {t('wallet:import_mnemonic_tip')}
              </Typography>

              <LineNumberedTextArea
                maxRows={100}
                rows={10}
                value={mnemonic}
                onChange={handleChangeMnemonic}
                placeholder="edge scout smile ranch wasp gate potato brush drift never snack remain"
                height={250}
                onError={setErrorMessage}
              />
            </Box>
          )}

          {tab === 1 && (
            <Box sx={{ height: '100%' }}>
              <Typography variant="body2" sx={{ mb: 1 }}>
                {t('wallet:upload_file_tip')}
                {isLoading && (
                  <Box
                    component="span"
                    sx={{ display: 'inline-flex', alignItems: 'center', ml: 1 }}
                  >
                    <CircularProgress size={16} thickness={2} />
                    <Typography variant="caption" sx={{ ml: 1 }}>
                      正在解析文件...
                    </Typography>
                  </Box>
                )}
              </Typography>

              <Box
                sx={{
                  borderRadius: 1,
                }}
              >
                <Upload
                  file={file}
                  onDrop={handleDropSingleFile}
                  accept={{
                    'text/csv': ['.csv'],
                    'application/vnd.ms-excel': ['.xls'],
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
                    'text/plain': ['.txt'],
                  }}
                  maxSize={MAX_FILE_SIZE}
                  onFileReject={handleFileReject}
                  placeholder={
                    <Stack
                      spacing={1}
                      alignItems="center"
                      justifyContent="center"
                      sx={{ height: 200 }}
                    >
                      <Iconify
                        icon="eva:cloud-upload-fill"
                        width={40}
                        sx={{ color: 'primary.main' }}
                      />
                      <Typography variant="body2">拖拽文件到这里或点击选择</Typography>
                    </Stack>
                  }
                  sx={{
                    height: 'auto',
                    minHeight: 200,
                    '& .MuiBox-root': { p: 2.5 },
                    '&:hover': { opacity: 0.72 },
                  }}
                />
              </Box>
            </Box>
          )}
        </Box>
        {/* 错误提示 */}
        {errorMessage && (
          <Typography color="error" variant="body2" sx={{ mt: 1, mb: 2, mx: 2 }}>
            <Box component="span" sx={{ display: 'flex', alignItems: 'center' }}>
              <Iconify icon="solar:info-circle-bold" width={20} sx={{ mr: 1 }} />
              {errorMessage}
            </Box>
          </Typography>
        )}

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3, mb: 3 }}>
          <Box>
            {mnemonic && (
              <Button
                variant="outlined"
                color="inherit"
                onClick={() => {
                  setMnemonic('');
                  setFile(null);
                  setTab(0);
                  setErrorMessage(''); // 清空时清除错误信息
                }}
              >
                清空
              </Button>
            )}
          </Box>
          <Stack direction="row" spacing={2}>
            <Button variant="outlined" color="inherit" onClick={onClose}>
              {t('common:action.cancel')}
            </Button>
            <Button variant="contained" onClick={handleImport}>
              {t('common:action.import')}
            </Button>
          </Stack>
        </Box>
      </DialogContent>
    </Dialog>
  );
}

WalletImportDialog.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  onSuccess: PropTypes.func,
};
