import { useState, useEffect } from 'react';

import {
  Box,
  Stack,
  Dialog,
  Button,
  TextField,
  CardHeader,
  IconButton,
  DialogActions,
  DialogContent,
} from '@mui/material';

import { useTranslate } from 'src/locales';
import { Wallet, WalletService } from 'src/bindings';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

export function WalletEditDialog({ open, onClose, wallet, onSuccess }) {
  const { t } = useTranslate();

  const [formData, setFormData] = useState({
    Name: '',
    Group: '',
    Remark: '',
  });
  const [loading, setLoading] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    if (wallet) {
      setFormData({
        Name: wallet.Name || '',
        Group: wallet.Group || '',
        Remark: wallet.Remark || '',
      });
    }
  }, [wallet]);

  // 处理表单输入
  const handleInputChange = (field) => (event) => {
    setFormData((prev) => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  // 处理提交
  const handleSubmit = async () => {
    if (!wallet) return;

    // 验证必填项
    if (!formData.Name.trim()) {
      toast.error(t('wallet:validation.nameRequired'));
      return;
    }

    setLoading(true);
    try {
      // 创建更新对象
      const updatedWallet = new Wallet({
        ...wallet,
        Name: formData.Name.trim(),
        Group: formData.Group.trim(),
        Remark: formData.Remark.trim(),
      });

      await WalletService.Update(updatedWallet);
      toast.success(t('common:message.updateSuccess'));
      onSuccess && onSuccess();
    } catch (error) {
      console.error('更新钱包失败:', error);
      toast.error(t('common:message.updateFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setFormData({
      Name: '',
      Group: '',
      Remark: '',
    });
    onClose();
  };

  const isCreate = !wallet?.ID;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <CardHeader
        title={isCreate ? t('wallet:create') : t('common:action.edit')}
        action={
          <IconButton onClick={handleCancel}>
            <Iconify icon="solar:close-circle-bold" />
          </IconButton>
        }
      />

      <DialogContent>
        <Stack spacing={3} sx={{ pt: 1 }}>
          <TextField
            fullWidth
            label={t('wallet:name')}
            value={formData.Name}
            onChange={handleInputChange('Name')}
            placeholder={t('wallet:namePlaceholder')}
            required
          />

          <TextField
            fullWidth
            label={t('wallet:group')}
            value={formData.Group}
            onChange={handleInputChange('Group')}
            placeholder={t('wallet:groupPlaceholder')}
          />

          <TextField
            fullWidth
            label={t('wallet:remark')}
            value={formData.Remark}
            onChange={handleInputChange('Remark')}
            placeholder={t('wallet:remarkPlaceholder')}
            multiline
            rows={3}
          />

          {/* 如果是编辑模式，显示一些只读信息 */}
          {!isCreate && (
            <Box
              sx={{
                p: 2,
                bgcolor: 'background.neutral',
                borderRadius: 1,
                border: '1px dashed',
                borderColor: 'divider',
              }}
            >
              <Stack spacing={1}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Box component="span" sx={{ color: 'text.secondary' }}>
                    ID:
                  </Box>
                  <Box component="span" sx={{ fontWeight: 500 }}>
                    {wallet.ID}
                  </Box>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Box component="span" sx={{ color: 'text.secondary' }}>
                    {t('common:createdAt')}:
                  </Box>
                  <Box component="span" sx={{ fontWeight: 500 }}>
                    {wallet.CreatedAt ? new Date(wallet.CreatedAt).toLocaleString() : '-'}
                  </Box>
                </Box>
              </Stack>
            </Box>
          )}
        </Stack>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={handleCancel} color="inherit" variant="outlined">
          {t('common:action.cancel')}
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading || !formData.Name.trim()}
          loading={loading}
        >
          {isCreate ? t('common:action.create') : t('common:action.save')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
