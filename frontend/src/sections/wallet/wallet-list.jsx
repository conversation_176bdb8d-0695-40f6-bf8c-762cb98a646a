import { useBoolean } from 'minimal-shared/hooks';
import { useRef, useMemo, useState, forwardRef, useCallback, useImperativeHandle } from 'react';

import { Box, Chip, Button, Tooltip, IconButton } from '@mui/material';

import { fDateTime } from 'src/utils/format-time';
import { highlightText } from 'src/utils/highlight-text';

import { useTranslate } from 'src/locales';
import { WalletService } from 'src/bindings';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { TableView } from 'src/components/table';
import { ConfirmDialog } from 'src/components/custom-dialog';

import { WalletEditDialog } from './wallet-edit-dialog';

export const WalletList = forwardRef(function WalletList(props, ref) {
  const { t } = useTranslate();
  const [selectedWallet, setSelectedWallet] = useState(null);
  const [currentSearchTerm, setCurrentSearchTerm] = useState('');
  const tableRef = useRef(null);

  const editDialog = useBoolean(false);
  const deleteDialog = useBoolean(false);

  // 暴露给父组件的方法
  useImperativeHandle(
    ref,
    () => ({
      refetch: () => {
        tableRef.current?.refetch();
      },
    }),
    []
  );

  // 获取钱包数据
  const getData = useCallback(
    async (page, pageSize, orderBy, order, searchValue) => {
      try {
        // 更新当前搜索词状态，用于高亮显示
        setCurrentSearchTerm(searchValue?.trim() || '');

        // 构建分页参数，支持排序
        const pageable = {
          page: page + 1, // 后端从1开始
          pageSize,
          sort: orderBy || 'id', // 默认按 id 排序，使用小写与列定义保持一致
          order: order || 'desc', // 默认降序
        };

        let queryBuilder = null;

        // 如果有搜索词，构建查询条件
        if (searchValue && searchValue.trim()) {
          queryBuilder = {
            conditions: [
              {
                field: 'mnemonic',
                operator: 'LIKE',
                value: `%${searchValue.trim()}%`,
                logic: 'OR',
              },
              {
                field: 'name',
                operator: 'LIKE',
                value: `%${searchValue.trim()}%`,
                logic: 'OR',
              },
            ],
            logic: 'OR',
          };
        }

        // 使用支持排序的 Page 方法
        const result = await WalletService.Page(pageable, queryBuilder);

        return [result.data || [], result.total || 0];
      } catch (error) {
        console.error('获取钱包数据失败:', error);
        toast.error(t('common:message.loadFailed'));
        return [[], 0];
      }
    },
    [t]
  );

  // 处理编辑
  const handleEdit = useCallback(
    (wallet, e) => {
      e.stopPropagation();
      setSelectedWallet(wallet);
      editDialog.onTrue();
    },
    [editDialog]
  );

  // 处理删除
  const handleDelete = useCallback(
    (wallet, e) => {
      e.stopPropagation();
      setSelectedWallet(wallet);
      deleteDialog.onTrue();
    },
    [deleteDialog]
  );

  // 确认删除单个钱包
  const confirmDelete = async () => {
    if (!selectedWallet) return;

    try {
      await WalletService.DeleteByID(selectedWallet.ID);
      toast.success(t('common:message.deleteSuccess'));
      tableRef.current?.refetch(); // 刷新列表
      deleteDialog.onFalse();
      setSelectedWallet(null);
    } catch (error) {
      console.error('删除钱包失败:', error);
      toast.error(t('common:message.deleteFailed'));
    }
  };

  // 批量删除钱包
  const handleBatchDelete = useCallback(
    async (selectedIds) => {
      if (!selectedIds || selectedIds.length === 0) return;

      try {
        // 使用后端的批量删除方法
        await WalletService.BatchDelete(selectedIds);
        toast.success(t('common:message.deleteSuccess', { count: selectedIds.length }));
        tableRef.current?.refetch(); // 刷新列表
      } catch (error) {
        console.error('批量删除钱包失败:', error);
        toast.error(t('common:message.deleteFailed'));
      }
    },
    [t]
  );

  // 处理编辑完成
  const handleEditComplete = () => {
    editDialog.onFalse();
    setSelectedWallet(null);
    tableRef.current?.refetch(); // 刷新列表
  };

  // 表格列定义 - 使用 useMemo 优化性能并支持高亮搜索词
  const columns = useMemo(
    () => [
      {
        id: 'id',
        label: 'ID',
        width: 80,
        minWidth: 80,
        align: 'center',
        sortable: true, // 明确启用排序功能
      },
      {
        id: 'name',
        label: t('wallet:label_name'),
        width: 230,
        minWidth: 230,
        noWrap: true,
        renderCell: (row) => (
          <Box sx={{ fontWeight: 500 }}>{highlightText(row.name || '-', currentSearchTerm)}</Box>
        ),
      },
      {
        id: 'mnemonic',
        label: 'Mnemonic',
        width: 320,
        minWidth: 300,
        noWrap: true,
        sortable: false,
        renderCell: (row) => (
          <Box sx={{ fontWeight: 500 }}>
            {highlightText(row.mnemonic || '-', currentSearchTerm)}
          </Box>
        ),
      },
      {
        id: 'created_at',
        label: t('common:createdAt'),
        width: 160,
        minWidth: 160,
        noWrap: true,
        sortable: true, // 启用排序功能
        renderCell: (row) => fDateTime(row.created_at, 'YYYY/MM/DD HH:mm'),
      },
      {
        id: 'group',
        label: t('wallet:label_group'),
        width: 120,
        minWidth: 100,
        noWrap: true,
        sortable: false,
        renderCell: (row) =>
          row.group ? (
            <Chip label={row.group} size="small" variant="soft" color="primary" />
          ) : (
            <Box sx={{ color: 'text.secondary' }}>-</Box>
          ),
      },
      {
        id: 'remark',
        label: t('wallet:label_remark'),
        width: 300,
        minWidth: 200,
        noWrap: true,
        renderCell: (row) => (
          <Box
            sx={{
              color: row.Remark ? 'text.primary' : 'text.secondary',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {row.remark || '-'}
          </Box>
        ),
      },
      {
        id: 'action',
        label: t('common:action.action'),
        width: 120,
        minWidth: 120,
        align: 'center',
        renderCell: (row) => (
          <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
            <Tooltip title={t('common:action.edit')}>
              <IconButton
                size="small"
                color="primary"
                onClick={(e) => handleEdit(row, e)}
                sx={{
                  '&:hover': { backgroundColor: 'primary.lighter' },
                }}
              >
                <Iconify icon="solar:pen-bold" width={16} />
              </IconButton>
            </Tooltip>
            <Tooltip title={t('common:action.delete')}>
              <IconButton
                size="small"
                color="error"
                onClick={(e) => handleDelete(row, e)}
                sx={{
                  '&:hover': { backgroundColor: 'error.lighter' },
                }}
              >
                <Iconify icon="solar:trash-bin-trash-bold" width={16} />
              </IconButton>
            </Tooltip>
          </Box>
        ),
      },
    ],
    [t, currentSearchTerm, handleEdit, handleDelete]
  );

  return (
    <>
      <TableView
        tableId="wallet-list"
        ref={tableRef}
        columns={columns}
        getData={getData}
        onDelete={handleBatchDelete}
      />

      {/* Edit Dialog */}
      {editDialog.value && (
        <WalletEditDialog
          open={editDialog.value}
          onClose={editDialog.onFalse}
          wallet={selectedWallet}
          onSuccess={handleEditComplete}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={deleteDialog.value}
        onClose={deleteDialog.onFalse}
        title={t('common:action.delete')}
        content={
          <>
            {t('common:tips.deleteConfirm')}
            <Box component="span" sx={{ fontWeight: 'bold' }}>
              {selectedWallet?.Name || `ID: ${selectedWallet?.ID}`}
            </Box>
            ?
          </>
        }
        action={
          <Button variant="contained" color="inherit" onClick={confirmDelete} autoFocus>
            {t('common:action.delete')}
          </Button>
        }
      />
    </>
  );
});
