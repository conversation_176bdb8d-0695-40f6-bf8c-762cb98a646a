import { useState } from 'react';

// @mui
import {
  Box,
  Stack,
  Dialog,
  Button,
  Select,
  MenuItem,
  TextField,
  CardHeader,
  Typography,
  InputLabel,
  IconButton,
  FormControl,
  DialogActions,
  DialogContent,
  CircularProgress,
} from '@mui/material';

import { useTranslate } from 'src/locales';
import { EthWallet } from 'src/lib/wallet/eth';
import { Wallet, WalletService } from 'src/bindings';
import { generateMnemonic, validateMnemonic } from 'src/lib/wallet/wallet-manager.js';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

export function WalletNewDialog({ open, onClose, onSuccess }) {
  const { t } = useTranslate();
  const MAX_WALLET_COUNT = 100;
  const DEFAULT_WALLET_COUNT = 10;
  const DEFAULT_MNEMONIC_LENGTH = 128;

  const [mnemonicLength, setMnemonicLength] = useState(DEFAULT_MNEMONIC_LENGTH);
  const [walletCount, setWalletCount] = useState(DEFAULT_WALLET_COUNT);
  const [walletCountError, setWalletCountError] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState('');

  // 重置表单状态
  const resetForm = () => {
    setMnemonicLength(DEFAULT_MNEMONIC_LENGTH);
    setWalletCount(DEFAULT_WALLET_COUNT);
    setWalletCountError(false);
    setIsGenerating(false);
    setError('');
  };

  // 处理对话框关闭
  const handleClose = () => {
    if (!isGenerating) {
      resetForm();
      onClose();
    }
  };

  const handleMnemonicLengthChange = (event) => {
    setMnemonicLength(event.target.value);
  };

  const handleWalletCountChange = (event) => {
    let value = parseInt(event.target.value, 10);

    // 清空时设为1
    if (isNaN(value) || value < 1) {
      value = 1;
      setWalletCountError(false);
    } else if (value > MAX_WALLET_COUNT) {
      setWalletCountError(true);
    } else {
      setWalletCountError(false);
    }

    setWalletCount(value);
  };

  const handleGenerateWallets = async () => {
    if (!mnemonicLength || !walletCount || walletCountError) return;

    setIsGenerating(true);
    setError('');
    const totalCount = parseInt(walletCount, 10);

    try {
      // 生成助记词
      const mnemonics = generateMnemonic(mnemonicLength, totalCount);
      console.log('Generated mnemonics count:', Array.isArray(mnemonics) ? mnemonics.length : 1);

      // 保证 mnemonics 是数组
      const mnemonicArray = Array.isArray(mnemonics) ? mnemonics : [mnemonics];

      // 验证所有助记词
      for (let i = 0; i < mnemonicArray.length; i++) {
        if (!validateMnemonic(mnemonicArray[i])) {
          throw new Error(`第 ${i + 1} 个助记词无效: ${mnemonicArray[i]}`);
        }
      }

      // 批量创建钱包
      const wallets = [];
      const ethWallet = new EthWallet();

      for (let i = 0; i < mnemonicArray.length; i++) {
        try {
          const mnemonic = mnemonicArray[i];
          console.log(`Processing wallet ${i + 1}/${totalCount}`);

          // 生成私钥和地址
          const privateKey = await ethWallet.getDerivedPrivateKey(mnemonic, { index: 0 });

          if (!privateKey) {
            throw new Error(`无法生成私钥 for wallet ${i + 1}`);
          }

          const address = await ethWallet.getNewAddress({ privateKey });

          if (!address) {
            throw new Error(`无法生成地址 for wallet ${i + 1}`);
          }

          console.log(`Wallet ${i + 1} - Address:`, address, 'PrivateKey:', privateKey);

          // 创建钱包对象
          const wallet = new Wallet({
            Name: `Wallet-${Date.now()}-${i + 1}`,
            Mnemonic: mnemonic,
            Group: 'Generated',
            Remark: `Auto generated wallet ${i + 1}/${totalCount}`,
          });

          wallets.push(wallet);
        } catch (walletError) {
          console.error(`生成第 ${i + 1} 个钱包失败:`, walletError);
          throw new Error(`生成第 ${i + 1} 个钱包失败: ${walletError.message}`);
        }
      }

      // 使用 BatchCreate 方法批量创建钱包
      console.log('Creating wallets in database...');
      await WalletService.BatchCreate(wallets);

      // 显示成功消息
      toast.success(t('wallet:createSuccess', { count: totalCount }));

      // 通知父组件数据已更新
      if (onSuccess) {
        onSuccess();
      }

      // 关闭对话框
      handleClose();
    } catch (err) {
      const errorMsg = err.message || '生成钱包失败';
      setError(errorMsg);
      toast.error(errorMsg);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={!isGenerating ? handleClose : undefined}
      maxWidth="md"
      fullWidth
      disableEscapeKeyDown={isGenerating}
    >
      <CardHeader
        title={t('wallet:generate_title')}
        subheader={t('wallet:generate_tip')}
        action={
          <IconButton aria-label="关闭" onClick={handleClose} disabled={isGenerating}>
            <Iconify icon="solar:close-circle-bold" />
          </IconButton>
        }
      />

      <DialogContent sx={{ pt: 3 }}>
        <Stack spacing={4}>
          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
            <FormControl fullWidth>
              <InputLabel id="mnemonic-length-label">
                {t('wallet:choice_mnemonic_length')}
              </InputLabel>
              <Select
                labelId="mnemonic-length-label"
                value={mnemonicLength}
                label={t('wallet:choice_mnemonic_length')}
                onChange={handleMnemonicLengthChange}
                disabled={isGenerating}
              >
                <MenuItem value={128}>12 {t('wallet:words')}</MenuItem>
                <MenuItem value={160}>15 {t('wallet:words')}</MenuItem>
                <MenuItem value={192}>18 {t('wallet:words')}</MenuItem>
                <MenuItem value={224}>21 {t('wallet:words')}</MenuItem>
                <MenuItem value={256}>24 {t('wallet:words')}</MenuItem>
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <TextField
                label={t('wallet:generate_wallet_count')}
                type="number"
                value={walletCount}
                onChange={handleWalletCountChange}
                error={walletCountError}
                helperText={
                  walletCountError
                    ? t('wallet:maxWalletCountError', { max: MAX_WALLET_COUNT })
                    : t('wallet:walletCountHelper', { max: MAX_WALLET_COUNT })
                }
                disabled={isGenerating}
                slotProps={{
                  input: {
                    min: 1,
                    max: MAX_WALLET_COUNT,
                  },
                }}
              />
            </FormControl>
          </Box>

          {/* 错误信息显示 */}
          {error && (
            <Box
              sx={{
                p: 2,
                bgcolor: 'error.lighter',
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'error.main',
              }}
            >
              <Typography variant="body2" color="error.main">
                {error}
              </Typography>
            </Box>
          )}
        </Stack>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={handleClose} color="inherit" disabled={isGenerating}>
          {t('common:action.cancel')}
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={handleGenerateWallets}
          disabled={!mnemonicLength || !walletCount || walletCountError || isGenerating}
          startIcon={
            isGenerating ? (
              <CircularProgress size={20} color="inherit" />
            ) : (
              <Iconify icon="hugeicons:wallet-02" />
            )
          }
        >
          {isGenerating ? t('wallet:generating') : t('wallet:immediate_generation')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
