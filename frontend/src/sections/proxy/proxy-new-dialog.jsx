import { useState } from 'react';

import {
  Box,
  Dialog,
  Button,
  TextField,
  CardHeader,
  IconButton,
  DialogContent,
  DialogActions,
  CircularProgress,
} from '@mui/material';

import { useTranslate } from 'src/locales';

import { Iconify } from 'src/components/iconify';

export function ProxyNewDialog({ open, onClose }) {
  const { t } = useTranslate('wallet');

  const [isImporting, setIsImporting] = useState(false);
  const [content, setContent] = useState('');

  const handleContentChange = (event) => {
    setContent(event.target.value);
  };

  const handleImport = () => {
    setIsImporting(true);
  };

  return (
    <Dialog open={open} onClose={null} disableEscapeKeyDown fullWidth maxWidth="md">
      <CardHeader
        title={t('proxy:dialog.import_title')}
        subheader={t('proxy:dialog.import_subheader')}
        action={
          <IconButton aria-label="关闭" onClick={onClose}>
            <Iconify icon="solar:close-circle-bold" />
          </IconButton>
        }
      />

      <DialogContent>
        <Box>
          <TextField
            fullWidth
            multiline
            rows={4}
            minRows={4}
            maxRows={12}
            placeholder={t('proxy:dialog.import_subheader')}
            sx={{
              '& .MuiInputBase-input': {
                resize: 'vertical',
              },
            }}
            value={content}
            onChange={handleContentChange}
          />
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button
          variant="contained"
          color="primary"
          onClick={handleImport}
          startIcon={
            isImporting ? (
              <CircularProgress size={10} color="inherit" />
            ) : (
              <Iconify icon="mingcute:arrow-down-circle-fill" />
            )
          }
        >
          {isImporting ? t('common:action.importing') : t('common:action.import')}
        </Button>

        <Button onClick={onClose} color="inherit" variant="contained">
          {t('common:action.cancel')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
