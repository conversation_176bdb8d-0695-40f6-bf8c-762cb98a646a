import { useBoolean } from 'minimal-shared/hooks';
import { useRef, useMemo, useState, forwardRef, useCallback, useImperativeHandle } from 'react';

import { Box, Chip, Button, Tooltip, IconButton } from '@mui/material';

import { fDateTime } from 'src/utils/format-time';
import { highlightText } from 'src/utils/highlight-text';

import { useTranslate } from 'src/locales';
import { ProxyService } from 'src/bindings';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { TableView } from 'src/components/table';
import { ConfirmDialog } from 'src/components/custom-dialog';

import { ProxyEditDialog } from './proxy-edit-dialog';

export const ListView = forwardRef(function ListView(props, ref) {
  const { t } = useTranslate();
  const [selectedProxy, setSelectedProxy] = useState(null);
  const [currentSearchTerm, setCurrentSearchTerm] = useState('');
  const tableRef = useRef(null);

  const editDialog = useBoolean(false);
  const deleteDialog = useBoolean(false);

  // 获取代理数据
  const getData = useCallback(
    async (page, pageSize, orderBy, order, searchValue) => {
      try {
        // 更新当前搜索词状态，用于高亮显示
        setCurrentSearchTerm(searchValue?.trim() || '');

        let result;

        if (searchValue && searchValue.trim()) {
          // 有搜索词时使用 SearchPageList，在 Name 和 IPAddress 字段中进行 OR 查询
          const searchFields = ['Name', 'IPAddress'];
          result = await ProxyService.SearchPageList(
            page + 1, // 后端从1开始
            pageSize,
            searchValue.trim(),
            searchFields
          );
        } else {
          // 无搜索词时使用 PageList
          result = await ProxyService.PageList(page + 1, pageSize);
        }

        return [result.data || [], result.total || 0];
      } catch (error) {
        console.error('获取代理数据失败:', error);
        toast.error(t('common:message.loadFailed'));
        return [[], 0];
      }
    },
    [t]
  );

  // 处理编辑
  const handleEdit = useCallback(
    (proxy, e) => {
      e.stopPropagation();
      setSelectedProxy(proxy);
      editDialog.onTrue();
    },
    [editDialog]
  );

  // 处理删除
  const handleDelete = useCallback(
    (proxy, e) => {
      e.stopPropagation();
      setSelectedProxy(proxy);
      deleteDialog.onTrue();
    },
    [deleteDialog]
  );

  // 确认删除单个代理
  const confirmDelete = async () => {
    if (!selectedProxy) return;

    try {
      await ProxyService.DeleteByID(selectedProxy.id || selectedProxy.ID);
      toast.success(t('common:message.deleteSuccess'));
      tableRef.current?.refetch(); // 刷新列表
      deleteDialog.onFalse();
      setSelectedProxy(null);
    } catch (error) {
      console.error('删除代理失败:', error);
      toast.error(t('common:message.deleteFailed'));
    }
  };

  // 批量删除代理
  const handleBatchDelete = useCallback(
    async (selectedIds) => {
      if (!selectedIds || selectedIds.length === 0) return;

      try {
        // 使用后端的批量删除方法
        await ProxyService.BatchDelete(selectedIds);
        toast.success(t('common:message.deleteSuccess', { count: selectedIds.length }));
        tableRef.current?.refetch(); // 刷新列表
      } catch (error) {
        console.error('批量删除代理失败:', error);
        toast.error(t('common:message.deleteFailed'));
      }
    },
    [t]
  );

  // 处理编辑完成
  const handleEditComplete = () => {
    editDialog.onFalse();
    setSelectedProxy(null);
    tableRef.current?.refetch(); // 刷新列表
  };

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    refetch: () => tableRef.current?.refetch(),
  }));

  // 表格列定义 - 使用 useMemo 优化性能并支持高亮搜索词
  const columns = useMemo(
    () => [
      {
        id: 'id',
        label: 'ID',
        width: 80,
        minWidth: 80,
        align: 'center',
        sortable: true, // 启用排序功能
      },
      {
        id: 'name',
        label: t('proxy:label_name'),
        width: 230,
        minWidth: 230,
        noWrap: true,
        sortable: true,
        renderCell: (row) => (
          <Box sx={{ fontWeight: 500 }}>
            {highlightText(row.Name || row.name || '-', currentSearchTerm)}
          </Box>
        ),
      },
      {
        id: 'IPAddress',
        label: t('proxy:label_ip'),
        width: 180,
        minWidth: 180,
        noWrap: true,
        renderCell: (row) => (
          <Box sx={{ fontWeight: 500 }}>
            {highlightText(row.IPAddress || '-', currentSearchTerm)}
          </Box>
        ),
      },
      {
        id: 'Port',
        label: t('proxy:label_port'),
        width: 100,
        minWidth: 100,
        align: 'center',
        renderCell: (row) => row.Port || '-',
      },
      {
        id: 'Protocol',
        label: t('proxy:label_protocol'),
        width: 120,
        minWidth: 120,
        renderCell: (row) =>
          row.Protocol ? (
            <Chip label={row.Protocol} size="small" variant="soft" color="primary" />
          ) : (
            <Box sx={{ color: 'text.secondary' }}>-</Box>
          ),
      },
      {
        id: 'Country',
        label: t('proxy:label_country'),
        width: 120,
        minWidth: 120,
        renderCell: (row) => row.Country || '-',
      },
      {
        id: 'created_at',
        label: t('common:createdAt'),
        width: 160,
        minWidth: 160,
        noWrap: true,
        sortable: true,
        renderCell: (row) => fDateTime(row.CreatedAt || row.created_at, 'YYYY/MM/DD HH:mm'),
      },
      {
        id: 'Remark',
        label: t('proxy:label_remark'),
        width: 300,
        minWidth: 200,
        noWrap: true,
        renderCell: (row) => (
          <Box
            sx={{
              color: row.Remark ? 'text.primary' : 'text.secondary',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {row.Remark || '-'}
          </Box>
        ),
      },
      {
        id: 'action',
        label: t('common:action.action'),
        width: 120,
        minWidth: 120,
        align: 'center',
        renderCell: (row) => (
          <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
            <Tooltip title={t('common:action.edit')}>
              <IconButton
                size="small"
                color="primary"
                onClick={(e) => handleEdit(row, e)}
                sx={{
                  '&:hover': { backgroundColor: 'primary.lighter' },
                }}
              >
                <Iconify icon="solar:pen-bold" width={16} />
              </IconButton>
            </Tooltip>
            <Tooltip title={t('common:action.delete')}>
              <IconButton
                size="small"
                color="error"
                onClick={(e) => handleDelete(row, e)}
                sx={{
                  '&:hover': { backgroundColor: 'error.lighter' },
                }}
              >
                <Iconify icon="solar:trash-bin-trash-bold" width={16} />
              </IconButton>
            </Tooltip>
          </Box>
        ),
      },
    ],
    [t, currentSearchTerm, handleEdit, handleDelete]
  );

  return (
    <>
      <TableView
        tableId="proxy-list"
        ref={tableRef}
        columns={columns}
        getData={getData}
        onDelete={handleBatchDelete}
      />

      {/* Edit Dialog */}
      {editDialog.value && (
        <ProxyEditDialog
          open={editDialog.value}
          onClose={editDialog.onFalse}
          proxy={selectedProxy}
          onSuccess={handleEditComplete}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={deleteDialog.value}
        onClose={deleteDialog.onFalse}
        title={t('common:action.delete')}
        content={
          <>
            {t('common:tips.deleteConfirm')}
            <Box component="span" sx={{ fontWeight: 'bold' }}>
              {selectedProxy?.Name ||
                selectedProxy?.name ||
                `ID: ${selectedProxy?.id || selectedProxy?.ID}`}
            </Box>
            ?
          </>
        }
        action={
          <Button variant="contained" color="inherit" onClick={confirmDelete} autoFocus>
            {t('common:action.delete')}
          </Button>
        }
      />
    </>
  );
});
