import { useState, useEffect } from 'react';

import {
  Box,
  Stack,
  Dialog,
  Button,
  MenuItem,
  TextField,
  CardHeader,
  IconButton,
  DialogActions,
  DialogContent,
} from '@mui/material';

import { useTranslate } from 'src/locales';
import { Proxy, ProxyService } from 'src/bindings';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

export function ProxyEditDialog({ open, onClose, proxy, onSuccess }) {
  const { t } = useTranslate();

  const [formData, setFormData] = useState({
    Name: '',
    IPAddress: '',
    Port: '',
    Protocol: 'HTTP',
    Username: '',
    Password: '',
    Country: '',
    Remark: '',
  });
  const [loading, setLoading] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    if (proxy) {
      setFormData({
        Name: proxy.Name || '',
        IPAddress: proxy.IPAddress || '',
        Port: proxy.Port || '',
        Protocol: proxy.Protocol || 'HTTP',
        Username: proxy.Username || '',
        Password: proxy.Password || '',
        Country: proxy.Country || '',
        Remark: proxy.Remark || '',
      });
    }
  }, [proxy]);

  // 处理表单输入
  const handleInputChange = (field) => (event) => {
    setFormData((prev) => ({
      ...prev,
      [field]: event.target.value,
    }));
  };

  // 处理提交
  const handleSubmit = async () => {
    if (!formData.Name.trim()) {
      toast.error(t('proxy:validation.nameRequired'));
      return;
    }

    if (!formData.IPAddress.trim()) {
      toast.error(t('proxy:validation.ipRequired'));
      return;
    }

    if (!formData.Port || formData.Port <= 0) {
      toast.error(t('proxy:validation.portRequired'));
      return;
    }

    setLoading(true);
    try {
      if (proxy) {
        // 更新现有代理
        const updatedProxy = Proxy.createFrom({
          ...proxy,
          ...formData,
          Port: parseInt(formData.Port, 10),
        });
        await ProxyService.Update(updatedProxy);
        toast.success(t('common:message.updateSuccess'));
      } else {
        // 创建新代理
        const newProxy = Proxy.createFrom({
          ...formData,
          Port: parseInt(formData.Port, 10),
        });
        await ProxyService.Create(newProxy);
        toast.success(t('common:message.createSuccess'));
      }

      onSuccess?.();
    } catch (error) {
      console.error('保存代理失败:', error);
      toast.error(proxy ? t('common:message.updateFailed') : t('common:message.createFailed'));
    } finally {
      setLoading(false);
    }
  };

  const protocolOptions = [
    { value: 'HTTP', label: 'HTTP' },
    { value: 'HTTPS', label: 'HTTPS' },
    { value: 'SOCKS4', label: 'SOCKS4' },
    { value: 'SOCKS5', label: 'SOCKS5' },
  ];

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <CardHeader
        title={proxy ? t('proxy:dialog.editTitle') : t('proxy:dialog.createTitle')}
        action={
          <IconButton onClick={onClose}>
            <Iconify icon="mingcute:close-line" />
          </IconButton>
        }
      />

      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          <TextField
            label={t('proxy:label_name')}
            value={formData.Name}
            onChange={handleInputChange('Name')}
            fullWidth
            required
          />

          <Box sx={{ display: 'flex', gap: 2 }}>
            <TextField
              label={t('proxy:label_ip')}
              value={formData.IPAddress}
              onChange={handleInputChange('IPAddress')}
              fullWidth
              required
              placeholder="***********"
            />
            <TextField
              label={t('proxy:label_port')}
              value={formData.Port}
              onChange={handleInputChange('Port')}
              type="number"
              inputProps={{ min: 1, max: 65535 }}
              sx={{ minWidth: 120 }}
              required
            />
          </Box>

          <TextField
            label={t('proxy:label_protocol')}
            value={formData.Protocol}
            onChange={handleInputChange('Protocol')}
            select
            fullWidth
          >
            {protocolOptions.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </TextField>

          <Box sx={{ display: 'flex', gap: 2 }}>
            <TextField
              label={t('proxy:label_username')}
              value={formData.Username}
              onChange={handleInputChange('Username')}
              fullWidth
            />
            <TextField
              label={t('proxy:label_password')}
              value={formData.Password}
              onChange={handleInputChange('Password')}
              type="password"
              fullWidth
            />
          </Box>

          <TextField
            label={t('proxy:label_country')}
            value={formData.Country}
            onChange={handleInputChange('Country')}
            fullWidth
            placeholder="US"
          />

          <TextField
            label={t('proxy:label_remark')}
            value={formData.Remark}
            onChange={handleInputChange('Remark')}
            fullWidth
            multiline
            rows={3}
          />
        </Stack>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} color="inherit">
          {t('common:action.cancel')}
        </Button>
        <Button onClick={handleSubmit} variant="contained" loading={loading}>
          {proxy ? t('common:action.update') : t('common:action.create')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
