import PropTypes from 'prop-types';
import { useNavigate } from 'react-router';

import Button from '@mui/material/Button';

import { useAppSettings } from 'src/hooks/use-app-settings';

import { Iconify } from 'src/components/iconify';
import { lockScreen, shouldEnableLockScreen } from 'src/components/auto-lock/lock-guard';

// ----------------------------------------------------------------------

export function LockScreenButton({ onClose, sx, ...other }) {
  const navigate = useNavigate();
  const { settings } = useAppSettings();

  // 如果不应该启用锁屏功能，不渲染按钮
  if (!shouldEnableLockScreen(settings)) {
    return null;
  }

  const handleClick = () => {
    // 先执行关闭回调（如果有）
    if (onClose) {
      onClose();
    }

    // 使用锁屏机制锁定屏幕
    lockScreen(navigate, window.location.pathname);
  };

  return (
    <Button
      fullWidth
      color="inherit"
      variant="text"
      onClick={handleClick}
      startIcon={<Iconify icon="eva:lock-fill" />}
      sx={{
        height: 44,
        ...sx,
      }}
      {...other}
    >
      锁定屏幕
    </Button>
  );
}

LockScreenButton.propTypes = {
  onClose: PropTypes.func,
  sx: PropTypes.object,
};
