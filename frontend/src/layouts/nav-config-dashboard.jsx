import { paths } from 'src/routes/paths';

import { CONFIG } from 'src/global-config';
import { useTranslate } from 'src/locales';

import { SvgColor } from 'src/components/svg-color';

// ----------------------------------------------------------------------

//
const icon = (name) => <SvgColor src={`${CONFIG.assetsDir}/assets/icons/navbar/${name}.svg`} />;

const ICONS = {
  wallet: icon('ic-wallet'),
  email: icon('ic-mail'),
  twitter: icon('ic-twitter'),
  discord: icon('ic-discord'),
  telegram: icon('ic-telegram'),
  proxy: icon('ic-ip'),
  account: icon('ic-account'),
  profile: icon('ic-chrome-reader-mode'),
  settings: icon('ic-settings'),
};

// ----------------------------------------------------------------------
/**
 *
 * Input nav data is an array of navigation section items used to define the structure and content of a navigation bar.
 * Each section contains a subheader and an array of items, which can include nested children items.
 *
 * Each item can have the following properties:
 * - `title`: The title of the navigation item.
 * - `path`: The URL path the item links to.
 * - `icon`: An optional icon component to display alongside the title.
 * - `info`: Optional additional information to display, such as a label.
 * - `allowedRoles`: An optional array of roles that are allowed to see the item.
 * - `caption`: An optional caption to display below the title.
 * - `children`: An optional array of nested navigation items.
 * - `disabled`: An optional boolean to disable the item.
 */

const createNavData = (t) => [
  /**
   * Overview
   */
  {
    subheader: t('navbar:environment'),
    items: [
      {
        title: t('navbar:wallet'),
        path: paths.wallet.root,
        icon: ICONS.wallet,
      },
      {
        title: t('navbar:account'),
        path: paths.account.root,
        icon: ICONS.account,
        matchPrefixForActiveState: true,
      },
      {
        title: t('navbar:proxy'),
        path: paths.proxy.root,
        icon: ICONS.proxy,
      },
      {
        title: t('navbar:profile'),
        path: paths.profile.root,
        icon: ICONS.profile,
      },
    ],
  },
  /**
   * Management
   */
  {
    subheader: t('navbar:task'),
    items: [],
  },
  {
    subheader: t('navbar:tool'),
    items: [],
  },
];

// ----------------------------------------------------------------------

export function useNavData() {
  const { t } = useTranslate('navbar');
  return createNavData(t);
}
