import { _mock } from '../_mock';

// ----------------------------------------------------------------------

export const countries = [
  {
    timezones: ['America/Aruba'],
    latlng: [12.5, -69.96666666],
    name: 'Aruba',
    country_code: 'AW',
    capital: 'Oranjestad',
    photoUrl: _mock.image.cover(1),
  },
  {
    timezones: ['Asia/Kabul'],
    latlng: [33, 65],
    name: 'Afghanistan',
    country_code: 'AF',
    capital: 'Kabul',
    photoUrl: _mock.image.cover(2),
  },
  {
    timezones: ['Africa/Luanda'],
    latlng: [-12.5, 18.5],
    name: 'Angola',
    country_code: 'AO',
    capital: 'Luanda',
    photoUrl: _mock.image.cover(3),
  },
  {
    timezones: ['Pacific/Efate'],
    latlng: [-16, 167],
    name: 'Vanuatu',
    country_code: 'VU',
    capital: 'Port Vila',
    photoUrl: _mock.image.cover(4),
  },
  {
    timezones: ['Pacific/Wallis'],
    latlng: [-13.3, -176.2],
    name: 'Wallis and Futuna',
    country_code: 'WF',
    capital: 'Mata-Utu',
    photoUrl: _mock.image.cover(5),
  },
  {
    timezones: ['Pacific/Apia'],
    latlng: [-13.58333333, -172.33333333],
    name: 'Samoa',
    country_code: 'WS',
    capital: 'Apia',
    photoUrl: _mock.image.cover(6),
  },
  {
    timezones: ['Asia/Aden'],
    latlng: [15, 48],
    name: 'Yemen',
    country_code: 'YE',
    capital: "Sana'a",
    photoUrl: _mock.image.cover(7),
  },
  {
    timezones: ['Africa/Johannesburg'],
    latlng: [-29, 24],
    name: 'South Africa',
    country_code: 'ZA',
    capital: 'Pretoria',
    photoUrl: _mock.image.cover(8),
  },
  {
    timezones: ['Africa/Lusaka'],
    latlng: [-15, 30],
    name: 'Zambia',
    country_code: 'ZM',
    capital: 'Lusaka',
    photoUrl: _mock.image.cover(9),
  },
  {
    timezones: ['Africa/Harare'],
    latlng: [-20, 30],
    name: 'Zimbabwe',
    country_code: 'ZW',
    capital: 'Harare',
    photoUrl: _mock.image.cover(10),
  },
];
