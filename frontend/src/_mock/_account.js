import { _mock } from './_mock';

export const _proxyList = Array.from({ length: 20 }, (_, index) => ({
  id: index + 1,
  name: _mock.fullName(index) + ' ' + _mock.fullName(index) + ' ' + _mock.fullName(index),
  ip_location: _mock.countryNames(index),
  group: _mock.companyNames(index),
  created_at: _mock.time(index),
  status:
    (index % 2 && 'pending') || (index % 3 && 'banned') || (index % 4 && 'rejected') || 'active',
}));

export const _mailList = Array.from({ length: 20 }, (_, index) => ({
  id: index + 1,
  account: _mock.email(index),
  password: _mock.email(index),
  group: _mock.companyNames(index),
  label: _mock.role(index),
  created_at: _mock.time(index),
  status:
    (index % 2 && 'pending') || (index % 3 && 'banned') || (index % 4 && 'rejected') || 'active',
}));
