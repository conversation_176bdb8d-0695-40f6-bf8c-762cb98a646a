import { useLocalStorage } from 'minimal-shared/hooks';
import { useState, useEffect, useCallback } from 'react';

// 导入AutoLock重置函数
import { resetAutoLock } from 'src/components/auto-lock/index.jsx';

import * as AppPasswordService from '../../bindings/a8.tools/backend/services/keyring/apppasswordservice.js';

// 应用设置的存储key
const APP_SETTINGS_STORAGE_KEY = 'app-settings-data';
const APP_PASSWORD_KEY = 'app-passcode';

// 默认应用设置（移除passcode字段，改用AppPasswordService管理）
const defaultAppSettings = {
  app: {
    isInited: false,
  },
  security: {
    autoLock: -1,
    // passcode 字段移除，由AppPasswordService管理
  },
  backup: {
    dataPath: '/default/path',
  },
  about: {
    version: '1.0.0',
  },
  // 版本号，用于数据迁移
  version: '1.0.0',
};

/**
 * 应用设置管理hook
 * 使用localStorage进行本地持久化，密码使用AppPasswordService安全存储
 */
export function useAppSettings() {
  const {
    state: settings,
    setState,
    setField,
  } = useLocalStorage(APP_SETTINGS_STORAGE_KEY, defaultAppSettings);

  const [hasPassword, setHasPassword] = useState(false);

  // 刷新密码状态
  const refreshPasswordStatus = useCallback(async () => {
    try {
      const result = await AppPasswordService.HasPassword(APP_PASSWORD_KEY);
      setHasPassword(result);
      return result;
    } catch (error) {
      console.error('检查密码状态失败:', error);
      setHasPassword(false);
      return false;
    }
  }, []);

  // 检查是否设置了密码
  useEffect(() => {
    refreshPasswordStatus();
  }, [refreshPasswordStatus]);

  /**
   * 设置密码
   * @param {string} password - 新密码
   */
  const setPassword = useCallback(
    async (password) => {
      // 参数验证
      if (!password || typeof password !== 'string' || password.trim() === '') {
        console.error('密码参数无效');
        return false;
      }

      try {
        await AppPasswordService.SetPassword(APP_PASSWORD_KEY, password);
        // 只有成功后才更新状态
        setHasPassword(true);
        return true;
      } catch (error) {
        console.error('设置密码失败:', error);
        // 确保状态正确，重新检查密码状态
        await refreshPasswordStatus();
        return false;
      }
    },
    [refreshPasswordStatus]
  );

  /**
   * 验证密码
   * @param {string} password - 要验证的密码
   */
  const verifyPassword = useCallback(async (password) => {
    // 参数验证
    if (!password || typeof password !== 'string') {
      console.error('密码参数无效');
      return false;
    }

    try {
      const result = await AppPasswordService.VerifyPassword(APP_PASSWORD_KEY, password);
      return result;
    } catch (error) {
      console.error('验证密码失败:', error);
      return false;
    }
  }, []);

  /**
   * 清除密码
   */
  const clearPassword = useCallback(async () => {
    try {
      await AppPasswordService.ClearPassword(APP_PASSWORD_KEY);
      // 只有成功后才更新状态
      setHasPassword(false);
      return true;
    } catch (error) {
      console.error('清除密码失败:', error);
      // 确保状态正确，重新检查密码状态
      await refreshPasswordStatus();
      return false;
    }
  }, [refreshPasswordStatus]);

  /**
   * 更新设置值
   * @param {string} key - 设置键，支持嵌套路径如 'security.autoLock'
   * @param {any} value - 设置值
   */
  const updateSetting = useCallback(
    (key, value) => {
      // 支持嵌套路径更新
      if (key.includes('.')) {
        const keys = key.split('.');
        const newSettings = { ...settings };

        // 深度更新嵌套对象
        let current = newSettings;
        for (let i = 0; i < keys.length - 1; i += 1) {
          const currentKey = keys[i];
          if (!current[currentKey] || typeof current[currentKey] !== 'object') {
            current[currentKey] = {};
          }
          current[currentKey] = { ...current[currentKey] };
          current = current[currentKey];
        }

        // 设置最终值
        current[keys[keys.length - 1]] = value;
        setState(newSettings);
      } else {
        // 直接更新顶级属性
        setField(key, value);
      }

      // 如果更新了自动锁屏设置，重置AutoLock组件
      if (key === 'security.autoLock') {
        resetAutoLock();
      }
    },
    [settings, setState, setField]
  );

  /**
   * 批量更新设置
   * @param {object} updates - 要更新的设置对象
   */
  const updateSettings = useCallback(
    (updates) => {
      setState((prev) => ({
        ...prev,
        ...updates,
      }));
    },
    [setState]
  );

  /**
   * 重置设置到默认值
   */
  const resetSettings = useCallback(() => {
    setState(defaultAppSettings);
  }, [setState]);

  /**
   * 获取特定设置值
   * @param {string} key - 设置键，支持嵌套路径
   * @param {any} defaultValue - 默认值
   */
  const getSetting = useCallback(
    (key, defaultValue = null) => {
      if (key.includes('.')) {
        const keys = key.split('.');
        let current = settings;

        for (const currentKey of keys) {
          if (current && typeof current === 'object' && currentKey in current) {
            current = current[currentKey];
          } else {
            return defaultValue;
          }
        }

        return current;
      }

      return settings[key] ?? defaultValue;
    },
    [settings]
  );

  return {
    settings,
    updateSetting,
    updateSettings,
    resetSettings,
    getSetting,
    // 密码相关方法
    hasPassword,
    setPassword,
    verifyPassword,
    clearPassword,
    refreshPasswordStatus,
  };
}
