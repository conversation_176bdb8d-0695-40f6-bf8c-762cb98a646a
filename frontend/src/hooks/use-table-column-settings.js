import { useMemo, useState, useCallback } from 'react';

const STORAGE_KEY = 'table-column-settings';
const CONFIG_VERSION = 1;

/**
 * 表格列设置持久化Hook
 * @param {string} tableId - 表格唯一标识符
 * @param {Array} defaultColumns - 默认列配置数组
 * @returns {Object} 列设置管理对象
 */
export function useTableColumnSettings(tableId, defaultColumns = []) {
  // 直接使用 useState 和 localStorage API
  const [settings, setSettingsState] = useState(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      const parsed = saved ? JSON.parse(saved) : {};
      if (process.env.NODE_ENV === 'development') {
        console.log(`[TableColumnSettings] 初始化读取localStorage:`, saved, parsed);
      }
      return parsed;
    } catch (error) {
      console.error('[TableColumnSettings] 读取localStorage失败:', error);
      return {};
    }
  });

  // 保存到localStorage的函数
  const saveToLocalStorage = useCallback((newSettings) => {
    try {
      const jsonString = JSON.stringify(newSettings);
      localStorage.setItem(STORAGE_KEY, jsonString);

      if (process.env.NODE_ENV === 'development') {
        console.log(`[TableColumnSettings] 保存到localStorage成功:`, newSettings);
        console.log(`[TableColumnSettings] 保存的JSON字符串:`, jsonString);
      }

      // 立即同步更新React状态
      setSettingsState(newSettings);
      return true;
    } catch (error) {
      console.error('[TableColumnSettings] 保存到localStorage失败:', error);
      return false;
    }
  }, []);

  // 添加强制更新状态
  const [forceUpdateKey, setForceUpdateKey] = useState(0);
  const forceUpdate = useCallback(() => {
    setForceUpdateKey((prev) => prev + 1);
  }, []);

  // 获取当前表格的设置
  const tableKey = useMemo(() => (tableId ? `table-${tableId}` : null), [tableId]);
  const currentTableSettings = useMemo(
    () => (tableKey ? settings[tableKey] : null),
    [settings, tableKey, forceUpdateKey]
  );

  // 在开发环境添加调试日志
  if (process.env.NODE_ENV === 'development' && tableId) {
    console.log(`[TableColumnSettings] tableId: ${tableId}, settings:`, currentTableSettings);
    console.log(`[TableColumnSettings] 完整localStorage数据:`, settings);
    console.log(`[TableColumnSettings] localStorage原始数据:`, localStorage.getItem(STORAGE_KEY));
  }

  // 初始化默认设置
  const defaultColumnOrder = useMemo(() => defaultColumns.map((col) => col.id), [defaultColumns]);

  const defaultColumnVisibility = useMemo(
    () =>
      defaultColumns.reduce((acc, col) => {
        // 默认显示所有列，除非明确设置为隐藏
        acc[col.id] = col.hidden !== true;
        return acc;
      }, {}),
    [defaultColumns]
  );

  // 合并设置（使用保存的设置或默认设置）
  const columnOrder = useMemo(() => {
    if (currentTableSettings?.columnOrder) {
      // 如果存在保存的顺序，需要验证并合并新的列
      const savedOrder = currentTableSettings.columnOrder;
      const currentColumnIds = new Set(defaultColumnOrder);

      // 过滤掉不存在的列ID
      const validSavedOrder = savedOrder.filter((id) => currentColumnIds.has(id));

      // 添加新的列ID（保存的设置中没有的）
      const newColumnIds = defaultColumnOrder.filter((id) => !savedOrder.includes(id));

      return [...validSavedOrder, ...newColumnIds];
    }
    return defaultColumnOrder;
  }, [currentTableSettings?.columnOrder, defaultColumnOrder, forceUpdateKey]);

  const columnVisibility = useMemo(() => {
    if (currentTableSettings?.columnVisibility) {
      // 合并保存的可见性设置和默认设置
      return {
        ...defaultColumnVisibility,
        ...currentTableSettings.columnVisibility,
      };
    }
    return defaultColumnVisibility;
  }, [currentTableSettings?.columnVisibility, defaultColumnVisibility, forceUpdateKey]);

  // 根据顺序和可见性重新排列列
  const orderedColumns = useMemo(
    () =>
      columnOrder
        .map((columnId) => defaultColumns.find((col) => col.id === columnId))
        .filter(Boolean), // 过滤掉undefined的列
    [columnOrder, defaultColumns, forceUpdateKey]
  );

  // 获取可见的列
  const visibleColumns = useMemo(() => {
    const result = orderedColumns.filter((col) => columnVisibility[col.id] !== false);

    // 在开发环境添加详细调试信息
    if (process.env.NODE_ENV === 'development' && tableId) {
      console.log(`[TableColumnSettings] 计算 visibleColumns:`);
      console.log(
        `[TableColumnSettings] orderedColumns:`,
        orderedColumns.map((col) => col.id)
      );
      console.log(`[TableColumnSettings] columnVisibility:`, columnVisibility);
      console.log(
        `[TableColumnSettings] 过滤结果:`,
        result.map((col) => col.id)
      );

      // 详细检查每一列的可见性
      orderedColumns.forEach((col) => {
        const isVisible = columnVisibility[col.id] !== false;
        console.log(
          `[TableColumnSettings] 列 ${col.id}: visibility=${columnVisibility[col.id]}, isVisible=${isVisible}`
        );
      });
    }

    return result;
  }, [orderedColumns, columnVisibility, forceUpdateKey, tableId]);

  // 更新设置到localStorage
  const updateTableSettings = useCallback(
    (updates) => {
      if (!tableKey) {
        console.warn('Cannot update table settings without tableKey');
        return;
      }

      const newTableSettings = {
        ...currentTableSettings,
        ...updates,
        version: CONFIG_VERSION,
        lastUpdated: Date.now(),
      };

      const newSettings = {
        ...settings,
        [tableKey]: newTableSettings,
      };

      // 在开发环境添加调试日志
      if (process.env.NODE_ENV === 'development') {
        console.log(`[TableColumnSettings] 更新设置 - tableKey: ${tableKey}, updates:`, updates);
        console.log(`[TableColumnSettings] 新设置:`, newTableSettings);
        console.log(`[TableColumnSettings] 将保存到localStorage:`, newSettings);
      }

      const saveSuccess = saveToLocalStorage(newSettings);

      if (saveSuccess) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`[TableColumnSettings] 保存成功，立即触发强制更新`);
        }

        // 立即触发强制更新，确保UI同步更新
        forceUpdate();
      } else {
        console.error('[TableColumnSettings] 保存到localStorage失败');
      }
    },
    [currentTableSettings, saveToLocalStorage, tableKey, forceUpdate, settings]
  );

  // 更新列顺序
  const updateColumnOrder = useCallback(
    (newOrder) => {
      if (!tableKey) {
        console.warn('无法更新列顺序：未提供tableId');
        return;
      }

      const columnOrderArray = newOrder.map((col) => (typeof col === 'string' ? col : col.id));

      // 在开发环境添加调试日志
      if (process.env.NODE_ENV === 'development') {
        console.log(`[TableColumnSettings] 更新列顺序:`, columnOrderArray);
      }

      updateTableSettings({
        columnOrder: columnOrderArray,
      });
    },
    [updateTableSettings, tableKey]
  );

  // 切换列可见性
  const toggleColumnVisibility = useCallback(
    (columnId) => {
      if (!tableKey) {
        console.warn('无法切换列可见性：未提供tableId');
        return;
      }

      const currentVisibility = columnVisibility[columnId];
      const newVisibility = !currentVisibility;

      // 在开发环境添加调试日志
      if (process.env.NODE_ENV === 'development') {
        console.log(
          `[TableColumnSettings] 切换列可见性 - columnId: ${columnId}, 当前: ${currentVisibility} -> 新: ${newVisibility}`
        );
      }

      const newVisibilityObject = {
        ...columnVisibility,
        [columnId]: newVisibility,
      };

      updateTableSettings({
        columnVisibility: newVisibilityObject,
      });
    },
    [columnVisibility, updateTableSettings, tableKey]
  );

  // 设置列可见性
  const setColumnVisibility = useCallback(
    (columnId, visible) => {
      if (!tableKey) {
        console.warn('无法设置列可见性：未提供tableId');
        return;
      }

      const newVisibility = {
        ...columnVisibility,
        [columnId]: visible,
      };

      // 在开发环境添加调试日志
      if (process.env.NODE_ENV === 'development') {
        console.log(
          `[TableColumnSettings] 设置列可见性 - columnId: ${columnId}, visible: ${visible}`
        );
      }

      updateTableSettings({
        columnVisibility: newVisibility,
      });
    },
    [columnVisibility, updateTableSettings, tableKey]
  );

  // 重置到默认设置
  const resetToDefault = useCallback(() => {
    if (!tableKey) {
      console.warn('Cannot reset table settings without tableKey');
      return;
    }

    // 在开发环境添加调试日志
    if (process.env.NODE_ENV === 'development') {
      console.log(`[TableColumnSettings] 重置到默认设置 - tableKey: ${tableKey}`);
    }

    const newSettings = { ...settings };
    delete newSettings[tableKey];
    saveToLocalStorage(newSettings);
  }, [settings, saveToLocalStorage, tableKey]);

  // 检查是否有自定义设置
  const hasCustomSettings = useMemo(
    () => Boolean(currentTableSettings),
    [currentTableSettings, forceUpdateKey]
  );

  // 返回的对象需要在状态更新时重新创建
  const result = useMemo(() => {
    const resultObject = {
      // 状态
      columnOrder,
      columnVisibility,
      orderedColumns,
      visibleColumns,
      hasCustomSettings,

      // 操作方法
      updateColumnOrder,
      toggleColumnVisibility,
      setColumnVisibility,
      resetToDefault,

      // 原始数据（用于调试）
      rawSettings: currentTableSettings,

      // 添加强制更新的键，确保对象引用会改变
      _updateKey: forceUpdateKey,
    };

    // 在开发环境添加调试信息
    if (process.env.NODE_ENV === 'development' && tableId) {
      console.log(`[TableColumnSettings] 返回新的result对象 - updateKey: ${forceUpdateKey}`);
      console.log(
        `[TableColumnSettings] visibleColumns length: ${resultObject.visibleColumns.length}`
      );
      console.log(
        `[TableColumnSettings] orderedColumns length: ${resultObject.orderedColumns.length}`
      );
    }

    return resultObject;
  }, [
    columnOrder,
    columnVisibility,
    orderedColumns,
    visibleColumns,
    hasCustomSettings,
    updateColumnOrder,
    toggleColumnVisibility,
    setColumnVisibility,
    resetToDefault,
    currentTableSettings,
    forceUpdateKey,
    tableId,
  ]);

  return result;
}
