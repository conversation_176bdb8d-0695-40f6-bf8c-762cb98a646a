import 'src/global.css';

import { useState, useEffect } from 'react';

import { paths } from 'src/routes/paths';
import { usePathname } from 'src/routes/hooks';

import { useAppSettings } from 'src/hooks/use-app-settings';

import { LocalizationProvider } from 'src/locales';
import { themeConfig, ThemeProvider } from 'src/theme';
import { I18nProvider } from 'src/locales/i18n-provider';

import AutoLock from 'src/components/auto-lock';
import { Snackbar } from 'src/components/snackbar';
import { ProgressBar } from 'src/components/progress-bar';
import { LockGuard } from 'src/components/auto-lock/lock-guard';
import { MotionLazy } from 'src/components/animate/motion-lazy';
import InitializationDialog from 'src/components/initialization/dialog';
import InitializationLoading from 'src/components/initialization/loading';
import { SettingsDrawer, defaultSettings, SettingsProvider } from 'src/components/settings';

import { AuthProvider as JwtAuthProvider } from 'src/auth/context/jwt';

// ----------------------------------------------------------------------

const AuthProvider = JwtAuthProvider;

// ----------------------------------------------------------------------

export default function App({ children }) {
  useScrollToTop();
  // 禁用右键
  useDisableRightClick();
  const pathname = usePathname();

  // 应用设置状态
  const { getSetting, updateSetting } = useAppSettings();
  const [showInitDialog, setShowInitDialog] = useState(false);
  const [isCheckingInit, setIsCheckingInit] = useState(true);

  // 检查应用初始化状态
  useEffect(() => {
    const checkInitStatus = () => {
      const isInited = getSetting('app.isInited', false);

      if (!isInited) {
        setShowInitDialog(true);
      }

      setIsCheckingInit(false);
    };

    // 延迟检查，确保设置已加载，同时让用户能看到加载状态
    const timer = setTimeout(checkInitStatus, 200);

    return () => clearTimeout(timer);
  }, [getSetting]);

  // 处理初始化完成
  const handleInitComplete = () => {
    updateSetting('app.isInited', true);
    setShowInitDialog(false);

    // 刷新页面，以更新数据
    window.location.reload();
  };

  // 处理初始化对话框关闭
  const handleInitClose = () => {
    setShowInitDialog(false);
  };

  // 如果正在检查初始化状态，显示加载状态
  if (isCheckingInit) {
    return (
      <I18nProvider>
        <SettingsProvider defaultSettings={defaultSettings}>
          <LocalizationProvider>
            <ThemeProvider
              modeStorageKey={themeConfig.modeStorageKey}
              defaultMode={themeConfig.enableSystemMode ? 'system' : themeConfig.defaultMode}
            >
              <MotionLazy>
                <InitializationLoading />
              </MotionLazy>
            </ThemeProvider>
          </LocalizationProvider>
        </SettingsProvider>
      </I18nProvider>
    );
  }

  return (
    <I18nProvider>
      <AuthProvider>
        <SettingsProvider defaultSettings={defaultSettings}>
          <LocalizationProvider>
            <ThemeProvider
              modeStorageKey={themeConfig.modeStorageKey}
              defaultMode={themeConfig.enableSystemMode ? 'system' : themeConfig.defaultMode}
            >
              <MotionLazy>
                <Snackbar />
                <ProgressBar />
                <SettingsDrawer defaultSettings={defaultSettings} />
                {pathname !== paths.lock && <AutoLock idleTime={10 * 60 * 1000} />}
                <LockGuard>{children}</LockGuard>

                {/* 初始化对话框 */}
                <InitializationDialog
                  open={showInitDialog}
                  onComplete={handleInitComplete}
                  onClose={handleInitClose}
                />
              </MotionLazy>
            </ThemeProvider>
          </LocalizationProvider>
        </SettingsProvider>
      </AuthProvider>
    </I18nProvider>
  );
}

// ----------------------------------------------------------------------

function useScrollToTop() {
  const pathname = usePathname();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
}

// ----------------------------------------------------------------------

function useDisableRightClick() {
  useEffect(() => {
    // 只在生产环境禁用右键
    if (import.meta.env.PROD) {
      const handleContextMenu = (e) => {
        e.preventDefault();
        return false;
      };

      document.addEventListener('contextmenu', handleContextMenu);

      return () => {
        document.removeEventListener('contextmenu', handleContextMenu);
      };
    }

    // 开发环境下返回undefined（不执行任何操作）
    return undefined;
  }, []);

  return null;
}
