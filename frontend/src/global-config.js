import { paths } from './routes/paths.js';
import packageJson from '../package.json';

// ----------------------------------------------------------------------

export const CONFIG = {
  appName: 'A8Tools',
  appVersion: packageJson.version,
  serverUrl: import.meta.env.VITE_SERVER_URL ?? '',
  assetsDir: import.meta.env.VITE_ASSETS_DIR ?? '',
  /**
   * Auth
   * @method jwt
   */
  auth: {
    method: 'jwt',
    skip: true,
    redirectPath: paths.dashboard.root,
  },
};

export const SUPPORTED_COINTYPES = [
  { value: 'eth', label: 'EVM', id: 'ethereum', network: 'Ethereum' },
  { value: 'sol', label: 'Sol', id: 'solana', network: 'Solana' },
  { value: 'btc', label: 'BTC', id: 'bitcoin', network: 'Bitcoin' },
  { value: 'trx', label: 'TRX', id: 'tron', network: 'Tron' },
  { value: 'sui', label: 'Sui', id: 'sui', network: 'Sui' },
  { value: 'aptos', label: 'Aptos', id: 'aptos', network: 'Aptos' },
  { value: 'ton', label: 'TON', id: 'ton', network: 'TON' },
];
