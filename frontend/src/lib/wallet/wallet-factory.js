/**
 * 钱包工厂 - 统一管理钱包实例的创建
 */

// 钱包类型映射
const WALLET_MODULES = {
  aptos: { path: './aptos/index.js', className: 'AptosWallet' },
  bsc: { path: './bsc/index.js', className: 'BscWallet' },
  btc: { path: './btc/index.js', className: 'BtcWallet' },
  cardano: { path: './cardano/index.js', className: 'AdaWallet' },
  cosmos: { path: './cosmos/index.js', className: 'CosmosWallet' },
  eos: { path: './eos/index.js', className: 'WaxWallet' },
  eth: { path: './eth/index.js', className: 'EthWallet' },
  kaspa: { path: './kaspa/index.js', className: 'KaspaWallet' },
  near: { path: './near/index.js', className: 'NearWallet' },
  sol: { path: './sol/index.js', className: 'SolWallet' },
  stacks: { path: './stacks/index.js', className: 'StxWallet' },
  starknet: { path: './starknet/index.js', className: 'StarknetWallet' },
  sui: { path: './sui/index.js', className: 'SuiWallet' },
  ton: { path: './ton/index.js', className: 'TonWallet' },
  trx: { path: './trx/index.js', className: 'TrxWallet' },
};

/**
 * 钱包工厂类 - 负责创建各种钱包实例
 */
class WalletFactory {
  /**
   * 异步创建钱包实例
   * @param {string} coinType - 币种类型
   * @returns {Promise<BaseWallet>} - 钱包实例
   * @throws {Error} - 如果币种不支持或创建失败
   */
  async createWallet(coinType) {
    if (!coinType) {
      throw new Error('币种类型不能为空');
    }

    const type = coinType.toLowerCase();
    const walletInfo = WALLET_MODULES[type];

    if (!walletInfo) {
      throw new Error(`不支持的币种类型: ${coinType}`);
    }

    try {
      // 动态导入钱包模块
      const module = await import(walletInfo.path);
      const WalletClass = module[walletInfo.className];

      if (!WalletClass) {
        throw new Error(`找不到钱包类: ${walletInfo.className}`);
      }

      return new WalletClass();
    } catch (error) {
      throw new Error(`创建钱包失败 (${coinType}): ${error.message}`);
    }
  }
}

// 创建单例实例
const walletFactory = new WalletFactory();

export default walletFactory;
