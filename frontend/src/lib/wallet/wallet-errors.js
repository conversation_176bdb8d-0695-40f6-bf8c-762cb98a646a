/**
 * 钱包错误类 - 定义钱包操作中的各种错误类型
 * 提供统一的错误处理机制
 */

/**
 * 钱包基础错误类
 */
export class WalletError extends Error {
  constructor(message, options = {}) {
    super(message);
    this.name = this.constructor.name;
    this.code = options.code || 'WALLET_ERROR';
    this.chainType = options.chainType;
    this.details = options.details;

    // 捕获堆栈跟踪
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

/**
 * 地址生成错误
 */
export class AddressGenerationError extends WalletError {
  constructor(coinType, cause, options = {}) {
    super(`生成 ${coinType.toUpperCase()} 地址失败: ${cause.message || cause}`, {
      code: 'ADDRESS_GENERATION_FAILED',
      coinType,
      details: { cause },
      ...options,
    });
    this.cause = cause;
  }
}

/**
 * 地址验证错误
 */
export class AddressValidationError extends WalletError {
  constructor(coinType, address, cause, options = {}) {
    super(`验证 ${coinType.toUpperCase()} 地址失败: ${cause.message || cause}`, {
      code: 'ADDRESS_VALIDATION_FAILED',
      coinType,
      details: { address, cause },
      ...options,
    });
    this.cause = cause;
  }
}

/**
 * 无效助记词错误
 */
export class InvalidMnemonicError extends WalletError {
  constructor(message = '无效的助记词', options = {}) {
    super(message, {
      code: 'INVALID_MNEMONIC',
      ...options,
    });
  }
}

/**
 * 私钥生成错误
 */
export class PrivateKeyGenerationError extends WalletError {
  constructor(coinType, cause, options = {}) {
    super(`获取 ${coinType.toUpperCase()} 私钥失败: ${cause.message || cause}`, {
      code: 'PRIVATE_KEY_GENERATION_FAILED',
      coinType,
      details: { cause },
      ...options,
    });
    this.cause = cause;
  }
}

/**
 * 不支持的 CoinType 错误
 */
export class UnsupportedCoinTypeError extends WalletError {
  constructor(coinType, options = {}) {
    super(`不支持的 CoinType: ${coinType}`, {
      code: 'UNSUPPORTED_COIN_TYPE',
      coinType,
      ...options,
    });
  }
}

export class MissingParameterError extends Error {
  constructor(params) {
    super(`缺少必填参数: ${params.join(', ')}`);
    this.name = 'MissingParameterError';
  }
}
