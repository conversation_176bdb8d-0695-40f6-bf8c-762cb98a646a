/**
 * 并行处理工具 - 提供统一的并行处理功能
 */

import logger from './logger.js';

/**
 * 并行执行任务
 * @param {Array} items - 要处理的项目数组
 * @param {Function} taskFn - 任务处理函数，接收(item, index)参数并返回Promise
 * @param {Object} options - 额外选项
 * @param {boolean} [options.parallel=true] - 是否并行执行
 * @param {number} [options.batchSize=10] - 并行批处理大小
 * @param {Function} [options.onProgress] - 进度回调函数，参数为(processedCount, totalCount)
 * @param {Function} [options.onItemComplete] - 单项完成回调，参数为(result, item, index)
 * @param {Function} [options.onItemError] - 单项错误回调，参数为(error, item, index)
 * @param {string} [options.taskName='task'] - 任务名称，用于日志
 * @returns {Promise<Array>} - 处理结果数组
 */
export async function processItems(items, taskFn, options = {}) {
  const {
    parallel = true,
    batchSize = 10,
    onProgress,
    onItemComplete,
    onItemError,
    taskName = 'task',
  } = options;

  const results = new Array(items.length).fill(null);
  let processedCount = 0;
  const totalCount = items.length;

  // 进度报告函数
  const reportProgress = () => {
    processedCount++;
    if (onProgress && typeof onProgress === 'function') {
      onProgress(processedCount, totalCount);
    }
  };

  // 创建处理单个项目的函数
  const processItem = async (item, index) => {
    try {
      const result = await taskFn(item, index);
      results[index] = result;

      if (onItemComplete && typeof onItemComplete === 'function') {
        onItemComplete(result, item, index);
      }

      return result;
    } catch (error) {
      logger.error(`${taskName} #${index} 处理失败:`, error);

      if (onItemError && typeof onItemError === 'function') {
        onItemError(error, item, index);
      }

      return null;
    } finally {
      reportProgress();
    }
  };

  if (parallel) {
    if (batchSize >= totalCount) {
      // 全部并行处理
      const promises = items.map((item, index) => processItem(item, index));
      await Promise.all(promises);
    } else {
      // 分批并行处理
      for (let i = 0; i < totalCount; i += batchSize) {
        const batch = items.slice(i, i + batchSize);
        const batchPromises = batch.map((item, batchIndex) => {
          const actualIndex = i + batchIndex;
          return processItem(item, actualIndex);
        });
        await Promise.all(batchPromises);
      }
    }
  } else {
    // 顺序处理
    for (let i = 0; i < totalCount; i++) {
      await processItem(items[i], i);
    }
  }

  return results;
}

/**
 * 并行执行索引任务
 * @param {number} count - 要处理的任务数量
 * @param {Function} taskFn - 任务处理函数，接收索引参数并返回Promise
 * @param {Object} options - 额外选项
 * @param {boolean} [options.parallel=true] - 是否并行执行
 * @param {number} [options.batchSize=10] - 并行批处理大小
 * @param {Function} [options.onProgress] - 进度回调函数，参数为(processedCount, totalCount)
 * @param {Function} [options.onItemComplete] - 单项完成回调，参数为(result, index)
 * @param {Function} [options.onItemError] - 单项错误回调，参数为(error, index)
 * @param {string} [options.taskName='task'] - 任务名称，用于日志
 * @returns {Promise<Array>} - 处理结果数组
 */
export async function processIndexTasks(count, taskFn, options = {}) {
  const indices = Array.from({ length: count }, (_, i) => i);
  return processItems(indices, (index) => taskFn(index), {
    ...options,
    onItemComplete: options.onItemComplete
      ? (result, _, index) => options.onItemComplete(result, index)
      : undefined,
    onItemError: options.onItemError
      ? (error, _, index) => options.onItemError(error, index)
      : undefined,
  });
}

export default {
  processItems,
  processIndexTasks,
};
