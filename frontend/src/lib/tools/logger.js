/**
 * 日志工具 - 提供统一的日志记录功能
 */

// 日志级别
export const LogLevel = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  NONE: 4,
};

// 默认日志配置
const defaultConfig = {
  level: LogLevel.INFO,
  includeTimestamp: true,
  includeLevel: true,
  maskSensitiveData: true,
  sensitiveDataLength: {
    prefix: 6,
    suffix: 4,
  },
};

// 当前配置
let currentConfig = { ...defaultConfig };

/**
 * 配置日志系统
 * @param {Object} config - 日志配置
 * @param {number} config.level - 日志级别
 * @param {boolean} config.includeTimestamp - 是否包含时间戳
 * @param {boolean} config.includeLevel - 是否包含日志级别
 * @param {boolean} config.maskSensitiveData - 是否掩码敏感数据
 * @param {Object} config.sensitiveDataLength - 敏感数据显示长度
 */
export function configureLogger(config = {}) {
  currentConfig = { ...defaultConfig, ...config };
}

/**
 * 格式化日志消息
 * @param {string} level - 日志级别
 * @param {string} message - 日志消息
 * @returns {string} - 格式化后的日志消息
 */
function formatLogMessage(level, message) {
  let formattedMessage = '';

  if (currentConfig.includeTimestamp) {
    formattedMessage += `[${new Date().toISOString()}] `;
  }

  if (currentConfig.includeLevel) {
    formattedMessage += `[${level}] `;
  }

  formattedMessage += message;
  return formattedMessage;
}

/**
 * 掩码敏感数据
 * @param {string} data - 敏感数据
 * @returns {string} - 掩码后的数据
 */
export function maskSensitiveData(data) {
  if (!data || typeof data !== 'string' || data.length <= 10) {
    return '***';
  }

  const { prefix, suffix } = currentConfig.sensitiveDataLength;
  return `${data.substring(0, prefix)}...${data.substring(data.length - suffix)}`;
}

/**
 * 记录调试级别日志
 * @param {string} message - 日志消息
 * @param {...any} args - 额外参数
 */
export function debug(message, ...args) {
  if (currentConfig.level <= LogLevel.DEBUG) {
    console.debug(formatLogMessage('DEBUG', message), ...args);
  }
}

/**
 * 记录信息级别日志
 * @param {string} message - 日志消息
 * @param {...any} args - 额外参数
 */
export function info(message, ...args) {
  if (currentConfig.level <= LogLevel.INFO) {
    console.info(formatLogMessage('INFO', message), ...args);
  }
}

/**
 * 记录警告级别日志
 * @param {string} message - 日志消息
 * @param {...any} args - 额外参数
 */
export function warn(message, ...args) {
  if (currentConfig.level <= LogLevel.WARN) {
    console.warn(formatLogMessage('WARN', message), ...args);
  }
}

/**
 * 记录错误级别日志
 * @param {string} message - 日志消息
 * @param {...any} args - 额外参数
 */
export function error(message, ...args) {
  if (currentConfig.level <= LogLevel.ERROR) {
    console.error(formatLogMessage('ERROR', message), ...args);
  }
}

// 导出默认日志对象
export default {
  debug,
  info,
  warn,
  error,
  maskSensitiveData,
  configureLogger,
  LogLevel,
};
