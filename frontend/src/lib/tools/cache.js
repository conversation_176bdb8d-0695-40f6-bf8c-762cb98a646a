/**
 * 通用缓存工具
 * 用于缓存各种对象，避免重复创建
 * 实现了过期策略和自动清理
 *
 * @version 1.0.0
 * <AUTHOR> Team
 */

/**
 * 通用缓存类
 * 提供对象缓存、过期策略和自动清理功能
 *
 *
 * 示例：创建自定义缓存实例
import { createCache } from './cache';

// 创建带自定义配置的缓存实例
const userCache = createCache({
  namespace: 'users',
  expirationTime: 3600000, // 1小时过期
  valueTransformer: {
    // 自定义序列化/反序列化
    serialize: (value) => JSON.stringify(value),
    deserialize: (value) => JSON.parse(value)
  }
});

// 使用自定义缓存实例
userCache.set('profile:1', { id: 1, name: '王五' });
const profile = userCache.get('profile:1');
console.log(profile); // { id: 1, name: '王五' }

 */
class Cache {
  /**
   * 创建缓存实例
   * @param {Object} options - 缓存配置选项
   * @param {number} [options.expirationTime=1800000] - 缓存过期时间（毫秒），默认30分钟
   * @param {string} [options.namespace='default'] - 缓存命名空间
   */
  constructor(options = {}) {
    // 缓存存储
    this.cache = new Map();

    // 缓存创建时间记录
    this.creationTime = new Map();

    // 缓存过期时间（毫秒），默认30分钟
    this.expirationTime = options.expirationTime || 30 * 60 * 1000;

    // 缓存命名空间
    this.namespace = options.namespace || 'default';

    // 值转换器，用于自定义值的存储和检索
    this.valueTransformer = options.valueTransformer || {
      serialize: (value) => value,
      deserialize: (value) => value,
    };
  }

  /**
   * 获取缓存项
   * @param {string} key - 缓存键
   * @returns {*} - 缓存值，如果不存在或已过期则返回undefined
   */
  get(key) {
    if (!key) return undefined;

    const normalizedKey = this._normalizeKey(key);

    // 检查缓存是否过期
    this._checkExpiration(normalizedKey);

    if (!this.cache.has(normalizedKey)) {
      return undefined;
    }

    const value = this.cache.get(normalizedKey);
    return this.valueTransformer.deserialize(value);
  }

  /**
   * 设置缓存项
   * @param {string} key - 缓存键
   * @param {*} value - 缓存值
   * @param {number} [ttl] - 此项的生存时间（秒），覆盖默认过期时间
   * @returns {boolean} - 是否成功设置
   */
  set(key, value, ttl) {
    if (!key) return false;

    const normalizedKey = this._normalizeKey(key);
    const serializedValue = this.valueTransformer.serialize(value);

    this.cache.set(normalizedKey, serializedValue);

    // 设置创建时间
    this.creationTime.set(normalizedKey, {
      time: Date.now(),
      ttl: ttl ? ttl * 1000 : null, // 转换为毫秒
    });

    return true;
  }

  /**
   * 检查键是否存在且未过期
   * @param {string} key - 缓存键
   * @returns {boolean} - 是否存在且未过期
   */
  has(key) {
    if (!key) return false;

    const normalizedKey = this._normalizeKey(key);

    // 检查缓存是否过期
    this._checkExpiration(normalizedKey);

    return this.cache.has(normalizedKey);
  }

  /**
   * 删除缓存项
   * @param {string} key - 缓存键
   * @returns {boolean} - 是否成功删除
   */
  delete(key) {
    if (!key) return false;

    const normalizedKey = this._normalizeKey(key);
    const deleted = this.cache.delete(normalizedKey);
    this.creationTime.delete(normalizedKey);

    return deleted;
  }

  /**
   * 清除所有缓存
   */
  clear() {
    this.cache.clear();
    this.creationTime.clear();
  }

  /**
   * 获取所有缓存键
   * @returns {string[]} - 缓存键数组
   */
  keys() {
    // 先检查所有缓存是否过期
    this._checkExpiration();

    // 返回未过期的键
    return Array.from(this.cache.keys()).map((key) => {
      // 移除命名空间前缀
      const parts = key.split(':');
      return parts.length > 1 ? parts.slice(1).join(':') : key;
    });
  }

  /**
   * 获取缓存项数量
   * @returns {number} - 缓存项数量
   */
  size() {
    // 先检查所有缓存是否过期
    this._checkExpiration();

    return this.cache.size;
  }

  /**
   * 设置缓存过期时间
   * @param {number} timeInMs - 过期时间（毫秒）
   */
  setExpirationTime(timeInMs) {
    if (typeof timeInMs === 'number' && timeInMs > 0) {
      this.expirationTime = timeInMs;
      // 立即检查所有缓存
      this._checkExpiration();
    }
  }

  /**
   * 获取或设置缓存项
   * @param {string} key - 缓存键
   * @param {Function} createFn - 创建缓存值的函数
   * @param {number} [ttl] - 此项的生存时间（秒）
   * @returns {*} - 缓存值
   */
  getOrSet(key, createFn, ttl) {
    if (!key) throw new Error('缓存键不能为空');
    if (typeof createFn !== 'function') throw new Error('createFn必须是函数');

    const normalizedKey = this._normalizeKey(key);

    // 检查缓存是否过期
    this._checkExpiration(normalizedKey);

    if (!this.cache.has(normalizedKey)) {
      const value = createFn();
      this.set(key, value, ttl);
    }

    return this.get(key);
  }

  /**
   * 检查并清理过期的缓存项
   * @param {string} key - 缓存键（可选）
   * @private
   */
  _checkExpiration(key) {
    const now = Date.now();

    if (key) {
      // 检查特定键是否过期
      const creationInfo = this.creationTime.get(key);
      if (creationInfo) {
        const ttl = creationInfo.ttl || this.expirationTime;
        if (now - creationInfo.time > ttl) {
          this.cache.delete(key);
          this.creationTime.delete(key);
        }
      }
    } else {
      // 检查所有缓存是否过期
      this.creationTime.forEach((creationInfo, k) => {
        const ttl = creationInfo.ttl || this.expirationTime;
        if (now - creationInfo.time > ttl) {
          this.cache.delete(k);
          this.creationTime.delete(k);
        }
      });
    }
  }

  /**
   * 标准化缓存键
   * @param {string} key - 原始键
   * @returns {string} - 标准化后的键
   * @private
   */
  _normalizeKey(key) {
    // 添加命名空间前缀
    return `${this.namespace}:${key}`;
  }
}

// 创建默认缓存实例
const defaultCache = new Cache();

/**
 * 创建新的缓存实例
 * @param {Object} options - 缓存配置选项
 * @returns {Cache} - 新的缓存实例
 */
export function createCache(options = {}) {
  return new Cache(options);
}

// 导出默认缓存实例和缓存类
export { Cache, defaultCache as cache };
export default defaultCache;
