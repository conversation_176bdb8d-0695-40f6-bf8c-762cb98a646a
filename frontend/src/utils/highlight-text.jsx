import { Box } from '@mui/material';

/**
 * 高亮搜索关键词的工具函数
 * @param {string} text - 要处理的文本
 * @param {string} searchTerm - 搜索关键词
 * @param {object} highlightSx - 高亮样式，可选
 * @returns {string|JSX.Element[]} 返回原文本或包含高亮的JSX元素数组
 */
export function highlightText(text, searchTerm, highlightSx = {}) {
  if (!searchTerm || !text || text === '-') return text;

  // 转义正则表达式特殊字符
  const escapedTerm = searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  const regex = new RegExp(`(${escapedTerm})`, 'gi');

  // 如果没有匹配，直接返回原文本
  if (!regex.test(text)) return text;

  // 重置正则表达式索引
  regex.lastIndex = 0;
  const parts = text.split(regex);

  return parts.map((part, index) => {
    const isMatch = regex.test(part);
    regex.lastIndex = 0; // 重置索引

    return isMatch ? (
      <Box
        key={index}
        component="span"
        sx={{
          backgroundColor: 'warning.lighter',
          color: 'warning.darker',
          fontWeight: 'bold',
          padding: '1px 2px',
          borderRadius: '2px',
          ...highlightSx,
        }}
      >
        {part}
      </Box>
    ) : (
      part
    );
  });
}

/**
 * 默认高亮样式
 */
export const defaultHighlightStyle = {
  backgroundColor: 'warning.lighter',
  color: 'warning.darker',
  fontWeight: 'bold',
  padding: '1px 2px',
  borderRadius: '2px',
};

/**
 * 其他预设高亮样式
 */
export const highlightStyles = {
  primary: {
    backgroundColor: 'primary.lighter',
    color: 'primary.darker',
    fontWeight: 'bold',
    padding: '1px 2px',
    borderRadius: '2px',
  },
  secondary: {
    backgroundColor: 'secondary.lighter',
    color: 'secondary.darker',
    fontWeight: 'bold',
    padding: '1px 2px',
    borderRadius: '2px',
  },
  error: {
    backgroundColor: 'error.lighter',
    color: 'error.darker',
    fontWeight: 'bold',
    padding: '1px 2px',
    borderRadius: '2px',
  },
  success: {
    backgroundColor: 'success.lighter',
    color: 'success.darker',
    fontWeight: 'bold',
    padding: '1px 2px',
    borderRadius: '2px',
  },
};

/**
 * 
 * 使用示例

  // 基本使用
  {highlightText(row.Name,
  currentSearchTerm)}

  // 自定义样式
  {highlightText(row.Name, currentSearchTerm,
   { backgroundColor: 'blue' })}

  // 使用预设样式
  {highlightText(row.Name, currentSearchTerm,
   highlightStyles.primary)}

 */
