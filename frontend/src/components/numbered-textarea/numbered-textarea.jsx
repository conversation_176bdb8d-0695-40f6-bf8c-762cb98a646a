import PropTypes from 'prop-types';
import React, { useRef, useState, useEffect, useCallback } from 'react';

import { Box } from '@mui/material';
import { styled } from '@mui/material/styles';

// 主容器样式 - 使用flexbox布局
const TextareaContainer = styled('div')(({ theme }) => ({
  position: 'relative',
  border: `1px solid ${theme.palette.mode === 'light' ? 'rgba(0, 0, 0, 0.23)' : 'rgba(255, 255, 255, 0.23)'}`,
  borderRadius: theme.shape.borderRadius,
  fontFamily: 'monospace',
  fontSize: 14,
  lineHeight: 1.5,
  overflow: 'hidden',
  width: '100%',
  display: 'flex',
  '&:hover': {
    borderColor: theme.palette.text.primary,
  },
  '&:focus-within': {
    borderColor: theme.palette.primary.main,
    borderWidth: 2,
  },
}));

// 行号区域样式
const LineNumbersColumn = styled('div')(({ theme }) => ({
  width: '48px',
  flexShrink: 0,
  backgroundColor:
    theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[900],
  borderRight: `1px solid ${theme.palette.mode === 'light' ? 'rgba(0, 0, 0, 0.08)' : 'rgba(255, 255, 255, 0.08)'}`,
  color: theme.palette.text.disabled,
  textAlign: 'right',
  paddingTop: '16px',
  userSelect: 'none',
  overflowY: 'hidden',
  '& span': {
    display: 'block',
    height: '24px',
    lineHeight: '24px',
    fontSize: '14px',
    paddingRight: '8px',
    minWidth: '32px',
    textAlign: 'right',
    width: '100%',
    boxSizing: 'border-box',
  },
}));

// 文本输入区域样式
const StyledTextarea = styled('textarea')(({ theme }) => ({
  flex: 1,
  height: '100%',
  fontFamily: 'monospace',
  fontSize: 14,
  lineHeight: '24px',
  padding: '16px 16px 16px 12px',
  border: 'none',
  outline: 'none',
  resize: 'none',
  boxSizing: 'border-box',
  whiteSpace: 'pre',
  overflowX: 'auto', // 允许水平滚动
  overflowY: 'scroll', // 始终显示垂直滚动条
  backgroundColor: 'transparent',
  color: theme.palette.text.primary,
  '&::placeholder': {
    color: theme.palette.text.disabled,
  },
  '&::-webkit-scrollbar': {
    width: '8px',
    height: '8px',
  },
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: 'rgba(0,0,0,0.2)',
    borderRadius: '4px',
  },
}));

// 行号组件
const LineNumbers = ({ count, scrollTop }) => {
  // 创建一个数组，长度为 count，并生成行号
  const numbers = [];
  // 确保至少显示1行
  const actualCount = Math.max(count, 1);

  for (let i = 0; i < actualCount; i++) {
    numbers.push(<span key={i}>{i + 1}</span>);
  }

  return <div style={{ transform: `translateY(-${scrollTop}px)` }}>{numbers}</div>;
};

LineNumbers.propTypes = {
  count: PropTypes.number.isRequired,
  scrollTop: PropTypes.number,
};

/**
 * 带行号的文本输入组件
 */
export const LineNumberedTextArea = ({
  value = '',
  onChange,
  placeholder,
  rows = 10,
  maxRows = 100,
  minRows = 10,
  height,
  helperText,
  error,
  onError,
  ...other
}) => {
  const [lineCount, setLineCount] = useState(calculateLineCount(value));
  const [scrollTop, setScrollTop] = useState(0);
  const [isExceedingMaxRows, setIsExceedingMaxRows] = useState(false);
  const textareaRef = useRef(null);

  // 计算实际行数
  function calculateLineCount(text) {
    if (!text || typeof text !== 'string') return 1;
    // 计算文本中的换行符数量，确保至少返回1行
    const newLineCount = text.split('\n').length;
    // 始终返回实际行数，至少为1行
    return Math.max(newLineCount, 1);
  }

  // 使用 useCallback 优化事件处理函数
  const handleChange = useCallback(
    (e) => {
      const newValue = e.target.value;
      const newLineCount = calculateLineCount(newValue);

      // 更新是否超出最大行数的状态
      const isExceeding = newLineCount > maxRows;
      setIsExceedingMaxRows(isExceeding);

      // 调用onError回调
      if (isExceeding && onError) {
        onError(`不能超过${maxRows}行`);
      } else if (!isExceeding && onError) {
        onError(''); // 清除错误消息
      }

      // 允许删除操作（新内容比当前内容短）
      // 或者新行数不超过最大行数
      if (newValue.length < value.length || newLineCount <= maxRows) {
        onChange?.(e);
      }
    },
    [onChange, maxRows, value, onError]
  );

  const handleScroll = useCallback((event) => {
    setScrollTop(event.target.scrollTop);
  }, []);

  // 初始时和value变化时更新行号
  useEffect(() => {
    // 计算实际行数
    const newLineCount = calculateLineCount(value);
    // 确保行号数量正确
    setLineCount(newLineCount);

    // 检查是否超出最大行数
    const isExceeding = newLineCount > maxRows;
    setIsExceedingMaxRows(isExceeding);

    // 调用onError回调
    if (isExceeding && onError) {
      onError(`不能超过${maxRows}行`);
    }
  }, [value, rows, maxRows, onError]);

  // 添加和移除滚动事件监听器
  useEffect(() => {
    let textareaElement = textareaRef.current;
    if (textareaElement) {
      textareaElement.addEventListener('scroll', handleScroll);
      return () => {
        textareaElement.removeEventListener('scroll', handleScroll);
      };
    }
    return undefined;
  }, [handleScroll]);

  return (
    <Box>
      <TextareaContainer
        style={{
          height: height || 'auto',
          ...(isExceedingMaxRows && { border: '1px solid #d32f2f' }),
        }}
      >
        <LineNumbersColumn>
          <LineNumbers count={lineCount} scrollTop={scrollTop} />
        </LineNumbersColumn>
        <StyledTextarea
          ref={textareaRef}
          rows={rows}
          value={value}
          onChange={handleChange}
          placeholder={placeholder}
          {...other}
        />
      </TextareaContainer>
    </Box>
  );
};

LineNumberedTextArea.propTypes = {
  value: PropTypes.string,
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
  rows: PropTypes.number,
  maxRows: PropTypes.number,
  minRows: PropTypes.number,
  height: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  helperText: PropTypes.string,
  error: PropTypes.bool,
  onError: PropTypes.func,
};
