import { useState } from 'react';

import { Box, Menu, Button, MenuItem } from '@mui/material';

import { useTranslate } from 'src/locales';

import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

export function ImportExportAction({
  onImport,
  onAddIndividual,
  onBatchAdd,
  onExport,
  importLabel,
  exportLabel,
  individualLabel,
  batchLabel,
  variant = 'text',
  sx,
  ...other
}) {
  const { t } = useTranslate();

  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  const handleImportClick = (event) => {
    if (onImport) {
      onImport();
    } else {
      setAnchorEl(event.currentTarget);
    }
  };

  const handleImportClose = () => {
    setAnchorEl(null);
  };

  const handleAddIndividual = () => {
    onAddIndividual?.();
    handleImportClose();
  };

  const handleBatchAdd = () => {
    onBatchAdd?.();
    handleImportClose();
  };

  const handleExport = () => {
    onExport?.();
  };

  return (
    <Box sx={{ display: 'flex', gap: 2, ...sx }} {...other}>
      <Button
        variant={variant === 'contained' ? 'text' : variant}
        startIcon={<Iconify icon="mingcute:arrow-down-circle-fill" />}
        onClick={handleImportClick}
      >
        {importLabel || t('common:action.import')}
      </Button>

      {!onImport && (
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleImportClose}
          aria-labelledby="import-button"
        >
          <MenuItem onClick={handleAddIndividual}>
            {individualLabel || t('common:action.addIndividual')}
          </MenuItem>
          <MenuItem onClick={handleBatchAdd}>{batchLabel || t('common:action.batchAdd')}</MenuItem>
        </Menu>
      )}

      <Button
        variant={variant}
        startIcon={<Iconify icon="mingcute:arrow-up-circle-fill" />}
        onClick={handleExport}
      >
        {exportLabel || t('common:action.export')}
      </Button>
    </Box>
  );
}
