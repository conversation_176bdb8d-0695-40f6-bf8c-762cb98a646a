import { Box } from '@mui/material';

import { Label } from './label';

/**
 * 账号标签渲染组件
 * @param {string} labels - 标签字符串，支持逗号分隔
 * @param {string} searchTerm - 搜索词，用于高亮显示
 * @param {number} maxDisplay - 最大显示标签数量，默认为3
 * @param {Object} sx - 自定义样式
 */
export function ColumnLabels({ labels, searchTerm = '', maxDisplay = 3, sx = {} }) {
  // 如果没有标签或标签为空，显示占位符
  if (!labels || labels === '-' || labels.trim() === '') {
    return <Box sx={{ color: 'text.secondary', ...sx }}>-</Box>;
  }

  // 解析标签字符串（支持逗号分隔）
  const labelList = labels
    .split(',')
    .map((label) => label.trim())
    .filter(Boolean);

  const colors = ['default', 'primary', 'secondary', 'info', 'success', 'warning', 'error'];

  // 优化的颜色分配算法：确保相同标签颜色一致，且相邻标签颜色不同
  const getOptimizedColors = (list) => {
    const labelColors = [];

    // 为每个标签生成基础哈希值
    const getBaseHash = (text) => {
      let hash = 0;
      for (let i = 0; i < text.length; i += 1) {
        hash = text.charCodeAt(i) + hash * 31;
      }
      return Math.abs(hash);
    };

    list.forEach((label, index) => {
      const baseHash = getBaseHash(label);
      let colorIndex = baseHash % colors.length;

      // 如果与前一个标签颜色相同，则选择下一个颜色
      if (index > 0) {
        const prevColorIndex = colors.indexOf(labelColors[index - 1]);
        if (colorIndex === prevColorIndex) {
          colorIndex = (colorIndex + 1) % colors.length;
          // 如果还是相同（颜色数组只有2个元素的极端情况），再次偏移
          if (colorIndex === prevColorIndex && colors.length > 2) {
            colorIndex = (colorIndex + 1) % colors.length;
          }
        }
      }

      labelColors.push(colors[colorIndex]);
    });

    return labelColors;
  };

  const displayLabels = labelList.slice(0, maxDisplay);
  const labelColors = getOptimizedColors(displayLabels);

  return (
    <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', alignItems: 'center', ...sx }}>
      {displayLabels.map((label, index) => (
        <Label
          key={`${label}-${index}`}
          variant="soft"
          color={labelColors[index]}
          sx={{ fontSize: '0.75rem', maxWidth: 80 }}
        >
          {label.length > 10 ? `${label.substring(0, 10)}...` : label}
        </Label>
      ))}
      {labelList.length > maxDisplay && (
        <Label variant="soft" color="default" sx={{ fontSize: '0.75rem' }}>
          +{labelList.length - maxDisplay}
        </Label>
      )}
    </Box>
  );
}
