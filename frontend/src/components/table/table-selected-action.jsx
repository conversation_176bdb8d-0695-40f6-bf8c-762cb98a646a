import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Checkbox from '@mui/material/Checkbox';
import Typography from '@mui/material/Typography';

import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

export function TableSelectedAction({
  sx,
  dense,
  action,
  actions = [],
  rowCount,
  numSelected,
  onSelectAllRows,
  onDelete,
  selectedIds = [],
  ...other
}) {
  if (!numSelected) {
    return null;
  }

  // 构建默认的删除操作
  const defaultDeleteAction = onDelete && {
    key: 'delete',
    label: 'Delete',
    icon: 'solar:trash-bin-trash-bold',
    onClick: () => onDelete(selectedIds),
    color: 'error',
  };

  // 合并所有操作，使用展开语法为删除操作提供默认实现
  const allActions = [...(defaultDeleteAction ? [defaultDeleteAction] : []), ...actions];

  // 如果既有action prop又有actions数组，将action作为附加操作
  const finalActions = action ? [...allActions, { key: 'custom', component: action }] : allActions;

  return (
    <Box
      sx={[
        () => ({
          pl: 1,
          pr: 2,
          top: 0,
          left: 0,
          width: 1,
          zIndex: 9,
          height: 58, // 与 TableView 中的 normal 模式行高保持一致
          display: 'flex',
          position: 'absolute',
          alignItems: 'center',
          bgcolor: 'primary.lighter',
          ...(dense && { height: 55 }), // 与 TableView 中的 dense 模式行高保持一致
        }),
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
      {...other}
    >
      <Checkbox
        indeterminate={!!numSelected && numSelected < rowCount}
        checked={!!rowCount && numSelected === rowCount}
        onChange={(event) => onSelectAllRows(event.target.checked)}
        slotProps={{
          input: {
            id: 'deselect-all-checkbox',
            'aria-label': 'Deselect all checkbox',
          },
        }}
      />

      <Typography
        variant="subtitle2"
        sx={{
          ml: 2,
          flexGrow: 1,
          color: 'primary.main',
          ...(dense && { ml: 3 }),
        }}
      >
        {numSelected} selected
      </Typography>

      {finalActions.length > 0 && (
        <Box sx={{ display: 'flex', gap: 1 }}>
          {finalActions.map((actionItem) => {
            // 如果是自定义组件，直接渲染
            if (actionItem.component) {
              return <Box key={actionItem.key}>{actionItem.component}</Box>;
            }

            // 渲染标准按钮
            return (
              <Button
                key={actionItem.key}
                size="small"
                color={actionItem.color || 'primary'}
                variant={actionItem.variant || 'text'}
                startIcon={actionItem.icon && <Iconify icon={actionItem.icon} />}
                onClick={actionItem.onClick}
                {...(actionItem.buttonProps || {})}
              >
                {actionItem.label}
              </Button>
            );
          })}
        </Box>
      )}
    </Box>
  );
}
