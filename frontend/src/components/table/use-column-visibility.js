import { useMemo, useState, useEffect, useCallback } from 'react';

/**
 * 列可见性管理Hook
 * @param {Array} columns - 列定义数组
 * @returns {Object} 列可见性相关状态和方法
 */
export function useColumnVisibility(columns) {
  const [columnVisibility, setColumnVisibility] = useState({});
  const [columnMenuAnchor, setColumnMenuAnchor] = useState(null);
  const [columnOrder, setColumnOrder] = useState([]);

  // 初始化列可见性状态和列顺序
  useEffect(() => {
    const initialVisibility = {};
    const initialOrder = [];

    columns.forEach((column) => {
      // 默认所有列都可见，除了action列始终可见且不可隐藏
      initialVisibility[column.id] = true;
      initialOrder.push(column.id);
    });

    setColumnVisibility(initialVisibility);
    setColumnOrder(initialOrder);
  }, [columns]);

  // 处理列可见性切换
  const handleColumnVisibilityChange = useCallback((columnId) => {
    setColumnVisibility((prev) => ({
      ...prev,
      [columnId]: !prev[columnId],
    }));
  }, []);

  // 处理列重新排序
  const handleColumnReorder = useCallback((newColumns) => {
    const newOrder = newColumns.map((column) => column.id);
    setColumnOrder(newOrder);
  }, []);

  // 处理列菜单打开
  const handleColumnMenuOpen = useCallback((event) => {
    setColumnMenuAnchor(event.currentTarget);
  }, []);

  // 处理列菜单关闭
  const handleColumnMenuClose = useCallback(() => {
    setColumnMenuAnchor(null);
  }, []);

  // 根据列顺序和可见性过滤列
  const visibleColumns = useMemo(() => {
    // 如果还没有初始化列顺序，使用原始顺序
    if (columnOrder.length === 0) {
      return columns.filter((column) => columnVisibility[column.id] !== false);
    }

    // 根据列顺序重新排列，并过滤可见的列
    const orderedColumns = columnOrder
      .map((columnId) => columns.find((column) => column.id === columnId))
      .filter((column) => column && columnVisibility[column.id] !== false);

    return orderedColumns;
  }, [columns, columnVisibility, columnOrder]);

  // 获取按顺序排列的所有列（包括不可见的）
  const orderedColumns = useMemo(() => {
    if (columnOrder.length === 0) {
      return columns;
    }

    return columnOrder
      .map((columnId) => columns.find((column) => column.id === columnId))
      .filter((column) => column);
  }, [columns, columnOrder]);

  // 重置所有列可见性
  const resetColumnVisibility = useCallback(() => {
    const resetVisibility = {};
    columns.forEach((column) => {
      resetVisibility[column.id] = true;
    });
    setColumnVisibility(resetVisibility);
  }, [columns]);

  // 隐藏所有非必需列
  const hideAllColumns = useCallback(() => {
    const hiddenVisibility = {};
    columns.forEach((column) => {
      // action列始终可见
      if (column.id === 'action' || column.id === 'actions') {
        hiddenVisibility[column.id] = true;
      } else {
        hiddenVisibility[column.id] = false;
      }
    });
    setColumnVisibility(hiddenVisibility);
  }, [columns]);

  // 重置列顺序
  const resetColumnOrder = useCallback(() => {
    const initialOrder = columns.map((column) => column.id);
    setColumnOrder(initialOrder);
  }, [columns]);

  return {
    columnVisibility,
    visibleColumns,
    orderedColumns,
    columnOrder,
    columnMenuAnchor,
    columnMenuOpen: Boolean(columnMenuAnchor),
    handleColumnVisibilityChange,
    handleColumnReorder,
    handleColumnMenuOpen,
    handleColumnMenuClose,
    resetColumnVisibility,
    hideAllColumns,
    resetColumnOrder,
    setColumnVisibility,
    setColumnOrder,
  };
}
