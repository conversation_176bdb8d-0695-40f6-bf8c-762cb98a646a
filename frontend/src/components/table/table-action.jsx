import { Box, Button } from '@mui/material';

import { useTranslate } from 'src/locales';

import { Iconify } from 'src/components/iconify';

export function TableActions({ onAdd, onImport, onExport }) {
  const { t } = useTranslate('common');

  return (
    <Box
      sx={{
        gap: 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'flex-end',
        mb: 2,
      }}
    >
      <Button startIcon={<Iconify icon="mingcute:add-circle-fill" />} onClick={onAdd}>
        {t('common:action.add')}
      </Button>

      {onImport && (
        <Button startIcon={<Iconify icon="mingcute:arrow-down-circle-fill" />} onClick={onImport}>
          {t('common:action.import')}
        </Button>
      )}

      {onExport && (
        <Button startIcon={<Iconify icon="mingcute:arrow-up-circle-fill" />} onClick={onExport}>
          {t('common:action.export')}
        </Button>
      )}
    </Box>
  );
}
