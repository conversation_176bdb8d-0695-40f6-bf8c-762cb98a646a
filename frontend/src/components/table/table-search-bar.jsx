import { Box, Button, TextField, Typography, InputAdornment } from '@mui/material';

import { useTranslate } from 'src/locales';

import { Iconify } from 'src/components/iconify';

/**
 * 表格搜索栏组件
 * @param {string} value - 搜索值
 * @param {function} onChange - 搜索值变化回调
 * @param {function} onClear - 清空搜索回调
 * @param {boolean} loading - 加载状态
 * @param {string} debouncedValue - 防抖后的搜索值
 * @param {number} totalCount - 搜索结果总数
 * @param {string} placeholder - 占位文本
 * @param {Object} sx - 样式对象
 */
export function TableSearchBar({
  value,
  onChange,
  onClear,
  loading = false,
  debouncedValue = '',
  totalCount = 0,
  placeholder,
  sx,
}) {
  const { t } = useTranslate('common');

  return (
    <Box
      sx={{
        py: 2.5,
        px: 3,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: 2,
        ...sx,
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flex: 1 }}>
        <TextField
          size="small"
          value={value}
          onChange={onChange}
          placeholder={placeholder || t('common:tips.search')}
          slotProps={{
            input: {
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify
                    icon={loading && debouncedValue ? 'eos-icons:loading' : 'eva:search-fill'}
                    sx={{
                      color: 'text.disabled',
                      ...(loading &&
                        debouncedValue && {
                          animation: 'spin 1s linear infinite',
                          '@keyframes spin': {
                            '0%': { transform: 'rotate(0deg)' },
                            '100%': { transform: 'rotate(360deg)' },
                          },
                        }),
                    }}
                  />
                </InputAdornment>
              ),
              endAdornment: value && (
                <InputAdornment position="end">
                  <Button
                    size="small"
                    onClick={onClear}
                    sx={{
                      p: 0,
                      minWidth: 'auto',
                      color: 'text.secondary',
                      '&:hover': {
                        color: 'error.main',
                        backgroundColor: 'transparent',
                      },
                    }}
                  >
                    <Iconify icon="eva:close-fill" />
                  </Button>
                </InputAdornment>
              ),
            },
          }}
          sx={{
            width: { sm: 280, md: 320 },
          }}
        />
        {/* 搜索结果提示 */}
        {debouncedValue && (
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              fontSize: '0.875rem',
              fontWeight: 500,
            }}
          >
            {loading ? (
              'Searching...'
            ) : (
              <>
                Search results: {totalCount} items
                {totalCount === 0 && (
                  <Box component="span" sx={{ color: 'warning.main', ml: 1 }}>
                    (No results found)
                  </Box>
                )}
              </>
            )}
          </Typography>
        )}
      </Box>
    </Box>
  );
}
