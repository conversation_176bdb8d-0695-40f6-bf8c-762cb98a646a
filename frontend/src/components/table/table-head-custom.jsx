import Box from '@mui/material/Box';
import TableRow from '@mui/material/TableRow';
import Checkbox from '@mui/material/Checkbox';
import TableHead from '@mui/material/TableHead';
import TableCell from '@mui/material/TableCell';
import TableSortLabel from '@mui/material/TableSortLabel';

// ----------------------------------------------------------------------

// 自定义排序图标组件 - 上下三角箭头样式
const SortIcon = ({ active, direction }) => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      width: 16,
      height: 20,
      ml: 0.5,
      gap: 0.25, // 增加上下三角之间的间距
    }}
  >
    {/* 上三角 */}
    <Box
      sx={{
        width: 0,
        height: 0,
        borderLeft: '5px solid transparent',
        borderRight: '5px solid transparent',
        borderBottom: '6px solid',
        borderBottomColor: active && direction === 'asc' ? 'primary.main' : 'text.disabled',
        opacity: active && direction === 'asc' ? 1 : 0.65, // 未激活时保持更好的可见度
        transition: 'all 0.2s ease-in-out',
      }}
    />

    {/* 下三角 */}
    <Box
      sx={{
        width: 0,
        height: 0,
        borderLeft: '5px solid transparent',
        borderRight: '5px solid transparent',
        borderTop: '6px solid',
        borderTopColor: active && direction === 'desc' ? 'primary.main' : 'text.disabled',
        opacity: active && direction === 'desc' ? 1 : 0.65, // 未激活时保持更好的可见度
        transition: 'all 0.2s ease-in-out',
      }}
    />
  </Box>
);

// ----------------------------------------------------------------------

const visuallyHidden = {
  border: 0,
  padding: 0,
  width: '1px',
  height: '1px',
  margin: '-1px',
  overflow: 'hidden',
  position: 'absolute',
  whiteSpace: 'nowrap',
  clip: 'rect(0 0 0 0)',
};

// ----------------------------------------------------------------------

export function TableHeadCustom({
  sx,
  order,
  onSort,
  orderBy,
  headCells,
  rowCount = 0,
  numSelected = 0,
  onSelectAllRows,
  enableStickyLastColumn = true,
}) {
  return (
    <TableHead sx={sx}>
      <TableRow>
        {onSelectAllRows && (
          <TableCell
            padding="checkbox"
            sx={{
              position: 'sticky',
              left: 0,
              backgroundColor: 'background.paper',
              zIndex: 10, // 确保 checkbox 列在 TableSelectedAction 之上
            }}
          >
            <Checkbox
              indeterminate={!!numSelected && numSelected < rowCount}
              checked={!!rowCount && numSelected === rowCount}
              onChange={(event) => onSelectAllRows(event.target.checked)}
              slotProps={{
                input: {
                  id: `all-row-checkbox`,
                  'aria-label': `All row Checkbox`,
                },
              }}
            />
          </TableCell>
        )}

        {headCells.map((headCell, index) => {
          const isLastColumn = index === headCells.length - 1;
          const isActionColumn = headCell.id === 'action' || headCell.id === 'actions';
          const shouldStick = enableStickyLastColumn && (isLastColumn || isActionColumn);

          return (
            <TableCell
              key={headCell.id}
              align={headCell.align || 'left'}
              sortDirection={orderBy === headCell.id ? order : false}
              sx={[
                // 强制尺寸设置 - 与表格行保持一致
                ...(headCell.width
                  ? [
                      {
                        width: headCell.width,
                        maxWidth: headCell.width,
                        minWidth: headCell.width,
                      },
                    ]
                  : []),

                // 强制文本截断样式
                {
                  whiteSpace: 'nowrap',
                  overflow: 'hidden !important',
                  textOverflow: 'ellipsis',
                },

                // 固定最后一列的样式
                ...(shouldStick
                  ? [
                      {
                        position: 'sticky',
                        right: 0,
                        backgroundColor: 'background.paper',
                        zIndex: 10, // 表头需要比 TableSelectedAction (zIndex: 9) 更高的层级
                        // 使用 box-shadow 创建左边分隔线，与表格行保持一致
                        boxShadow: 'inset 1px 0 0 0 rgba(145, 158, 171, 0.16)',
                      },
                    ]
                  : []),
                ...(Array.isArray(headCell.sx) ? headCell.sx : [headCell.sx]),
              ]}
            >
              {onSort && headCell.sortable === true && !isActionColumn ? (
                <TableSortLabel
                  active={orderBy === headCell.id}
                  direction={orderBy === headCell.id ? order : 'asc'}
                  onClick={() => onSort(headCell.id)}
                  IconComponent={() => (
                    <SortIcon
                      active={orderBy === headCell.id}
                      direction={orderBy === headCell.id ? order : 'asc'}
                    />
                  )}
                  sx={{
                    '& .MuiTableSortLabel-icon': {
                      opacity: 1, // 始终显示图标
                      marginLeft: 0.5,
                      transition: 'all 0.2s ease-in-out',
                    },
                  }}
                >
                  {headCell.label}

                  {orderBy === headCell.id ? (
                    <Box component="span" sx={visuallyHidden}>
                      {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                    </Box>
                  ) : null}
                </TableSortLabel>
              ) : (
                headCell.label
              )}
            </TableCell>
          );
        })}
      </TableRow>
    </TableHead>
  );
}
