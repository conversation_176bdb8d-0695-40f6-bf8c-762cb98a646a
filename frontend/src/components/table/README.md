# Table 组件使用说明

## TableView 使用指南

TableView 组件是一个功能完整的分页表格组件，支持搜索、选中操作、排序等多种功能。

## 搜索功能

TableView 内置了搜索功能，支持实时搜索和防抖动处理：

### 基本搜索用法

```jsx
const getData = async (page, rowsPerPage, orderBy, order, searchValue) => {
  // 处理搜索参数
  const params = {
    page,
    size: rowsPerPage,
    orderBy,
    order,
    ...(searchValue && { search: searchValue }), // 添加搜索参数
  };
  
  const response = await apiService.getData(params);
  return [response.data, response.total];
};

<TableView
  columns={columns}
  getData={getData} // getData函数会接收searchValue参数
/>
```

### 搜索功能特性

- **防抖动处理**：300ms 延迟，避免频繁请求
- **实时反馈**：搜索时显示加载状态
- **结果统计**：显示搜索结果数量
- **清空搜索**：一键清空搜索条件
- **自动重置**：搜索时自动重置页码和选中项

### 高级搜索用法

#### 1. 服务端搜索实现

```jsx
const getData = async (page, rowsPerPage, orderBy, order, searchValue) => {
  const params = new URLSearchParams({
    page: page.toString(),
    size: rowsPerPage.toString(),
    ...(orderBy && { sort: `${orderBy},${order}` }),
    ...(searchValue && { q: searchValue }),
  });
  
  const response = await fetch(`/api/data?${params}`);
  const result = await response.json();
  
  return [result.content, result.totalElements];
};
```

#### 2. 客户端搜索实现

```jsx
const [allData, setAllData] = useState([]);

const getData = async (page, rowsPerPage, orderBy, order, searchValue) => {
  // 如果没有搜索条件，使用原始数据
  let filteredData = allData;
  
  // 客户端搜索过滤
  if (searchValue) {
    filteredData = allData.filter(item => 
      Object.values(item).some(value => 
        String(value).toLowerCase().includes(searchValue.toLowerCase())
      )
    );
  }
  
  // 客户端排序
  if (orderBy) {
    filteredData.sort((a, b) => {
      const aVal = a[orderBy];
      const bVal = b[orderBy];
      if (order === 'desc') {
        return bVal > aVal ? 1 : -1;
      }
      return aVal > bVal ? 1 : -1;
    });
  }
  
  // 客户端分页
  const startIndex = page * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const pageData = filteredData.slice(startIndex, endIndex);
  
  return [pageData, filteredData.length];
};
```

#### 3. 搜索状态管理

```jsx
const MyTable = () => {
  const tableRef = useRef();
  
  const handleExternalSearch = (searchTerm) => {
    // 外部触发搜索，比如从其他组件
    // TableView内部会自动处理搜索逻辑
    tableRef.current?.refetch();
  };
  
  return (
    <TableView
      ref={tableRef}
      columns={columns}
      getData={getData}
      selectedActions={selectedActions}
    />
  );
};
```

## 列拖拽排序功能

TableView 组件内置了列拖拽排序功能，用户可以通过拖拽来重新排列表格列的顺序。

### 基本特性

- **拖拽排序**：可拖拽的列显示菜单图标和拖拽手柄
- **固定列限制**：`id`、`action`、`actions` 列和设置了 `allowHiding: false` 的列不能拖拽
- **视觉反馈**：拖拽时提供清晰的视觉反馈和阴影效果
- **自动保存**：列顺序会自动保存到组件状态中
- **优化滚动**：自定义滚动条样式，隐藏横向滚动条，纵向滚动条更细更美观
- **交互优化**：悬停效果、过渡动画和状态指示器

### 列配置

```jsx
const columns = [
  { id: 'id', label: 'ID' }, // 固定列，不可拖拽
  { id: 'name', label: '名称' }, // 可拖拽
  { id: 'email', label: '邮箱', allowHiding: false }, // 不可拖拽（设置了 allowHiding: false）
  { id: 'status', label: '状态' }, // 可拖拽
  { id: 'createTime', label: '创建时间' }, // 可拖拽
  { id: 'actions', label: '操作' }, // 固定列，不可拖拽
];
```

### 拖拽排序规则

1. **可拖拽的列**：
   - 普通数据列（默认 `allowHiding: true`）
   - 显示菜单图标（`custom:menu-duotone`）和拖拽手柄图标（`solar:drag-vertical-bold`）
   - 鼠标悬停时显示 `grab` 光标和主题色高亮
   - 支持圆角卡片式布局和悬停效果

2. **不可拖拽的列**：
   - `id` 列（主键列）
   - `action` 和 `actions` 列（操作列）
   - 设置了 `allowHiding: false` 的列
   - 显示锁定图标（`solar:lock-keyhole-bold`）
   - 鼠标悬停时显示 `default` 光标，半透明显示

3. **拖拽交互**：
   - 拖拽时列项会有半透明效果
   - 拖拽中的列项会有阴影、背景色变化和主题色边框
   - 支持键盘导航（空格键激活拖拽，方向键移动）
   - 平滑的过渡动画效果
   - **事件分离**：拖拽事件作用于整个列项，显示/隐藏切换事件仅作用于右侧眼睛图标，避免事件冲突

4. **UI 增强功能**：
   - **批量操作按钮**：快速显示全部/隐藏全部列
   - **统计信息**：底部显示可见列数量统计
   - **状态指示器**：彩色圆点区分可见/隐藏状态
   - **操作提示**：悬停时的工具提示说明
   - **优化滚动条**：
     - 隐藏横向滚动条（`overflowX: 'hidden'`）
     - 纵向滚动条宽度为 6px，更加精细
     - 支持 Webkit 和 Firefox 浏览器
     - 滚动条悬停时颜色加深
     - 圆角设计，与整体UI风格一致

### 交互模型

#### 事件分离设计

为了避免拖拽和点击事件冲突，组件采用了精确的事件分离策略：

1. **拖拽区域**：
   - 仅左侧拖拽手柄响应拖拽事件
   - 拖拽手柄有独立的悬停效果和 `grab/grabbing` 光标
   - 限制为垂直方向拖拽，禁止横向移动
   - 激活距离：8px，避免意外触发

2. **切换区域**：
   - 仅右侧眼睛图标响应点击事件
   - 独立的悬停效果和点击区域
   - 使用 `e.stopPropagation()` 阻止事件冒泡

3. **列项主体**：
   - 不响应拖拽或点击事件
   - 提供统一的悬停效果
   - 鼠标光标保持为 `default`

4. **固定列处理**：
   - 固定列不响应任何交互事件
   - 显示锁定图标和半透明样式
   - 鼠标光标保持为 `default`

#### 用户操作指南

- **拖拽排序**：点击并拖拽左侧的菜单图标（仅垂直方向）
- **切换显示**：点击右侧的眼睛图标
- **键盘操作**：使用Tab键导航到拖拽手柄，空格键激活拖拽，方向键移动
- **视觉反馈**：拖拽手柄悬停时变色，拖拽中的列项有边框高亮

### 使用示例

```jsx
import { TableView } from 'src/components/table';

const MyTable = () => {
  const columns = [
    { id: 'id', label: 'ID' }, // 不可拖拽
    { id: 'name', label: '名称' }, // 可拖拽
    { id: 'email', label: '邮箱' }, // 可拖拽
    { id: 'status', label: '状态' }, // 可拖拽
    { id: 'important', label: '重要信息', allowHiding: false }, // 不可拖拽
    { id: 'actions', label: '操作' }, // 不可拖拽
  ];

  const getData = async (page, rowsPerPage, orderBy, order, searchValue) => {
    // 数据获取逻辑
    return [data, total];
  };

  return (
    <TableView
      columns={columns}
      getData={getData}
      // 其他属性...
    />
  );
};
```

### 技术实现

组件使用 `@dnd-kit` 库实现拖拽功能：

- **DndContext**：提供拖拽上下文
- **SortableContext**：管理可排序项目
- **useSortable**：为每个列项提供拖拽能力
- **arrayMove**：处理数组重新排序

拖拽排序的状态管理集成在 `useColumnVisibility` Hook 中，确保列顺序与可见性状态同步。

## selectedActions 使用指南

TableView 组件支持通过 `selectedActions` 参数自定义选中项的操作按钮，提供了灵活的批量操作功能。

### 基本用法

```jsx
import { TableView } from 'src/components/table';

const MyTable = () => {
  const selectedActions = [
    {
      key: 'export',
      label: '导出',
      icon: 'solar:export-bold',
      onClick: (selectedIds) => handleExport(selectedIds),
      color: 'primary',
    },
    {
      key: 'archive',
      label: '归档',
      icon: 'solar:archive-bold', 
      onClick: (selectedIds) => handleArchive(selectedIds),
      color: 'warning',
    },
  ];

  return (
    <TableView
      columns={columns}
      getData={getData}
      selectedActions={selectedActions}
      onDelete={handleDelete} // 可选：提供默认删除操作
    />
  );
};
```

### selectedActions 配置项

每个 action 对象支持以下属性：

| 属性 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `key` | `string` | ✅ | - | 唯一标识符 |
| `label` | `string` | ✅ | - | 按钮显示文本 |
| `icon` | `string` | ❌ | - | Iconify 图标名称 |
| `onClick` | `function` | ✅ | - | 点击回调函数，参数为 selectedIds 数组 |
| `color` | `string` | ❌ | `'primary'` | 按钮颜色：`primary`、`error`、`warning`、`info`、`success` |
| `variant` | `string` | ❌ | `'text'` | 按钮变体：`text`、`outlined`、`contained` |
| `buttonProps` | `object` | ❌ | `{}` | 传递给 Button 组件的额外属性 |

### 默认删除操作

当提供 `onDelete` 回调函数时，系统会自动添加一个默认的删除操作：

```jsx
<TableView
  columns={columns}
  getData={getData}
  onDelete={handleDelete} // 自动添加删除按钮
  selectedActions={customActions} // 自定义操作
/>
```

默认删除操作配置：
```javascript
{
  key: 'delete',
  label: 'Delete',
  icon: 'solar:trash-bin-trash-bold',
  onClick: () => onDelete(selectedIds),
  color: 'error',
}
```

### 高级用法示例

#### 1. 带确认对话框的操作

```