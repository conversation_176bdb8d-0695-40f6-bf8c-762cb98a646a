import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';

import { EmptyContent } from '../empty-content';

// ----------------------------------------------------------------------

export function TableNoData({ notFound, sx, title, colSpan = 1 }) {
  if (!notFound) {
    return null;
  }

  return (
    <TableRow>
      {/* The sx prop passed to TableNoData is intended for the EmptyContent component */}
      <TableCell
        colSpan={colSpan}
        sx={{
          height: {
            xs: 'calc(50vh - 40px)', // 小屏幕手机上使用较小的高度，减去20px
            sm: 'calc(55vh - 40px)', // 平板上的高度，减去20px
            md: 'calc(60vh - 40px)', // 普通笔记本电脑的高度，减去20px
            lg: 'calc(65vh - 40px)', // 大屏幕笔记本电脑的高度，减去20px
            xl: 'calc(70vh - 40px)', // 桌面显示器的高度，减去20px
          },
          verticalAlign: 'middle', // 垂直居中对齐
          position: 'relative', // 为了确保内容能够正确定位
        }}
      >
        <EmptyContent
          filled
          title={title}
          sx={sx} // Pass the sx prop from TableNoData to EmptyContent
        />
      </TableCell>
    </TableRow>
  );
}
