import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router';

import { paths } from 'src/routes/paths';

import { useAppSettings } from 'src/hooks/use-app-settings';

import { SplashScreen } from 'src/components/loading-screen';

// ----------------------------------------------------------------------

// 锁屏状态存储键
export const LOCK_STORAGE_KEY = 'app_lock_status';

// 检查是否处于锁定状态
export const isLocked = () => {
  const lockStatus = sessionStorage.getItem(LOCK_STORAGE_KEY);
  return lockStatus === 'locked';
};

// 设置锁定状态
export const setLockStatus = (status) => {
  if (status) {
    sessionStorage.setItem(LOCK_STORAGE_KEY, 'locked');
  } else {
    sessionStorage.removeItem(LOCK_STORAGE_KEY);
  }
};

// 检查是否应该启用锁屏功能
export const shouldEnableLockScreen = (settings, hasPassword) => {
  // 如果没有设置密码，不启用锁屏
  if (!hasPassword) {
    return false;
  }

  // 如果autoLock设置为Never(-1)，不启用锁屏
  if (settings.security.autoLock === -1) {
    return false;
  }

  return true;
};

// 锁定屏幕
export const lockScreen = (navigate, currentPath) => {
  setLockStatus(true);
  navigate(paths.lock, {
    state: { from: currentPath },
    replace: true,
  });
};

// 解锁屏幕
export const unlockScreen = () => {
  setLockStatus(false);
};

// ----------------------------------------------------------------------

export function LockGuard({ children }) {
  const navigate = useNavigate();
  const location = useLocation();
  const pathname = location.pathname;
  const { settings, hasPassword } = useAppSettings();

  const [isChecking, setIsChecking] = useState(true);

  const checkLockStatus = () => {
    // 如果当前已经在锁屏页面，不需要再次检查
    if (pathname === paths.lock) {
      setIsChecking(false);
      return;
    }

    // 检查是否应该启用锁屏功能
    if (!shouldEnableLockScreen(settings, hasPassword)) {
      // 如果不应该启用锁屏，清除任何现有的锁定状态
      setLockStatus(false);
      setIsChecking(false);
      return;
    }

    // 检查是否处于锁定状态
    if (isLocked()) {
      // 如果处于锁定状态，重定向到锁屏页面
      lockScreen(navigate, pathname);
      return;
    }

    setIsChecking(false);
  };

  useEffect(() => {
    checkLockStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname, hasPassword, settings.security.autoLock]);

  if (isChecking) {
    return <SplashScreen />;
  }

  return <>{children}</>;
}
