import { useNavigate } from 'react-router';
import { useState, useEffect, useCallback } from 'react';

import { useAppSettings } from 'src/hooks/use-app-settings';

import { lockScreen, shouldEnableLockScreen } from 'src/components/auto-lock/lock-guard';

// ----------------------------------------------------------------------

// 默认的自动锁屏时间（毫秒）
const DEFAULT_IDLE_TIME = 1 * 60 * 1000; // 1分钟

// 全局的AutoLock重置回调
let globalResetAutoLock = null;

export default function AutoLock({ idleTime = DEFAULT_IDLE_TIME }) {
  const navigate = useNavigate();
  const { settings, hasPassword } = useAppSettings();
  const [lastActivity, setLastActivity] = useState(Date.now());

  // 重置计时器
  const resetTimer = useCallback(() => {
    setLastActivity(Date.now());
  }, []);

  // 强制重置AutoLock（清除所有状态，重新开始）
  const forceReset = useCallback(() => {
    setLastActivity(Date.now());
  }, []);

  // 注册全局重置回调
  useEffect(() => {
    globalResetAutoLock = forceReset;
    return () => {
      globalResetAutoLock = null;
    };
  }, [forceReset]);

  // 用户活动事件处理函数
  const handleUserActivity = useCallback(() => {
    resetTimer();
  }, [resetTimer]);

  // 根据设置计算实际的锁屏时间
  const getActualIdleTime = useCallback(() => {
    // 检查是否应该启用锁屏功能
    if (!shouldEnableLockScreen(settings, hasPassword)) {
      return null; // 返回null表示不启用自动锁屏
    }

    const autoLockMinutes = settings.security.autoLock;

    // 如果设置为0（总是），使用很短的时间（10秒）
    if (autoLockMinutes === 0) {
      return 10 * 1000; // 10秒
    }

    // 如果设置为-1（从不），返回null（不启用）
    if (autoLockMinutes === -1) {
      return null;
    }

    // 其他情况，将分钟转换为毫秒
    return autoLockMinutes * 60 * 1000;
  }, [settings, hasPassword]);

  useEffect(() => {
    const actualIdleTime = getActualIdleTime();

    // 如果不应该启用自动锁屏，直接返回
    if (actualIdleTime === null) {
      return undefined;
    }

    // 添加用户活动事件监听器
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];

    events.forEach((event) => {
      window.addEventListener(event, handleUserActivity);
    });

    // 设置定时器，定期检查用户是否空闲
    const timer = setInterval(() => {
      const currentTime = Date.now();
      const idleElapsed = currentTime - lastActivity;

      if (idleElapsed >= actualIdleTime) {
        // 用户空闲时间超过设定值，锁定屏幕
        lockScreen(navigate, window.location.pathname);
        clearInterval(timer);
      }
    }, 10000); // 每10秒检查一次

    // 清理函数
    return () => {
      // 移除事件监听器
      events.forEach((event) => {
        window.removeEventListener(event, handleUserActivity);
      });

      // 清除定时器
      clearInterval(timer);
    };
  }, [lastActivity, navigate, handleUserActivity, getActualIdleTime]);

  // 此组件不渲染任何UI
  return null;
}

// 导出全局重置函数
export const resetAutoLock = () => {
  if (globalResetAutoLock) {
    globalResetAutoLock();
  }
};
