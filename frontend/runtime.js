var Fe=Object.defineProperty,an=Object.defineProperties;var ln=Object.getOwnPropertyDescriptors;var ke=Object.getOwnPropertySymbols;var cn=Object.prototype.hasOwnProperty,dn=Object.prototype.propertyIsEnumerable;var Oe=(n,e,i)=>e in n?Fe(n,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):n[e]=i,Le=(n,e)=>{for(var i in e||(e={}))cn.call(e,i)&&Oe(n,i,e[i]);if(ke)for(var i of ke(e))dn.call(e,i)&&Oe(n,i,e[i]);return n},Ue=(n,e)=>an(n,ln(e));var m=(n,e)=>{for(var i in e)Fe(n,i,{get:e[i],enumerable:!0})};var ce={};m(ce,{Application:()=>ve,Browser:()=>_,Call:()=>Te,CancelError:()=>k,CancellablePromise:()=>j,CancelledRejectionError:()=>W,Clipboard:()=>xe,Create:()=>Ae,Dialogs:()=>ee,Events:()=>oe,Flags:()=>we,Screens:()=>Ee,System:()=>me,WML:()=>le,Window:()=>Z});var le={};m(le,{Enable:()=>ae,Reload:()=>Ke});var _={};m(_,{OpenURL:()=>q});var un="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";function P(n=21){let e="",i=n|0;for(;i--;)e+=un[Math.random()*64|0];return e}var mn=window.location.origin+"/wails/runtime",d=Object.freeze({Call:0,Clipboard:1,Application:2,Events:3,ContextMenu:4,Dialog:5,Window:6,Screens:7,System:8,Browser:9,CancelCall:10}),wn=P();function u(n,e=""){return function(i,o=null){return pn(n,i,e,o)}}async function pn(n,e,i,o){var l,c;let t=new URL(mn);t.searchParams.append("object",n.toString()),t.searchParams.append("method",e.toString()),o&&t.searchParams.append("args",JSON.stringify(o));let r={"x-wails-client-id":wn};i&&(r["x-wails-window-name"]=i);let a=await fetch(t,{headers:r});if(!a.ok)throw new Error(await a.text());return((c=(l=a.headers.get("Content-Type"))==null?void 0:l.indexOf("application/json"))!=null?c:-1)!==-1?a.json():a.text()}var fn=u(d.Browser),gn=0;function q(n){return fn(gn,{url:n.toString()})}var ee={};m(ee,{Error:()=>An,Info:()=>Tn,OpenFile:()=>Rn,Question:()=>$,SaveFile:()=>En,Warning:()=>xn});window._wails=window._wails||{};window._wails.dialogErrorCallback=Mn;window._wails.dialogResultCallback=Dn;var hn=u(d.Dialog),F=new Map,Wn=0,bn=1,vn=2,yn=3,Cn=4,Pn=5;function Dn(n,e,i){let o=Ie(n);if(o)if(i)try{o.resolve(JSON.parse(e))}catch(t){o.reject(new TypeError("could not parse result: "+t.message,{cause:t}))}else o.resolve(e)}function Mn(n,e){var i;(i=Ie(n))==null||i.reject(new window.Error(e))}function Ie(n){let e=F.get(n);return F.delete(n),e}function Sn(){let n;do n=P();while(F.has(n));return n}function D(n,e={}){let i=Sn();return new Promise((o,t)=>{F.set(i,{resolve:o,reject:t}),hn(n,Object.assign({"dialog-id":i},e)).catch(r=>{F.delete(i),t(r)})})}function Tn(n){return D(Wn,n)}function xn(n){return D(bn,n)}function An(n){return D(vn,n)}function $(n){return D(yn,n)}function Rn(n){var e;return(e=D(Cn,n))!=null?e:[]}function En(n){return D(Pn,n)}var oe={};m(oe,{Emit:()=>ie,Off:()=>In,OffAll:()=>zn,On:()=>Ln,OnMultiple:()=>ne,Once:()=>Un,Types:()=>je,WailsEvent:()=>M});var p=new Map,B=class{constructor(e,i,o){this.eventName=e,this.callback=i,this.maxCallbacks=o||-1}dispatch(e){try{this.callback(e)}catch(i){}return this.maxCallbacks===-1?!1:(this.maxCallbacks-=1,this.maxCallbacks===0)}};function ze(n){let e=p.get(n.eventName);e&&(e=e.filter(i=>i!==n),e.length===0?p.delete(n.eventName):p.set(n.eventName,e))}var je=Object.freeze({Windows:Object.freeze({APMPowerSettingChange:"windows:APMPowerSettingChange",APMPowerStatusChange:"windows:APMPowerStatusChange",APMResumeAutomatic:"windows:APMResumeAutomatic",APMResumeSuspend:"windows:APMResumeSuspend",APMSuspend:"windows:APMSuspend",ApplicationStarted:"windows:ApplicationStarted",SystemThemeChanged:"windows:SystemThemeChanged",WebViewNavigationCompleted:"windows:WebViewNavigationCompleted",WindowActive:"windows:WindowActive",WindowBackgroundErase:"windows:WindowBackgroundErase",WindowClickActive:"windows:WindowClickActive",WindowClosing:"windows:WindowClosing",WindowDidMove:"windows:WindowDidMove",WindowDidResize:"windows:WindowDidResize",WindowDPIChanged:"windows:WindowDPIChanged",WindowDragDrop:"windows:WindowDragDrop",WindowDragEnter:"windows:WindowDragEnter",WindowDragLeave:"windows:WindowDragLeave",WindowDragOver:"windows:WindowDragOver",WindowEndMove:"windows:WindowEndMove",WindowEndResize:"windows:WindowEndResize",WindowFullscreen:"windows:WindowFullscreen",WindowHide:"windows:WindowHide",WindowInactive:"windows:WindowInactive",WindowKeyDown:"windows:WindowKeyDown",WindowKeyUp:"windows:WindowKeyUp",WindowKillFocus:"windows:WindowKillFocus",WindowNonClientHit:"windows:WindowNonClientHit",WindowNonClientMouseDown:"windows:WindowNonClientMouseDown",WindowNonClientMouseLeave:"windows:WindowNonClientMouseLeave",WindowNonClientMouseMove:"windows:WindowNonClientMouseMove",WindowNonClientMouseUp:"windows:WindowNonClientMouseUp",WindowPaint:"windows:WindowPaint",WindowRestore:"windows:WindowRestore",WindowSetFocus:"windows:WindowSetFocus",WindowShow:"windows:WindowShow",WindowStartMove:"windows:WindowStartMove",WindowStartResize:"windows:WindowStartResize",WindowUnFullscreen:"windows:WindowUnFullscreen",WindowZOrderChanged:"windows:WindowZOrderChanged",WindowMinimise:"windows:WindowMinimise",WindowUnMinimise:"windows:WindowUnMinimise",WindowMaximise:"windows:WindowMaximise",WindowUnMaximise:"windows:WindowUnMaximise"}),Mac:Object.freeze({ApplicationDidBecomeActive:"mac:ApplicationDidBecomeActive",ApplicationDidChangeBackingProperties:"mac:ApplicationDidChangeBackingProperties",ApplicationDidChangeEffectiveAppearance:"mac:ApplicationDidChangeEffectiveAppearance",ApplicationDidChangeIcon:"mac:ApplicationDidChangeIcon",ApplicationDidChangeOcclusionState:"mac:ApplicationDidChangeOcclusionState",ApplicationDidChangeScreenParameters:"mac:ApplicationDidChangeScreenParameters",ApplicationDidChangeStatusBarFrame:"mac:ApplicationDidChangeStatusBarFrame",ApplicationDidChangeStatusBarOrientation:"mac:ApplicationDidChangeStatusBarOrientation",ApplicationDidChangeTheme:"mac:ApplicationDidChangeTheme",ApplicationDidFinishLaunching:"mac:ApplicationDidFinishLaunching",ApplicationDidHide:"mac:ApplicationDidHide",ApplicationDidResignActive:"mac:ApplicationDidResignActive",ApplicationDidUnhide:"mac:ApplicationDidUnhide",ApplicationDidUpdate:"mac:ApplicationDidUpdate",ApplicationShouldHandleReopen:"mac:ApplicationShouldHandleReopen",ApplicationWillBecomeActive:"mac:ApplicationWillBecomeActive",ApplicationWillFinishLaunching:"mac:ApplicationWillFinishLaunching",ApplicationWillHide:"mac:ApplicationWillHide",ApplicationWillResignActive:"mac:ApplicationWillResignActive",ApplicationWillTerminate:"mac:ApplicationWillTerminate",ApplicationWillUnhide:"mac:ApplicationWillUnhide",ApplicationWillUpdate:"mac:ApplicationWillUpdate",MenuDidAddItem:"mac:MenuDidAddItem",MenuDidBeginTracking:"mac:MenuDidBeginTracking",MenuDidClose:"mac:MenuDidClose",MenuDidDisplayItem:"mac:MenuDidDisplayItem",MenuDidEndTracking:"mac:MenuDidEndTracking",MenuDidHighlightItem:"mac:MenuDidHighlightItem",MenuDidOpen:"mac:MenuDidOpen",MenuDidPopUp:"mac:MenuDidPopUp",MenuDidRemoveItem:"mac:MenuDidRemoveItem",MenuDidSendAction:"mac:MenuDidSendAction",MenuDidSendActionToItem:"mac:MenuDidSendActionToItem",MenuDidUpdate:"mac:MenuDidUpdate",MenuWillAddItem:"mac:MenuWillAddItem",MenuWillBeginTracking:"mac:MenuWillBeginTracking",MenuWillDisplayItem:"mac:MenuWillDisplayItem",MenuWillEndTracking:"mac:MenuWillEndTracking",MenuWillHighlightItem:"mac:MenuWillHighlightItem",MenuWillOpen:"mac:MenuWillOpen",MenuWillPopUp:"mac:MenuWillPopUp",MenuWillRemoveItem:"mac:MenuWillRemoveItem",MenuWillSendAction:"mac:MenuWillSendAction",MenuWillSendActionToItem:"mac:MenuWillSendActionToItem",MenuWillUpdate:"mac:MenuWillUpdate",WebViewDidCommitNavigation:"mac:WebViewDidCommitNavigation",WebViewDidFinishNavigation:"mac:WebViewDidFinishNavigation",WebViewDidReceiveServerRedirectForProvisionalNavigation:"mac:WebViewDidReceiveServerRedirectForProvisionalNavigation",WebViewDidStartProvisionalNavigation:"mac:WebViewDidStartProvisionalNavigation",WindowDidBecomeKey:"mac:WindowDidBecomeKey",WindowDidBecomeMain:"mac:WindowDidBecomeMain",WindowDidBeginSheet:"mac:WindowDidBeginSheet",WindowDidChangeAlpha:"mac:WindowDidChangeAlpha",WindowDidChangeBackingLocation:"mac:WindowDidChangeBackingLocation",WindowDidChangeBackingProperties:"mac:WindowDidChangeBackingProperties",WindowDidChangeCollectionBehavior:"mac:WindowDidChangeCollectionBehavior",WindowDidChangeEffectiveAppearance:"mac:WindowDidChangeEffectiveAppearance",WindowDidChangeOcclusionState:"mac:WindowDidChangeOcclusionState",WindowDidChangeOrderingMode:"mac:WindowDidChangeOrderingMode",WindowDidChangeScreen:"mac:WindowDidChangeScreen",WindowDidChangeScreenParameters:"mac:WindowDidChangeScreenParameters",WindowDidChangeScreenProfile:"mac:WindowDidChangeScreenProfile",WindowDidChangeScreenSpace:"mac:WindowDidChangeScreenSpace",WindowDidChangeScreenSpaceProperties:"mac:WindowDidChangeScreenSpaceProperties",WindowDidChangeSharingType:"mac:WindowDidChangeSharingType",WindowDidChangeSpace:"mac:WindowDidChangeSpace",WindowDidChangeSpaceOrderingMode:"mac:WindowDidChangeSpaceOrderingMode",WindowDidChangeTitle:"mac:WindowDidChangeTitle",WindowDidChangeToolbar:"mac:WindowDidChangeToolbar",WindowDidDeminiaturize:"mac:WindowDidDeminiaturize",WindowDidEndSheet:"mac:WindowDidEndSheet",WindowDidEnterFullScreen:"mac:WindowDidEnterFullScreen",WindowDidEnterVersionBrowser:"mac:WindowDidEnterVersionBrowser",WindowDidExitFullScreen:"mac:WindowDidExitFullScreen",WindowDidExitVersionBrowser:"mac:WindowDidExitVersionBrowser",WindowDidExpose:"mac:WindowDidExpose",WindowDidFocus:"mac:WindowDidFocus",WindowDidMiniaturize:"mac:WindowDidMiniaturize",WindowDidMove:"mac:WindowDidMove",WindowDidOrderOffScreen:"mac:WindowDidOrderOffScreen",WindowDidOrderOnScreen:"mac:WindowDidOrderOnScreen",WindowDidResignKey:"mac:WindowDidResignKey",WindowDidResignMain:"mac:WindowDidResignMain",WindowDidResize:"mac:WindowDidResize",WindowDidUpdate:"mac:WindowDidUpdate",WindowDidUpdateAlpha:"mac:WindowDidUpdateAlpha",WindowDidUpdateCollectionBehavior:"mac:WindowDidUpdateCollectionBehavior",WindowDidUpdateCollectionProperties:"mac:WindowDidUpdateCollectionProperties",WindowDidUpdateShadow:"mac:WindowDidUpdateShadow",WindowDidUpdateTitle:"mac:WindowDidUpdateTitle",WindowDidUpdateToolbar:"mac:WindowDidUpdateToolbar",WindowDidZoom:"mac:WindowDidZoom",WindowFileDraggingEntered:"mac:WindowFileDraggingEntered",WindowFileDraggingExited:"mac:WindowFileDraggingExited",WindowFileDraggingPerformed:"mac:WindowFileDraggingPerformed",WindowHide:"mac:WindowHide",WindowMaximise:"mac:WindowMaximise",WindowUnMaximise:"mac:WindowUnMaximise",WindowMinimise:"mac:WindowMinimise",WindowUnMinimise:"mac:WindowUnMinimise",WindowShouldClose:"mac:WindowShouldClose",WindowShow:"mac:WindowShow",WindowWillBecomeKey:"mac:WindowWillBecomeKey",WindowWillBecomeMain:"mac:WindowWillBecomeMain",WindowWillBeginSheet:"mac:WindowWillBeginSheet",WindowWillChangeOrderingMode:"mac:WindowWillChangeOrderingMode",WindowWillClose:"mac:WindowWillClose",WindowWillDeminiaturize:"mac:WindowWillDeminiaturize",WindowWillEnterFullScreen:"mac:WindowWillEnterFullScreen",WindowWillEnterVersionBrowser:"mac:WindowWillEnterVersionBrowser",WindowWillExitFullScreen:"mac:WindowWillExitFullScreen",WindowWillExitVersionBrowser:"mac:WindowWillExitVersionBrowser",WindowWillFocus:"mac:WindowWillFocus",WindowWillMiniaturize:"mac:WindowWillMiniaturize",WindowWillMove:"mac:WindowWillMove",WindowWillOrderOffScreen:"mac:WindowWillOrderOffScreen",WindowWillOrderOnScreen:"mac:WindowWillOrderOnScreen",WindowWillResignMain:"mac:WindowWillResignMain",WindowWillResize:"mac:WindowWillResize",WindowWillUnfocus:"mac:WindowWillUnfocus",WindowWillUpdate:"mac:WindowWillUpdate",WindowWillUpdateAlpha:"mac:WindowWillUpdateAlpha",WindowWillUpdateCollectionBehavior:"mac:WindowWillUpdateCollectionBehavior",WindowWillUpdateCollectionProperties:"mac:WindowWillUpdateCollectionProperties",WindowWillUpdateShadow:"mac:WindowWillUpdateShadow",WindowWillUpdateTitle:"mac:WindowWillUpdateTitle",WindowWillUpdateToolbar:"mac:WindowWillUpdateToolbar",WindowWillUpdateVisibility:"mac:WindowWillUpdateVisibility",WindowWillUseStandardFrame:"mac:WindowWillUseStandardFrame",WindowZoomIn:"mac:WindowZoomIn",WindowZoomOut:"mac:WindowZoomOut",WindowZoomReset:"mac:WindowZoomReset"}),Linux:Object.freeze({ApplicationStartup:"linux:ApplicationStartup",SystemThemeChanged:"linux:SystemThemeChanged",WindowDeleteEvent:"linux:WindowDeleteEvent",WindowDidMove:"linux:WindowDidMove",WindowDidResize:"linux:WindowDidResize",WindowFocusIn:"linux:WindowFocusIn",WindowFocusOut:"linux:WindowFocusOut",WindowLoadChanged:"linux:WindowLoadChanged"}),Common:Object.freeze({ApplicationOpenedWithFile:"ApplicationOpenedWithFile",ApplicationStarted:"ApplicationStarted",ThemeChanged:"ThemeChanged",WindowClosing:"WindowClosing",WindowDidMove:"WindowDidMove",WindowDidResize:"WindowDidResize",WindowDPIChanged:"WindowDPIChanged",WindowFilesDropped:"WindowFilesDropped",WindowFocus:"WindowFocus",WindowFullscreen:"WindowFullscreen",WindowHide:"WindowHide",WindowLostFocus:"WindowLostFocus",WindowMaximise:"WindowMaximise",WindowMinimise:"WindowMinimise",WindowRestore:"WindowRestore",WindowRuntimeReady:"WindowRuntimeReady",WindowShow:"WindowShow",WindowUnFullscreen:"WindowUnFullscreen",WindowUnMaximise:"WindowUnMaximise",WindowUnMinimise:"WindowUnMinimise",WindowZoom:"WindowZoom",WindowZoomIn:"WindowZoomIn",WindowZoomOut:"WindowZoomOut",WindowZoomReset:"WindowZoomReset"})});window._wails=window._wails||{};window._wails.dispatchWailsEvent=Fn;var kn=u(d.Events),On=0,M=class{constructor(e,i=null){this.name=e,this.data=i}};function Fn(n){let e=p.get(n.name);if(!e)return;let i=new M(n.name,n.data);"sender"in n&&(i.sender=n.sender),e=e.filter(o=>!o.dispatch(i)),e.length===0?p.delete(n.name):p.set(n.name,e)}function ne(n,e,i){let o=p.get(n)||[],t=new B(n,e,i);return o.push(t),p.set(n,o),()=>ze(t)}function Ln(n,e){return ne(n,e,-1)}function Un(n,e){return ne(n,e,1)}function In(...n){n.forEach(e=>p.delete(e))}function zn(){p.clear()}function ie(n,e){let i;return typeof n=="object"&&n!==null&&"name"in n&&"data"in n?i=new M(n.name,n.data):i=new M(n,e),kn(On,i)}function Be(){return new MouseEvent("mousedown").buttons===0}function He(){if(!EventTarget||!AbortSignal||!AbortController)return!1;let n=!0,e=new EventTarget,i=new AbortController;return e.addEventListener("test",()=>{n=!1},{signal:i.signal}),i.abort(),e.dispatchEvent(new CustomEvent("test")),n}function H(n){var e;return n.target instanceof HTMLElement?n.target:!(n.target instanceof HTMLElement)&&n.target instanceof Node&&(e=n.target.parentElement)!=null?e:document.body}var Ne=!1;document.addEventListener("DOMContentLoaded",()=>{Ne=!0});function Ze(n){Ne||document.readyState==="complete"?n():document.addEventListener("DOMContentLoaded",n)}var jn=0,Bn=1,Hn=2,Nn=3,Zn=4,Vn=5,Gn=6,Kn=7,Yn=8,Xn=9,Jn=10,Qn=11,qn=12,_n=13,$n=14,ei=15,ni=16,ii=17,oi=18,ti=19,ri=20,si=21,ai=22,li=23,ci=24,di=25,ui=26,mi=27,wi=28,pi=29,fi=30,gi=31,hi=32,Wi=33,bi=34,vi=35,yi=36,Ci=37,Pi=38,Di=39,Mi=40,Si=41,Ti=42,xi=43,Ai=44,Ri=45,Ei=46,ki=47,s=Symbol("caller");s;var N=class N{constructor(e=""){this[s]=u(d.Window,e);for(let i of Object.getOwnPropertyNames(N.prototype))i!=="constructor"&&typeof this[i]=="function"&&(this[i]=this[i].bind(this))}Get(e){return new N(e)}Position(){return this[s](jn)}Center(){return this[s](Bn)}Close(){return this[s](Hn)}DisableSizeConstraints(){return this[s](Nn)}EnableSizeConstraints(){return this[s](Zn)}Focus(){return this[s](Vn)}ForceReload(){return this[s](Gn)}Fullscreen(){return this[s](Kn)}GetScreen(){return this[s](Yn)}GetZoom(){return this[s](Xn)}Height(){return this[s](Jn)}Hide(){return this[s](Qn)}IsFocused(){return this[s](qn)}IsFullscreen(){return this[s](_n)}IsMaximised(){return this[s]($n)}IsMinimised(){return this[s](ei)}Maximise(){return this[s](ni)}Minimise(){return this[s](ii)}Name(){return this[s](oi)}OpenDevTools(){return this[s](ti)}RelativePosition(){return this[s](ri)}Reload(){return this[s](si)}Resizable(){return this[s](ai)}Restore(){return this[s](li)}SetPosition(e,i){return this[s](ci,{x:e,y:i})}SetAlwaysOnTop(e){return this[s](di,{alwaysOnTop:e})}SetBackgroundColour(e,i,o,t){return this[s](ui,{r:e,g:i,b:o,a:t})}SetFrameless(e){return this[s](mi,{frameless:e})}SetFullscreenButtonEnabled(e){return this[s](wi,{enabled:e})}SetMaxSize(e,i){return this[s](pi,{width:e,height:i})}SetMinSize(e,i){return this[s](fi,{width:e,height:i})}SetRelativePosition(e,i){return this[s](gi,{x:e,y:i})}SetResizable(e){return this[s](hi,{resizable:e})}SetSize(e,i){return this[s](Wi,{width:e,height:i})}SetTitle(e){return this[s](bi,{title:e})}SetZoom(e){return this[s](vi,{zoom:e})}Show(){return this[s](yi)}Size(){return this[s](Ci)}ToggleFullscreen(){return this[s](Pi)}ToggleMaximise(){return this[s](Di)}UnFullscreen(){return this[s](Mi)}UnMaximise(){return this[s](Si)}UnMinimise(){return this[s](Ti)}Width(){return this[s](xi)}Zoom(){return this[s](Ai)}ZoomIn(){return this[s](Ri)}ZoomOut(){return this[s](Ei)}ZoomReset(){return this[s](ki)}},te=N,Oi=new te(""),Z=Oi;function Fi(n,e=null){ie(n,e)}function Li(n,e){let i=Z.Get(n),o=i[e];if(typeof o=="function")try{o.call(i)}catch(t){}}function Ve(n){let e=n.currentTarget;function i(t="Yes"){if(t!=="Yes")return;let r=e.getAttribute("wml-event")||e.getAttribute("data-wml-event"),a=e.getAttribute("wml-target-window")||e.getAttribute("data-wml-target-window")||"",l=e.getAttribute("wml-window")||e.getAttribute("data-wml-window"),c=e.getAttribute("wml-openurl")||e.getAttribute("data-wml-openurl");r!==null&&Fi(r),l!==null&&Li(a,l),c!==null&&q(c)}let o=e.getAttribute("wml-confirm")||e.getAttribute("data-wml-confirm");o?$({Title:"Confirm",Message:o,Detached:!1,Buttons:[{Label:"Yes"},{Label:"No",IsDefault:!0}]}).then(i):i()}var L=Symbol("controller"),S=Symbol("triggerMap"),b=Symbol("elementCount");L;var re=class{constructor(){this[L]=new AbortController}set(e,i){return{signal:this[L].signal}}reset(){this[L].abort(),this[L]=new AbortController}};S,b;var se=class{constructor(){this[S]=new WeakMap,this[b]=0}set(e,i){return this[S].has(e)||this[b]++,this[S].set(e,i),{}}reset(){if(!(this[b]<=0)){for(let e of document.body.querySelectorAll("*")){if(this[b]<=0)break;let i=this[S].get(e);i!=null&&this[b]--;for(let o of i||[])e.removeEventListener(o,Ve)}this[S]=new WeakMap,this[b]=0}}},Ge=He()?new re:new se;function Ui(n){let e=/\S+/g,i=n.getAttribute("wml-trigger")||n.getAttribute("data-wml-trigger")||"click",o=[],t;for(;(t=e.exec(i))!==null;)o.push(t[0]);let r=Ge.set(n,o);for(let a of o)n.addEventListener(a,Ve,r)}function ae(){Ze(Ke)}function Ke(){Ge.reset(),document.body.querySelectorAll("[wml-event], [wml-window], [wml-openurl], [data-wml-event], [data-wml-window], [data-wml-openurl]").forEach(Ui)}window.wails=ce;ae();var me={};m(me,{Capabilities:()=>Bi,Environment:()=>Hi,IsAMD64:()=>Vi,IsARM:()=>Gi,IsARM64:()=>Ki,IsDarkMode:()=>ji,IsDebug:()=>ue,IsLinux:()=>Ni,IsMac:()=>Zi,IsWindows:()=>V,invoke:()=>v});var Ye=u(d.System),Ii=0,zi=1,de=function(){var n,e,i,o,t;try{if((e=(n=window.chrome)==null?void 0:n.webview)!=null&&e.postMessage)return window.chrome.webview.postMessage.bind(window.chrome.webview);if((t=(o=(i=window.webkit)==null?void 0:i.messageHandlers)==null?void 0:o.external)!=null&&t.postMessage)return window.webkit.messageHandlers.external.postMessage.bind(window.webkit.messageHandlers.external)}catch(r){}return null}();function v(n){de==null||de(n)}function ji(){return Ye(Ii)}async function Bi(){let n=await fetch("/wails/capabilities");if(n.ok)return n.json();throw new Error("could not fetch capabilities: "+n.statusText)}function Hi(){return Ye(zi)}function V(){return window._wails.environment.OS==="windows"}function Ni(){return window._wails.environment.OS==="linux"}function Zi(){return window._wails.environment.OS==="darwin"}function Vi(){return window._wails.environment.Arch==="amd64"}function Gi(){return window._wails.environment.Arch==="arm"}function Ki(){return window._wails.environment.Arch==="arm64"}function ue(){return!!window._wails.environment.Debug}window.addEventListener("contextmenu",Qi);var Yi=u(d.ContextMenu),Xi=0;function Ji(n,e,i,o){Yi(Xi,{id:n,x:e,y:i,data:o})}function Qi(n){let e=H(n),i=window.getComputedStyle(e).getPropertyValue("--custom-contextmenu").trim();if(i){n.preventDefault();let o=window.getComputedStyle(e).getPropertyValue("--custom-contextmenu-data");Ji(i,n.clientX,n.clientY,o)}else qi(n,e)}function qi(n,e){if(ue())return;switch(window.getComputedStyle(e).getPropertyValue("--default-contextmenu").trim()){case"show":return;case"hide":n.preventDefault();return}if(e.isContentEditable)return;let i=window.getSelection(),o=i&&i.toString().length>0;if(o)for(let t=0;t<i.rangeCount;t++){let a=i.getRangeAt(t).getClientRects();for(let l=0;l<a.length;l++){let c=a[l];if(document.elementFromPoint(c.left,c.top)===e)return}}(e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement)&&(o||!e.readOnly&&!e.disabled)||n.preventDefault()}var we={};m(we,{GetFlag:()=>U});function U(n){try{return window._wails.flags[n]}catch(e){throw new Error("Unable to retrieve flag '"+n+"': "+e,{cause:e})}}var I=!1,z=!1,he=!1,x=!1,A=!1,y="",Xe="auto",T=0,pe=Be();window._wails=window._wails||{};window._wails.setResizable=n=>{he=n,he||(x=A=!1,w())};window.addEventListener("mousedown",We,{capture:!0});window.addEventListener("mousemove",We,{capture:!0});window.addEventListener("mouseup",We,{capture:!0});for(let n of["click","contextmenu","dblclick"])window.addEventListener(n,_i,{capture:!0});function _i(n){(z||A)&&(n.stopImmediatePropagation(),n.stopPropagation(),n.preventDefault())}var fe=0,$i=1,ge=2;function We(n){let e,i=n.buttons;switch(n.type){case"mousedown":e=fe,pe||(i=T|1<<n.button);break;case"mouseup":e=$i,pe||(i=T&~(1<<n.button));break;default:e=ge,pe||(i=T);break}let o=T&~i,t=i&~T;T=i,e===fe&&!(t&n.button)&&(o|=1<<n.button,t|=1<<n.button),(e!==ge&&A||z&&(e===fe||n.button!==0))&&(n.stopImmediatePropagation(),n.stopPropagation(),n.preventDefault()),o&1&&no(n),t&1&&eo(n),e===ge&&oo(n)}function eo(n){if(I=!1,x=!1,!V()&&n.type==="mousedown"&&n.button===0&&n.detail!==1)return;if(y){x=!0;return}let e=H(n),i=window.getComputedStyle(e);I=i.getPropertyValue("--wails-draggable").trim()==="drag"&&n.offsetX-parseFloat(i.paddingLeft)<e.clientWidth&&n.offsetY-parseFloat(i.paddingTop)<e.clientHeight}function no(n){I=!1,z=!1,x=!1,A=!1}var io=Object.freeze({"se-resize":"nwse-resize","sw-resize":"nesw-resize","nw-resize":"nwse-resize","ne-resize":"nesw-resize","w-resize":"ew-resize","n-resize":"ns-resize","s-resize":"ns-resize","e-resize":"ew-resize"});function w(n){n?(y||(Xe=document.body.style.cursor),document.body.style.cursor=io[n]):!n&&y&&(document.body.style.cursor=Xe),y=n||""}function oo(n){if(x&&y?(A=!0,v("wails:resize:"+y)):I&&(z=!0,v("wails:drag")),z||A){I=x=!1;return}if(!he||!V()){y&&w();return}let e=U("system.resizeHandleHeight")||5,i=U("system.resizeHandleWidth")||5,o=U("resizeCornerExtra")||10,t=window.outerWidth-n.clientX<i,r=n.clientX<i,a=n.clientY<e,l=window.outerHeight-n.clientY<e,c=window.outerWidth-n.clientX<i+o,g=n.clientX<i+o,J=n.clientY<e+o,Q=window.outerHeight-n.clientY<e+o;!g&&!J&&!Q&&!c?w():c&&Q?w("se-resize"):g&&Q?w("sw-resize"):g&&J?w("nw-resize"):J&&c?w("ne-resize"):r?w("w-resize"):a?w("n-resize"):l?w("s-resize"):t?w("e-resize"):w()}var ve={};m(ve,{Hide:()=>ao,Quit:()=>co,Show:()=>lo});var be=u(d.Application),to=0,ro=1,so=2;function ao(){return be(to)}function lo(){return be(ro)}function co(){return be(so)}var Te={};m(Te,{ByID:()=>ko,ByName:()=>Eo,Call:()=>Se,RuntimeError:()=>X});var Qe=Function.prototype.toString,R=typeof Reflect=="object"&&Reflect!==null&&Reflect.apply,ye,G;if(typeof R=="function"&&typeof Object.defineProperty=="function")try{ye=Object.defineProperty({},"length",{get:function(){throw G}}),G={},R(function(){throw 42},null,ye)}catch(n){n!==G&&(R=null)}else R=null;var uo=/^\s*class\b/,Pe=function(e){try{var i=Qe.call(e);return uo.test(i)}catch(o){return!1}},Ce=function(e){try{return Pe(e)?!1:(Qe.call(e),!0)}catch(i){return!1}},K=Object.prototype.toString,mo="[object Object]",wo="[object Function]",po="[object GeneratorFunction]",fo="[object HTMLAllCollection]",go="[object HTML document.all class]",ho="[object HTMLCollection]",Wo=typeof Symbol=="function"&&!!Symbol.toStringTag,bo=!(0 in[,]),De=function(){return!1};typeof document=="object"&&(Je=document.all,K.call(Je)===K.call(document.all)&&(De=function(e){if((bo||!e)&&(typeof e>"u"||typeof e=="object"))try{var i=K.call(e);return(i===fo||i===go||i===ho||i===mo)&&e("")==null}catch(o){}return!1}));var Je;function vo(n){if(De(n))return!0;if(!n||typeof n!="function"&&typeof n!="object")return!1;try{R(n,null,ye)}catch(e){if(e!==G)return!1}return!Pe(n)&&Ce(n)}function yo(n){if(De(n))return!0;if(!n||typeof n!="function"&&typeof n!="object")return!1;if(Wo)return Ce(n);if(Pe(n))return!1;var e=K.call(n);return e!==wo&&e!==po&&!/^\[object HTML/.test(e)?!1:Ce(n)}var h=R?vo:yo;var k=class extends Error{constructor(e,i){super(e,i),this.name="CancelError"}},W=class extends Error{constructor(e,i,o){super((o!=null?o:"Unhandled rejection in cancelled promise.")+" Reason: "+Co(i),{cause:i}),this.promise=e,this.name="CancelledRejectionError"}},f=Symbol("barrier"),Me=Symbol("cancelImpl"),en,qe=(en=Symbol.species)!=null?en:Symbol("speciesPolyfill"),j=class n extends Promise{constructor(e,i){let o,t;if(super((c,g)=>{o=c,t=g}),this.constructor[qe]!==Promise)throw new TypeError("CancellablePromise does not support transparent subclassing. Please refrain from overriding the [Symbol.species] static property.");let r={promise:this,resolve:o,reject:t,get oncancelled(){return i!=null?i:null},set oncancelled(c){i=c!=null?c:void 0}},a={get root(){return a},resolving:!1,settled:!1};Object.defineProperties(this,{[f]:{configurable:!1,enumerable:!1,writable:!0,value:null},[Me]:{configurable:!1,enumerable:!1,writable:!1,value:nn(r,a)}});let l=tn(r,a);try{e(on(r,a),l)}catch(c){a.resolving||l(c)}}cancel(e){return new n(i=>{Promise.all([this[Me](new k("Promise cancelled.",{cause:e})),Po(this)]).then(()=>i(),()=>i())})}cancelOn(e){return e.aborted?this.cancel(e.reason):e.addEventListener("abort",()=>void this.cancel(e.reason),{capture:!0}),this}then(e,i,o){if(!(this instanceof n))throw new TypeError("CancellablePromise.prototype.then called on an invalid object.");if(h(e)||(e=_e),h(i)||(i=$e),e===_e&&i==$e)return new n(r=>r(this));let t={};return this[f]=t,new n((r,a)=>{super.then(l=>{var c;this[f]===t&&(this[f]=null),(c=t.resolve)==null||c.call(t);try{r(e(l))}catch(g){a(g)}},l=>{var c;this[f]===t&&(this[f]=null),(c=t.resolve)==null||c.call(t);try{r(i(l))}catch(g){a(g)}})},async r=>{try{return o==null?void 0:o(r)}finally{await this.cancel(r)}})}catch(e,i){return this.then(void 0,e,i)}finally(e,i){if(!(this instanceof n))throw new TypeError("CancellablePromise.prototype.finally called on an invalid object.");return h(e)?this.then(o=>n.resolve(e()).then(()=>o),o=>n.resolve(e()).then(()=>{throw o}),i):this.then(e,e,i)}static get[(f,Me,qe)](){return Promise}static all(e){let i=Array.from(e),o=i.length===0?n.resolve(i):new n((t,r)=>{Promise.all(i).then(t,r)},t=>Y(o,i,t));return o}static allSettled(e){let i=Array.from(e),o=i.length===0?n.resolve(i):new n((t,r)=>{Promise.allSettled(i).then(t,r)},t=>Y(o,i,t));return o}static any(e){let i=Array.from(e),o=i.length===0?n.resolve(i):new n((t,r)=>{Promise.any(i).then(t,r)},t=>Y(o,i,t));return o}static race(e){let i=Array.from(e),o=new n((t,r)=>{Promise.race(i).then(t,r)},t=>Y(o,i,t));return o}static cancel(e){let i=new n(()=>{});return i.cancel(e),i}static timeout(e,i){let o=new n(()=>{});return AbortSignal&&typeof AbortSignal=="function"&&AbortSignal.timeout&&typeof AbortSignal.timeout=="function"?AbortSignal.timeout(e).addEventListener("abort",()=>void o.cancel(i)):setTimeout(()=>void o.cancel(i),e),o}static sleep(e,i){return new n(o=>{setTimeout(()=>o(i),e)})}static reject(e){return new n((i,o)=>o(e))}static resolve(e){return e instanceof n?e:new n(i=>i(e))}static withResolvers(){let e={oncancelled:null};return e.promise=new n((i,o)=>{e.resolve=i,e.reject=o},i=>{var o;(o=e.oncancelled)==null||o.call(e,i)}),e}};function nn(n,e){let i;return o=>{if(e.settled||(e.settled=!0,e.reason=o,n.reject(o),Promise.prototype.then.call(n.promise,void 0,t=>{if(t!==o)throw t})),!(!e.reason||!n.oncancelled))return i=new Promise(t=>{try{t(n.oncancelled(e.reason.cause))}catch(r){Promise.reject(new W(n.promise,r,"Unhandled exception in oncancelled callback."))}}).catch(t=>{Promise.reject(new W(n.promise,t,"Unhandled rejection in oncancelled callback."))}),n.oncancelled=null,i}}function on(n,e){return i=>{if(!e.resolving){if(e.resolving=!0,i===n.promise){if(e.settled)return;e.settled=!0,n.reject(new TypeError("A promise cannot be resolved with itself."));return}if(i!=null&&(typeof i=="object"||typeof i=="function")){let o;try{o=i.then}catch(t){e.settled=!0,n.reject(t);return}if(h(o)){try{let a=i.cancel;if(h(a)){let l=c=>{Reflect.apply(a,i,[c])};e.reason?nn(Ue(Le({},n),{oncancelled:l}),e)(e.reason):n.oncancelled=l}}catch(a){}let t={root:e.root,resolving:!1,get settled(){return this.root.settled},set settled(a){this.root.settled=a},get reason(){return this.root.reason}},r=tn(n,t);try{Reflect.apply(o,i,[on(n,t),r])}catch(a){r(a)}return}}e.settled||(e.settled=!0,n.resolve(i))}}}function tn(n,e){return i=>{if(!e.resolving)if(e.resolving=!0,e.settled){try{if(i instanceof k&&e.reason instanceof k&&Object.is(i.cause,e.reason.cause))return}catch(o){}Promise.reject(new W(n.promise,i))}else e.settled=!0,n.reject(i)}}function Y(n,e,i){let o=[];for(let t of e){let r;try{if(!h(t.then)||(r=t.cancel,!h(r)))continue}catch(l){continue}let a;try{a=Reflect.apply(r,t,[i])}catch(l){Promise.reject(new W(n,l,"Unhandled exception in cancel method."));continue}a&&o.push((a instanceof Promise?a:Promise.resolve(a)).catch(l=>{Promise.reject(new W(n,l,"Unhandled rejection in cancel method."))}))}return Promise.all(o)}function _e(n){return n}function $e(n){throw n}function Co(n){try{if(n instanceof Error||typeof n!="object"||n.toString!==Object.prototype.toString)return""+n}catch(e){}try{return JSON.stringify(n)}catch(e){}try{return Object.prototype.toString.call(n)}catch(e){}return"<could not convert error to string>"}function Po(n){var i;let e=(i=n[f])!=null?i:{};return"promise"in e||Object.assign(e,E()),n[f]==null&&(e.resolve(),n[f]=e),e.promise}var E=Promise.withResolvers;E&&typeof E=="function"?E=E.bind(Promise):E=function(){let n,e;return{promise:new Promise((o,t)=>{n=o,e=t}),resolve:n,reject:e}};window._wails=window._wails||{};window._wails.callResultHandler=xo;window._wails.callErrorHandler=Ao;var Do=u(d.Call),Mo=u(d.CancelCall),O=new Map,So=0,To=0,X=class extends Error{constructor(e,i){super(e,i),this.name="RuntimeError"}};function xo(n,e,i){let o=rn(n);if(o)if(!e)o.resolve(void 0);else if(!i)o.resolve(e);else try{o.resolve(JSON.parse(e))}catch(t){o.reject(new TypeError("could not parse result: "+t.message,{cause:t}))}}function Ao(n,e,i){let o=rn(n);if(o)if(!i)o.reject(new Error(e));else{let t;try{t=JSON.parse(e)}catch(l){o.reject(new TypeError("could not parse error: "+l.message,{cause:l}));return}let r={};t.cause&&(r.cause=t.cause);let a;switch(t.kind){case"ReferenceError":a=new ReferenceError(t.message,r);break;case"TypeError":a=new TypeError(t.message,r);break;case"RuntimeError":a=new X(t.message,r);break;default:a=new Error(t.message,r);break}o.reject(a)}}function rn(n){let e=O.get(n);return O.delete(n),e}function Ro(){let n;do n=P();while(O.has(n));return n}function Se(n){let e=Ro(),i=j.withResolvers();O.set(e,{resolve:i.resolve,reject:i.reject});let o=Do(So,Object.assign({"call-id":e},n)),t=!1;o.then(()=>{t=!0},a=>{O.delete(e),i.reject(a)});let r=()=>(O.delete(e),Mo(To,{"call-id":e}).catch(a=>{}));return i.oncancelled=()=>t?r():o.then(r),i.promise}function Eo(n,...e){return Se({methodName:n,args:e})}function ko(n,...e){return Se({methodID:n,args:e})}var xe={};m(xe,{SetText:()=>Lo,Text:()=>Uo});var sn=u(d.Clipboard),Oo=0,Fo=1;function Lo(n){return sn(Oo,{text:n})}function Uo(){return sn(Fo)}var Ae={};m(Ae,{Any:()=>C,Array:()=>zo,ByteSlice:()=>Io,Map:()=>jo,Nullable:()=>Bo,Struct:()=>Ho});function C(n){return n}function Io(n){return n==null?"":n}function zo(n){return n===C?e=>e===null?[]:e:e=>{if(e===null)return[];for(let i=0;i<e.length;i++)e[i]=n(e[i]);return e}}function jo(n,e){return e===C?i=>i===null?{}:i:i=>{if(i===null)return{};for(let o in i)i[o]=e(i[o]);return i}}function Bo(n){return n===C?C:e=>e===null?null:n(e)}function Ho(n){let e=!0;for(let i in n)if(n[i]!==C){e=!1;break}return e?C:i=>{for(let o in n)o in i&&(i[o]=n[o](i[o]));return i}}var Ee={};m(Ee,{GetAll:()=>Go,GetCurrent:()=>Yo,GetPrimary:()=>Ko});var Re=u(d.Screens),No=0,Zo=1,Vo=2;function Go(){return Re(No)}function Ko(){return Re(Zo)}function Yo(){return Re(Vo)}window._wails=window._wails||{};window._wails.invoke=v;v("wails:runtime:ready");export{ve as Application,_ as Browser,Te as Call,k as CancelError,j as CancellablePromise,W as CancelledRejectionError,xe as Clipboard,Ae as Create,ee as Dialogs,oe as Events,we as Flags,Ee as Screens,me as System,le as WML,Z as Window};
