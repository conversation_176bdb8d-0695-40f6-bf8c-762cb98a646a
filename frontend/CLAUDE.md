# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Primary Development Workflow
```bash
# Start development server (port 3030)
yarn dev

# Build for production
yarn build

# Run all linting and formatting
yarn fix:all

# Individual code quality commands
yarn lint          # Check ESLint issues
yarn lint:fix      # Fix ESLint issues automatically
yarn fm:check      # Check Prettier formatting
yarn fm:fix        # Fix Prettier formatting automatically

# Testing
yarn test          # Run Jest tests

# Clean rebuild (when dependencies change)
yarn re:dev        # Clean install and start dev
yarn re:build      # Clean install and build
```

### Prerequisites
- Node.js >=20 (required)
- Yarn package manager (preferred over npm)

## Project Architecture

### Technology Stack
- **React 19.1.0** with Vite 6.2.3 build system
- **Material-UI v7** for UI components
- **React Router v7** with data router pattern
- **OKX Web3 SDK** for multi-blockchain wallet support
- **Wails Runtime** for desktop application integration
- **i18next** for internationalization (English/Chinese)

### Core Application Structure
```
src/
├── app.jsx                 # Main app component with context providers
├── main.jsx               # Application entry point with router setup
├── global-config.js       # App configuration and blockchain networks
├── components/            # Reusable UI components (50+ folders)
├── layouts/               # Page layout components (auth-split, dashboard, simple)
├── pages/                 # Route page components
├── sections/              # Feature-specific grouped components
├── routes/                # Routing configuration and guards
├── auth/                  # JWT authentication system (currently disabled)
├── theme/                 # Material-UI theme customization
├── locales/               # i18n translations (en/cn)
├── lib/wallet/            # Multi-blockchain wallet implementations
└── bindings/              # Backend service integrations
```

### Key Application Features
1. **Multi-blockchain wallet management** - Supports 15+ networks via OKX Web3 SDK
2. **Account management** - Email, Discord, Telegram, X/Twitter integrations  
3. **Proxy management** - Proxy configuration and routing
4. **Team collaboration** - Multi-user team management
5. **Desktop application** - Built with Wails for native desktop experience

### Configuration Details
- **Development server**: Runs on port 3030 with HMR
- **Path aliases**: Use `src/` prefix for all internal imports
- **Authentication**: JWT-based but disabled via `CONFIG.auth.skip = true`
- **Mock data**: Demo API at `https://api-dev-minimal-[version].vercel.app`
- **Build output**: Optimized for desktop app distribution

### Wallet Architecture
- **Wallet Factory Pattern** in `src/lib/wallet/` for dynamic blockchain support
- **Base wallet class** with standardized interface across all networks
- **Network support**: Ethereum, Solana, Bitcoin, Tron, Sui, Aptos, Cardano, Cosmos, EOS, Near, Starknet, etc.
- **Mnemonic management** with BIP39 validation

### Theme System
- **Settings provider** with persistent user preferences
- **Multiple navigation layouts**: vertical, horizontal, mini drawer
- **Color presets** and dark/light mode support
- **RTL language support** for internationalization

### Development Notes
- **ESLint**: Uses flat config format with React, import sorting, unused imports rules
- **Code formatting**: Prettier with 100 character width, single quotes, semicolons
- **Import organization**: Automatic sorting with eslint-plugin-perfectionist
- **Testing**: Jest configured for Node.js environment
- **Node polyfills**: Required for desktop app compatibility (crypto, buffer, process, etc.)

### Route Protection
- Routes use `AuthGuard` component but authentication is currently bypassed
- Role-based access control available but not actively used
- Error boundaries configured for graceful error handling

### Key Commands for Quality Assurance
Always run before committing changes:
```bash
yarn lint:fix && yarn fm:fix  # Fix all code quality issues
yarn build                    # Ensure production build works
```