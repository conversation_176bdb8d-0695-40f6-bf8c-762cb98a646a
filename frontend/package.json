{"name": "@minimal-kit/vite-js", "author": "Minimals", "version": "7.0.0", "description": "Vite & JavaScript", "private": true, "type": "module", "scripts": {"dev": "vite", "start": "vite preview", "build": "vite build", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "lint:print": "npx eslint --print-config eslint.config.mjs > eslint-show-config.json", "fm:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx}\"", "fm:fix": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "fix:all": "npm run lint:fix && npm run fm:fix", "clean": "rm -rf node_modules .next out dist build", "re:dev": "yarn clean && yarn install && yarn dev", "re:build": "yarn clean && yarn install && yarn build", "re:build-npm": "npm run clean && npm install && npm run build", "test": "jest"}, "jest": {"testMatch": ["**/?(*.)+(spec|test).[jt]s?(x)"], "injectGlobals": true, "transform": {"^.+\\.(js|jsx)$": "babel-jest"}, "testEnvironment": "node"}, "engines": {"node": ">=20"}, "packageManager": "yarn@1.22.22", "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource-variable/dm-sans": "^5.2.5", "@fontsource-variable/inter": "^5.2.5", "@fontsource-variable/nunito-sans": "^5.2.5", "@fontsource-variable/public-sans": "^5.2.5", "@fontsource/barlow": "^5.2.5", "@hookform/resolvers": "^4.1.3", "@iconify/react": "^5.2.0", "@mui/lab": "^7.0.0-beta.10", "@mui/material": "^7.0.1", "@mui/x-data-grid": "^7.28.2", "@mui/x-date-pickers": "^7.28.2", "@mui/x-tree-view": "^7.28.1", "@okxweb3/coin-aptos": "^2.0.2", "@okxweb3/coin-base": "^1.1.3", "@okxweb3/coin-bitcoin": "^1.2.1", "@okxweb3/coin-cardano": "^1.1.0", "@okxweb3/coin-cosmos": "^1.1.0", "@okxweb3/coin-eos": "^1.1.0", "@okxweb3/coin-ethereum": "^1.1.1", "@okxweb3/coin-flow": "^1.1.0", "@okxweb3/coin-kaspa": "^1.1.0", "@okxweb3/coin-near": "^1.1.0", "@okxweb3/coin-solana": "^1.1.0", "@okxweb3/coin-stacks": "^1.1.0", "@okxweb3/coin-starknet": "^1.1.1", "@okxweb3/coin-sui": "^1.1.1", "@okxweb3/coin-ton": "^1.1.1", "@okxweb3/coin-tron": "^1.1.0", "@okxweb3/crypto-lib": "^1.0.11", "@tiptap/core": "^2.11.6", "@tiptap/extension-code-block": "^2.11.6", "@tiptap/extension-code-block-lowlight": "^2.11.6", "@tiptap/extension-image": "^2.11.6", "@tiptap/extension-link": "^2.11.6", "@tiptap/extension-placeholder": "^2.11.6", "@tiptap/extension-text-align": "^2.11.6", "@tiptap/extension-underline": "^2.11.6", "@tiptap/pm": "^2.11.6", "@tiptap/react": "^2.11.6", "@tiptap/starter-kit": "^2.11.6", "@wailsio/runtime": "^3.0.0-alpha.66", "@web3icons/react": "^4.0.12", "apexcharts": "^4.5.0", "assert": "^2.1.0", "autosuggest-highlight": "^3.3.4", "axios": "^1.8.4", "buffer": "^6.0.3", "dayjs": "^1.11.13", "embla-carousel": "^8.5.2", "embla-carousel-auto-height": "^8.5.2", "embla-carousel-auto-scroll": "^8.5.2", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-fade": "^8.5.2", "embla-carousel-react": "^8.5.2", "es-toolkit": "^1.34.1", "events": "^3.3.0", "framer-motion": "^12.6.1", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "i18next-resources-to-backend": "^1.2.1", "lowlight": "^3.3.0", "mapbox-gl": "^3.10.0", "minimal-shared": "^1.0.7", "mui-one-time-password-input": "^4.0.1", "nprogress": "^0.2.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "react": "^19.1.0", "react-apexcharts": "^1.7.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.55.0", "react-i18next": "^15.4.1", "react-map-gl": "^8.0.2", "react-markdown": "^10.1.0", "react-organizational-chart": "^2.2.1", "react-phone-number-input": "^3.4.12", "react-router": "^7.4.1", "react-router-dom": "^7.5.0", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "simplebar-react": "^3.3.0", "sonner": "^2.0.2", "stream-browserify": "^3.0.0", "stylis": "^4.3.6", "stylis-plugin-rtl": "^2.1.1", "swr": "^2.3.3", "turndown": "^7.2.0", "util": "^0.12.5", "xlsx": "^0.18.5", "yet-another-react-lightbox": "^3.21.8", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/plugin-transform-react-jsx": "^7.25.9", "@babel/preset-env": "^7.26.9", "@eslint/js": "^9.23.0", "@jest/globals": "^29.7.0", "@vitejs/plugin-react": "^4.3.4", "babel-jest": "^29.7.0", "crypto-browserify": "^3.12.1", "eslint": "^9.23.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-perfectionist": "^4.10.1", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.5.3", "vite": "^6.2.3", "vite-plugin-checker": "^0.9.1", "vite-plugin-node-polyfills": "^0.23.0"}}