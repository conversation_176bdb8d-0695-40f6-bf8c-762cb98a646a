import path from 'path';
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import checker from 'vite-plugin-checker';
import { nodePolyfills } from 'vite-plugin-node-polyfills';

// ----------------------------------------------------------------------

const PORT = 3030;

export default defineConfig({
  plugins: [
    // 启用 Babel 插件来自动注入 React
    react({
      babel: {
        plugins: [
          [
            '@babel/plugin-transform-react-jsx',
            {
              runtime: 'automatic',
              importSource: 'react',
            },
          ],
        ],
      },
    }),

    // 开启类型检查 + eslint
    checker({
      eslint: {
        useFlatConfig: true,
        lintCommand: 'eslint "./src/**/*.{js,jsx,ts,tsx}"',
        dev: { logLevel: ['error'] },
      },
      overlay: {
        position: 'tl',
        initialIsOpen: false,
      },
    }),

    // 添加 Node 核心模块 polyfill
    nodePolyfills(),
  ],

  resolve: {
    alias: [
      {
        find: /^src(.+)/,
        replacement: path.resolve(process.cwd(), 'src/$1'),
      },
    ],
  },

  optimizeDeps: {
    include: ['react', 'react-dom', 'buffer', 'process'],
  },

  build: {
    commonjsOptions: {
      include: [/node_modules/],
    },
  },

  server: { port: PORT, host: true },
  preview: { port: PORT, host: true },
});
