package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"a8.tools/backend/services/proxy"
)

func main() {
	homeDir, _ := os.UserHomeDir()
	appData := os.Getenv("APPDATA")
	fmt.Println("homeDir:", homeDir)
	fmt.Println("appData:", appData)
}

func TestImportProxy() {
	// vvdnn := http://*************/link/yQRPDvYSo24Xs0Oa?clash=2
	mitce := "https://app.mitce.net/?sid=286184&token=srvxvwpc"
	proxyService := proxy.NewProxyService()
	data, err := proxyService.ImportSubscription(mitce)
	if err != nil {
		log.Fatal(err)
	}
	fmt.Println("ID:", data.ID)
	fmt.Println("Name:", data.Name)
	fmt.Println("Type:", data.Type)
	fmt.Println("Status:", data.Status)
	fmt.Println("Description:", data.Description)
	fmt.Println("CreatedAt:", data.CreatedAt)
	fmt.Println("UpdatedAt:", data.UpdatedAt)
	fmt.Println("UpdatedBy:", data.UpdatedBy)
	fmt.Println("NodeCount:", data.NodeCount)
	fmt.Println("Nodes:", data.Nodes)
}

func generateProfileID(name string) string {
	// Simple implementation - in a real application you might want
	// to use UUID or a more sophisticated approach
	id := ""
	for _, r := range name {
		if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9') {
			id += string(r)
		} else if r == ' ' || r == '-' || r == '_' {
			id += "_"
		}
	}

	if id == "" {
		id = "P"
	}

	// Add timestamp to ensure uniqueness
	timestamp := time.Now().Unix()
	return fmt.Sprintf("%s_%d", id, timestamp)
}
