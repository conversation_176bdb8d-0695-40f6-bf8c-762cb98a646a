{"localesPaths": ["frontend/src/locales/langs"], "keystyle": "nested", "defaultNamespace": "common", "enabledParsers": ["json"], "sourceLanguage": "en", "displayLanguage": "cn", "enabledFrameworks": ["react", "i18next"], "pathMatcher": "{locale}/{namespace}.json", "namespace": true, "sortKeys": true, "keepFulfilled": true, "extract": {"autoDetect": true, "keygenStyle": "camelCase", "targetPickingStrategy": "most-similar", "keyMaxLength": 50, "parsers": {"javascript": {"extractorOptions": {"plugins": ["jsx"]}}}}, "review": {"enabled": true, "removeCommentOnResolved": true}, "annotations": true, "readonly": [], "ignoredLocales": [], "ignoredKeys": [], "usage": {"scanningIgnore": ["node_modules/**", "dist/**", "build/**", "*.min.js"]}, "editor": {"codeActions": true}}