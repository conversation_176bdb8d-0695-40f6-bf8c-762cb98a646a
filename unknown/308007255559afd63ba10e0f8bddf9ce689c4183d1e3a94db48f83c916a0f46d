import React from 'react';
import {
  useSensor,
  DndContext,
  useSensors,
  closestCenter,
  PointerSensor,
  KeyboardSensor,
} from '@dnd-kit/core';
import {
  arrayMove,
  useSortable,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';

import {
  Box,
  List,
  Menu,
  Button,
  Popover,
  Divider,
  ListItem,
  MenuItem,
  Typography,
  IconButton,
  ListItemText,
} from '@mui/material';

import { useTableColumnSettings } from 'src/hooks/use-table-column-settings';

import { useTranslate } from 'src/locales';

import { Iconify } from 'src/components/iconify';

// 可拖拽的列表项组件
function SortableListItem({ column, visibility, onVisibilityChange, canHide }) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: column.id,
    disabled: !canHide,
  });

  // 限制拖拽为仅垂直方向
  const style = {
    transform: transform ? `translate3d(0, ${transform.y}px, 0)` : undefined,
    transition,
  };

  const isVisible = visibility[column.id] !== false;

  // 在开发环境添加调试日志（只在某些特定列输出，避免过多日志）
  if (process.env.NODE_ENV === 'development' && column.id === 'name') {
    console.log(
      `[SortableListItem] ${column.id}: isVisible=${isVisible}, visibility[${column.id}]=${visibility[column.id]}`
    );
  }

  // 处理可见性切换
  const handleVisibilityToggle = (e) => {
    e.preventDefault();
    e.stopPropagation();

    // 在开发环境添加详细调试日志
    if (process.env.NODE_ENV === 'development') {
      console.log(
        `[SortableListItem] 切换列可见性 - columnId: ${column.id}, 当前状态: ${isVisible} -> ${!isVisible}`
      );
      console.log(`[SortableListItem] visibility对象:`, visibility);
      console.log(`[SortableListItem] 将调用 onVisibilityChange:`, !!onVisibilityChange);
      console.log(`[SortableListItem] onVisibilityChange函数:`, onVisibilityChange);
    }

    if (onVisibilityChange) {
      // 立即调用回调函数
      onVisibilityChange(column.id);

      // 添加延迟调试，检查状态是否更新
      if (process.env.NODE_ENV === 'development') {
        setTimeout(() => {
          console.log(
            `[SortableListItem] 延迟检查 - ${column.id} 状态是否已更新:`,
            visibility[column.id]
          );
        }, 100);
      }
    } else {
      console.warn(
        `[SortableListItem] onVisibilityChange 未定义，无法切换列 ${column.id} 的可见性`
      );
    }
  };

  return (
    <ListItem
      ref={setNodeRef}
      style={style}
      sx={{
        opacity: !canHide ? 0.6 : isDragging ? 0.8 : 1,
        cursor: 'default', // 改为默认光标，拖拽手柄有自己的光标
        borderRadius: 1,
        mb: 0.5,
        '&:hover': {
          backgroundColor: 'action.hover',
        },
        ...(isDragging && {
          zIndex: 1000,
          backgroundColor: 'background.paper',
          boxShadow: 3,
          borderRadius: 1,
          border: '1px solid',
          borderColor: 'primary.main',
        }),
      }}
    >
      {/* 排序状态图标和拖拽手柄 */}
      {canHide ? (
        <Box
          className="drag-handle"
          sx={{
            mr: 1.5,
            display: 'flex',
            alignItems: 'center',
            gap: 0.5,
            color: 'text.disabled',
            cursor: 'grab',
            transition: 'color 0.2s ease-in-out',
            p: 0.5,
            borderRadius: 1,
            '&:hover': {
              color: 'primary.main',
              backgroundColor: 'action.hover',
            },
            '&:active': {
              cursor: 'grabbing',
            },
          }}
          {...attributes}
          {...listeners}
        >
          <Iconify icon="jam:align-justify" width={18} height={18} />
        </Box>
      ) : (
        <Box
          sx={{
            mr: 1.5,
            display: 'flex',
            alignItems: 'center',
            gap: 0.5,
            color: 'text.disabled',
            p: 0.5,
          }}
        >
          <Iconify icon="solar:lock-keyhole-bold" width={14} height={14} />
        </Box>
      )}

      <ListItemText
        primary={column.label}
        sx={{
          flex: 1,
          '& .MuiListItemText-primary': {
            fontSize: '0.875rem',
            fontWeight: isVisible ? 600 : 400,
            color: isVisible ? 'text.primary' : 'text.secondary',
            transition: 'all 0.2s ease-in-out',
          },
        }}
      />

      {/* 可见性状态图标 - 可点击切换 */}
      <Box
        sx={{
          cursor: canHide ? 'pointer' : 'default',
          p: 0.5,
          borderRadius: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          '&:hover': canHide
            ? {
                backgroundColor: 'action.hover',
              }
            : {},
        }}
        onClick={canHide ? handleVisibilityToggle : undefined}
      >
        <Iconify
          icon={isVisible ? 'solar:eye-bold' : 'solar:eye-closed-bold'}
          sx={{
            color: isVisible ? 'primary.main' : 'text.disabled',
            width: 18,
            height: 18,
            transition: 'color 0.2s ease-in-out',
          }}
        />
      </Box>
    </ListItem>
  );
}

/**
 * 表格列管理组件
 * @param {string} tableId - 表格唯一标识符（用于持久化）
 * @param {Array} columns - 列定义数组
 * @param {Object} visibility - 列可见性状态对象（可选，如果提供则使用外部状态）
 * @param {function} onVisibilityChange - 列可见性变化回调（可选）
 * @param {function} onColumnReorder - 列重新排序回调（可选）
 * @param {Element} anchorEl - 弹出菜单锚点元素
 * @param {boolean} open - 弹出菜单打开状态
 * @param {function} onClose - 弹出菜单关闭回调
 * @param {function} onOpen - 弹出菜单打开回调（用于触发按钮）
 */
export function TableColumnManager({
  tableId,
  columns,
  visibility: externalVisibility,
  onVisibilityChange: externalOnVisibilityChange,
  onColumnReorder: externalOnColumnReorder,
  anchorEl,
  open,
  onClose,
  onOpen,
}) {
  const { t } = useTranslate('common');

  // 使用持久化Hook（仅在提供tableId时启用）
  const persistentSettings = useTableColumnSettings(tableId, columns);

  // 根据是否提供外部状态决定使用持久化还是外部状态
  const usePersistentState = Boolean(tableId && !externalVisibility);

  // 测试菜单状态
  const [testMenuAnchor, setTestMenuAnchor] = React.useState(null);
  const testMenuOpen = Boolean(testMenuAnchor);

  const handleTestMenuOpen = (event) => {
    setTestMenuAnchor(event.currentTarget);
  };

  const handleTestMenuClose = () => {
    setTestMenuAnchor(null);
  };

  // 在开发环境添加调试日志
  if (process.env.NODE_ENV === 'development') {
    console.log(
      `[TableColumnManager] tableId: ${tableId}, usePersistentState: ${usePersistentState}`
    );
    console.log(`[TableColumnManager] externalVisibility:`, externalVisibility);
    console.log(`[TableColumnManager] persistentSettings:`, persistentSettings);
  }

  // 选择使用的状态和回调函数
  const actualColumns = usePersistentState ? persistentSettings.orderedColumns : columns;
  const visibility = usePersistentState ? persistentSettings.columnVisibility : externalVisibility;
  const onVisibilityChange = usePersistentState
    ? persistentSettings.toggleColumnVisibility
    : externalOnVisibilityChange;
  const onColumnReorder = usePersistentState
    ? persistentSettings.updateColumnOrder
    : externalOnColumnReorder;

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 需要拖拽8px才激活
        tolerance: 5, // 容忍度
        delay: 100, // 延迟100ms激活，避免与点击冲突
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = (event) => {
    // 拖拽开始时的处理
    if (process.env.NODE_ENV === 'development') {
      console.log(`[TableColumnManager] 拖拽开始 - activeId: ${event.active.id}`);
    }
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (process.env.NODE_ENV === 'development') {
      console.log(`[TableColumnManager] 拖拽结束 - activeId: ${active.id}, overId: ${over?.id}`);
    }

    if (active.id !== over?.id) {
      const oldIndex = actualColumns.findIndex((column) => column.id === active.id);
      const newIndex = actualColumns.findIndex((column) => column.id === over.id);

      const newColumns = arrayMove(actualColumns, oldIndex, newIndex);

      // 在开发环境添加调试日志
      if (process.env.NODE_ENV === 'development') {
        console.log(`[TableColumnManager] 拖拽排序 - 从 ${oldIndex} 到 ${newIndex}`);
        console.log(
          `[TableColumnManager] 新的列顺序:`,
          newColumns.map((col) => col.id)
        );
        console.log(`[TableColumnManager] 将调用 onColumnReorder:`, !!onColumnReorder);
      }

      // 调用重新排序回调
      if (onColumnReorder) {
        onColumnReorder(newColumns);
      }
    }
  };

  // 获取可排序的列ID（排除固定列）
  const sortableColumnIds = actualColumns
    .filter((column) => {
      // 判断是否为操作列（id 或 action 列固定不能隐藏）
      const isActionColumn =
        column.id === 'id' || column.id === 'action' || column.id === 'actions';
      // 判断是否允许隐藏（检查 allowHiding 属性，默认为 true）
      const allowHiding = column.allowHiding !== false;
      // 最终判断是否可以隐藏/排序
      return !isActionColumn && allowHiding;
    })
    .map((column) => column.id);

  // 在开发环境添加调试日志
  if (process.env.NODE_ENV === 'development') {
    console.log(`[TableColumnManager] sortableColumnIds:`, sortableColumnIds);
    console.log(
      `[TableColumnManager] actualColumns:`,
      actualColumns.map((col) => col.id)
    );
  }

  return (
    <>
      {/* 列设置按钮 */}
      <IconButton onClick={onOpen} title={t('common:table.columnSettings')} sx={{ mr: 2 }}>
        <Iconify icon="solar:list-bold" />
      </IconButton>

      {/* 列设置弹出菜单 */}
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={onClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            sx: {
              width: 320,
              maxHeight: 480,
              overflow: 'hidden',
            },
          },
        }}
      >
        <Box sx={{ p: 2 }}>
          <Box
            sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}
          >
            <Typography variant="h6">{t('common:table.columnSettings')}</Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              {/* 开发模式下的测试菜单 */}
              {process.env.NODE_ENV === 'development' && (
                <>
                  <Button
                    size="small"
                    variant="outlined"
                    color="info"
                    onClick={handleTestMenuOpen}
                    sx={{
                      minWidth: 'auto',
                      fontSize: '0.75rem',
                      px: 1,
                      py: 0.5,
                    }}
                  >
                    测试 ▼
                  </Button>

                  <Menu
                    open={testMenuOpen}
                    anchorEl={testMenuAnchor}
                    onClose={handleTestMenuClose}
                    anchorOrigin={{
                      vertical: 'bottom',
                      horizontal: 'right',
                    }}
                    transformOrigin={{
                      vertical: 'top',
                      horizontal: 'right',
                    }}
                  >
                    {/* 调试信息 */}
                    <MenuItem
                      onClick={() => {
                        console.log('=== 调试信息 ===');
                        console.log('tableId:', tableId);
                        console.log('usePersistentState:', usePersistentState);
                        console.log('persistentSettings:', persistentSettings);
                        console.log(
                          'localStorage数据:',
                          localStorage.getItem('table-column-settings')
                        );
                        console.log('sortableColumnIds:', sortableColumnIds);
                        console.log(
                          'actualColumns:',
                          actualColumns.map((col) => col.id)
                        );
                        console.log('=== 调试信息结束 ===');
                        handleTestMenuClose();
                      }}
                    >
                      <Iconify icon="solar:bug-bold" sx={{ mr: 1 }} />
                      调试信息
                    </MenuItem>

                    {/* 功能测试 */}
                    <MenuItem
                      onClick={() => {
                        console.log('=== 测试保存功能 ===');
                        if (usePersistentState && persistentSettings.toggleColumnVisibility) {
                          const testColumn = actualColumns.find(
                            (col) =>
                              col.id !== 'id' &&
                              col.id !== 'action' &&
                              col.id !== 'actions' &&
                              col.allowHiding !== false
                          );
                          if (testColumn) {
                            console.log('测试切换列可见性:', testColumn.id);
                            persistentSettings.toggleColumnVisibility(testColumn.id);
                          }
                        }

                        if (usePersistentState && persistentSettings.updateColumnOrder) {
                          const testOrder = [...actualColumns];
                          if (testOrder.length >= 2) {
                            [testOrder[0], testOrder[1]] = [testOrder[1], testOrder[0]];
                            console.log(
                              '测试重新排序:',
                              testOrder.map((col) => col.id)
                            );
                            persistentSettings.updateColumnOrder(testOrder);
                          }
                        }
                        console.log('=== 测试保存功能结束 ===');
                        handleTestMenuClose();
                      }}
                    >
                      <Iconify icon="solar:play-bold" sx={{ mr: 1 }} />
                      功能测试
                    </MenuItem>

                    {/* UI更新测试 */}
                    <MenuItem
                      onClick={() => {
                        console.log('=== 测试UI实时更新 ===');
                        if (usePersistentState && persistentSettings.toggleColumnVisibility) {
                          console.log('切换 name 列可见性...');
                          persistentSettings.toggleColumnVisibility('name');

                          setTimeout(() => {
                            console.log('2秒后切换回来...');
                            persistentSettings.toggleColumnVisibility('name');
                          }, 2000);
                        }
                        console.log('=== 测试UI实时更新结束 ===');
                        handleTestMenuClose();
                      }}
                    >
                      <Iconify icon="solar:refresh-bold" sx={{ mr: 1 }} />
                      UI更新测试
                    </MenuItem>

                    {/* localStorage测试 */}
                    <MenuItem
                      onClick={() => {
                        console.log('=== 直接测试localStorage ===');
                        try {
                          const testData = {
                            test: 'localStorage测试',
                            timestamp: Date.now(),
                            'table-wallet-list': {
                              columnOrder: [
                                'name',
                                'id',
                                'mnemonic',
                                'created_at',
                                'group',
                                'remark',
                                'action',
                              ],
                              columnVisibility: { id: true, name: false, mnemonic: true },
                              version: 1,
                              lastUpdated: Date.now(),
                            },
                          };

                          localStorage.setItem('table-column-settings', JSON.stringify(testData));
                          console.log('✅ localStorage写入成功');

                          const readBack = localStorage.getItem('table-column-settings');
                          console.log('✅ localStorage读取结果:', readBack);
                          console.log('✅ localStorage解析结果:', JSON.parse(readBack));

                          setTimeout(() => window.location.reload(), 1000);
                        } catch (error) {
                          console.error('❌ localStorage测试失败:', error);
                        }
                        console.log('=== 直接测试localStorage结束 ===');
                        handleTestMenuClose();
                      }}
                    >
                      <Iconify icon="solar:database-bold" sx={{ mr: 1 }} />
                      LocalStorage测试
                    </MenuItem>

                    <Divider />

                    {/* 强制触发UI更新 */}
                    <MenuItem
                      onClick={() => {
                        console.log('=== 强制触发UI更新 ===');
                        if (usePersistentState && persistentSettings.updateColumnOrder) {
                          // 获取当前列顺序并重新设置，触发UI更新
                          const currentOrder = persistentSettings.orderedColumns;
                          console.log(
                            '当前列顺序:',
                            currentOrder.map((col) => col.id)
                          );

                          // 重新设置相同的顺序，但这会触发所有的更新机制
                          persistentSettings.updateColumnOrder(currentOrder);

                          console.log('已触发UI更新机制');
                        } else {
                          console.log('无法触发UI更新：缺少持久化设置');
                        }
                        console.log('=== 强制触发UI更新结束 ===');
                        handleTestMenuClose();
                      }}
                    >
                      <Iconify icon="solar:eye-scan-bold" sx={{ mr: 1 }} />
                      强制UI更新
                    </MenuItem>

                    {/* 直接测试实时更新 */}
                    <MenuItem
                      onClick={() => {
                        console.log('=== 直接测试实时更新 ===');
                        if (usePersistentState && persistentSettings.toggleColumnVisibility) {
                          console.log('隐藏 name 列...');
                          persistentSettings.toggleColumnVisibility('name');

                          // 立即检查效果
                          setTimeout(() => {
                            console.log('3秒后恢复 name 列...');
                            persistentSettings.toggleColumnVisibility('name');
                          }, 3000);
                        }
                        console.log('=== 直接测试实时更新结束 ===');
                        handleTestMenuClose();
                      }}
                    >
                      <Iconify icon="solar:smartphone-update-bold" sx={{ mr: 1 }} />
                      直接测试实时更新
                    </MenuItem>

                    <Divider />

                    {/* 强制刷新 */}
                    <MenuItem
                      onClick={() => {
                        console.log('=== 强制刷新 ===');
                        window.location.reload();
                      }}
                    >
                      <Iconify icon="solar:restart-bold" sx={{ mr: 1 }} />
                      强制刷新页面
                    </MenuItem>
                  </Menu>
                </>
              )}

              {usePersistentState && persistentSettings.hasCustomSettings && (
                <Button
                  size="small"
                  variant="outlined"
                  color="inherit"
                  onClick={persistentSettings.resetToDefault}
                  startIcon={<Iconify icon="solar:restart-bold" width={16} />}
                  sx={{
                    minWidth: 'auto',
                    fontSize: '0.75rem',
                    px: 1.5,
                    py: 0.5,
                  }}
                >
                  {t('common:action.reset')}
                </Button>
              )}
            </Box>
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* 列选择列表 */}
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          >
            {/* 修复：SortableContext 的 items 应该包含所有列的 ID，而不仅仅是可拖拽的列 */}
            <SortableContext items={sortableColumnIds} strategy={verticalListSortingStrategy}>
              <List
                sx={{
                  py: 0,
                  maxHeight: 400,
                  overflow: 'auto',
                  overflowX: 'hidden', // 隐藏横向滚动条
                  '&::-webkit-scrollbar': {
                    display: 'none',
                  },
                  scrollbarWidth: 'none', // Firefox
                  msOverflowStyle: 'none', // IE
                }}
              >
                {actualColumns.map((column) => {
                  // 判断是否为操作列（id 或 action 列固定不能隐藏）
                  const isActionColumn =
                    column.id === 'id' || column.id === 'action' || column.id === 'actions';

                  // 判断是否允许隐藏（检查 allowHiding 属性，默认为 true）
                  const allowHiding = column.allowHiding !== false;

                  // 最终判断是否可以隐藏
                  const canHide = !isActionColumn && allowHiding;

                  return (
                    <SortableListItem
                      key={column.id}
                      column={column}
                      visibility={visibility}
                      onVisibilityChange={onVisibilityChange}
                      canHide={canHide}
                    />
                  );
                })}
              </List>
            </SortableContext>
          </DndContext>
        </Box>
      </Popover>
    </>
  );
}
